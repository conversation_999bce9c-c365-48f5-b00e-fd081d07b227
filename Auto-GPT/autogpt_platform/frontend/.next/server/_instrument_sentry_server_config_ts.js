"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_instrument_sentry_server_config_ts";
exports.ids = ["_instrument_sentry_server_config_ts"];
exports.modules = {

/***/ "(instrument)/./sentry.server.config.ts":
/*!*********************************!*\
  !*** ./sentry.server.config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils */ \"(instrument)/./src/lib/utils.ts\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @sentry/nextjs */ \"(instrument)/./node_modules/@sentry/nextjs/build/cjs/index.server.js\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n// This file configures the initialization of Sentry on the server.\n// The config you add here will be used whenever the server handles a request.\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\n\n\n// import { NodeProfilingIntegration } from \"@sentry/profiling-node\";\n_sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__.init({\n    dsn: \"https://<EMAIL>/4507946746380288\",\n    enabled: \"development\" !== \"development\",\n    environment: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_0__.getEnvironmentStr)(),\n    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.\n    tracesSampleRate: 1,\n    tracePropagationTargets: [\n        \"localhost\",\n        \"localhost:8006\",\n        /^https:\\/\\/dev\\-builder\\.agpt\\.co\\/api/,\n        /^https:\\/\\/.*\\.agpt\\.co\\/api/\n    ],\n    // Setting this option to true will print useful information to the console while you're setting up Sentry.\n    debug: false,\n    // Integrations\n    integrations: [\n        _sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__.anrIntegration()\n    ],\n    _experiments: {\n        // Enable logs to be sent to Sentry.\n        enableLogs: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(instrument)/./sentry.server.config.ts\n");

/***/ }),

/***/ "(instrument)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppEnv: () => (/* binding */ AppEnv),\n/* harmony export */   BehaveAs: () => (/* binding */ BehaveAs),\n/* harmony export */   beautifyString: () => (/* binding */ beautifyString),\n/* harmony export */   categoryColorMap: () => (/* binding */ categoryColorMap),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   deepEquals: () => (/* binding */ deepEquals),\n/* harmony export */   exportAsJSONFile: () => (/* binding */ exportAsJSONFile),\n/* harmony export */   filterBlocksByType: () => (/* binding */ filterBlocksByType),\n/* harmony export */   findNewlyAddedBlockCoordinates: () => (/* binding */ findNewlyAddedBlockCoordinates),\n/* harmony export */   getAppEnv: () => (/* binding */ getAppEnv),\n/* harmony export */   getBehaveAs: () => (/* binding */ getBehaveAs),\n/* harmony export */   getEnvironmentStr: () => (/* binding */ getEnvironmentStr),\n/* harmony export */   getPrimaryCategoryColor: () => (/* binding */ getPrimaryCategoryColor),\n/* harmony export */   getTypeBgColor: () => (/* binding */ getTypeBgColor),\n/* harmony export */   getTypeColor: () => (/* binding */ getTypeColor),\n/* harmony export */   getTypeTextColor: () => (/* binding */ getTypeTextColor),\n/* harmony export */   getValue: () => (/* binding */ getValue),\n/* harmony export */   hasNonNullNonObjectValue: () => (/* binding */ hasNonNullNonObjectValue),\n/* harmony export */   hashString: () => (/* binding */ hashString),\n/* harmony export */   isEmptyOrWhitespace: () => (/* binding */ isEmptyOrWhitespace),\n/* harmony export */   parseKeys: () => (/* binding */ parseKeys),\n/* harmony export */   removeEmptyStringsAndNulls: () => (/* binding */ removeEmptyStringsAndNulls),\n/* harmony export */   setNestedProperty: () => (/* binding */ setNestedProperty)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(instrument)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(instrument)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/** Derived from https://stackoverflow.com/a/7616484 */ function hashString(str) {\n    let hash = 0, chr;\n    if (str.length === 0) return hash;\n    for(let i = 0; i < str.length; i++){\n        chr = str.charCodeAt(i);\n        hash = (hash << 5) - hash + chr;\n        hash |= 0; // Convert to 32bit integer\n    }\n    return hash;\n}\n/** Derived from https://stackoverflow.com/a/32922084 */ function deepEquals(x, y) {\n    const ok = (obj)=>Object.keys(obj).filter((key)=>obj[key] !== null), tx = typeof x, ty = typeof y;\n    const res = x && y && tx === ty && tx === \"object\" ? ok(x).length === ok(y).length && ok(x).every((key)=>deepEquals(x[key], y[key])) : x === y;\n    return res;\n}\n/** Get tailwind text color class from type name */ function getTypeTextColor(type) {\n    if (type === null) return \"text-gray-500\";\n    return ({\n        string: \"text-green-500\",\n        number: \"text-blue-500\",\n        integer: \"text-blue-500\",\n        boolean: \"text-yellow-500\",\n        object: \"text-purple-500\",\n        array: \"text-indigo-500\",\n        null: \"text-gray-500\",\n        any: \"text-gray-500\",\n        \"\": \"text-gray-500\"\n    })[type] || \"text-gray-500\";\n}\n/** Get tailwind bg color class from type name */ function getTypeBgColor(type) {\n    if (type === null) return \"border-gray-500\";\n    return ({\n        string: \"border-green-500\",\n        number: \"border-blue-500\",\n        integer: \"border-blue-500\",\n        boolean: \"border-yellow-500\",\n        object: \"border-purple-500\",\n        array: \"border-indigo-500\",\n        null: \"border-gray-500\",\n        any: \"border-gray-500\",\n        \"\": \"border-gray-500\"\n    })[type] || \"border-gray-500\";\n}\nfunction getTypeColor(type) {\n    if (type === null) return \"#6b7280\";\n    return ({\n        string: \"#22c55e\",\n        number: \"#3b82f6\",\n        integer: \"#3b82f6\",\n        boolean: \"#eab308\",\n        object: \"#a855f7\",\n        array: \"#6366f1\",\n        null: \"#6b7280\",\n        any: \"#6b7280\",\n        \"\": \"#6b7280\"\n    })[type] || \"#6b7280\";\n}\nfunction beautifyString(name) {\n    // Regular expression to identify places to split, considering acronyms\n    const result = name.replace(/([a-z])([A-Z])/g, \"$1 $2\") // Add space before capital letters\n    .replace(/([A-Z])([A-Z][a-z])/g, \"$1 $2\") // Add space between acronyms and next word\n    .replace(/_/g, \" \") // Replace underscores with spaces\n    .replace(/\\b\\w/g, (char)=>char.toUpperCase()); // Capitalize the first letter of each word\n    return applyExceptions(result);\n}\nconst exceptionMap = {\n    \"Auto GPT\": \"AutoGPT\",\n    Gpt: \"GPT\",\n    Creds: \"Credentials\",\n    Id: \"ID\",\n    Openai: \"OpenAI\",\n    Api: \"API\",\n    Url: \"URL\",\n    Http: \"HTTP\",\n    Json: \"JSON\",\n    Ai: \"AI\",\n    \"You Tube\": \"YouTube\"\n};\nconst applyExceptions = (str)=>{\n    Object.keys(exceptionMap).forEach((key)=>{\n        const regex = new RegExp(`\\\\b${key}\\\\b`, \"g\");\n        str = str.replace(regex, exceptionMap[key]);\n    });\n    return str;\n};\nfunction exportAsJSONFile(obj, filename) {\n    // Create downloadable blob\n    const jsonString = JSON.stringify(obj, null, 2);\n    const blob = new Blob([\n        jsonString\n    ], {\n        type: \"application/json\"\n    });\n    const url = URL.createObjectURL(blob);\n    // Trigger the browser to download the blob to a file\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // Clean up\n    URL.revokeObjectURL(url);\n}\nfunction setNestedProperty(obj, path, value) {\n    if (!obj || typeof obj !== \"object\") {\n        throw new Error(\"Target must be a non-null object\");\n    }\n    if (!path || typeof path !== \"string\") {\n        throw new Error(\"Path must be a non-empty string\");\n    }\n    const keys = path.split(/[\\/.]/);\n    for (const key of keys){\n        if (!key || key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            throw new Error(`Invalid property name: ${key}`);\n        }\n    }\n    let current = obj;\n    for(let i = 0; i < keys.length - 1; i++){\n        const key = keys[i];\n        if (!current.hasOwnProperty(key)) {\n            current[key] = {};\n        } else if (typeof current[key] !== \"object\" || current[key] === null) {\n            current[key] = {};\n        }\n        current = current[key];\n    }\n    current[keys[keys.length - 1]] = value;\n}\nfunction removeEmptyStringsAndNulls(obj) {\n    if (Array.isArray(obj)) {\n        // If obj is an array, recursively check each element,\n        // but element removal is avoided to prevent index changes.\n        return obj.map((item)=>item === undefined || item === null ? \"\" : removeEmptyStringsAndNulls(item));\n    } else if (typeof obj === \"object\" && obj !== null) {\n        // If obj is an object, recursively remove empty strings and nulls from its properties\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                const value = obj[key];\n                if (value === null || value === undefined || typeof value === \"string\" && value === \"\") {\n                    delete obj[key];\n                } else {\n                    obj[key] = removeEmptyStringsAndNulls(value);\n                }\n            }\n        }\n    }\n    return obj;\n}\nconst categoryColorMap = {\n    AI: \"bg-orange-300 dark:bg-orange-700\",\n    SOCIAL: \"bg-yellow-300 dark:bg-yellow-700\",\n    TEXT: \"bg-green-300 dark:bg-green-700\",\n    SEARCH: \"bg-blue-300 dark:bg-blue-700\",\n    BASIC: \"bg-purple-300 dark:bg-purple-700\",\n    INPUT: \"bg-cyan-300 dark:bg-cyan-700\",\n    OUTPUT: \"bg-red-300 dark:bg-red-700\",\n    LOGIC: \"bg-teal-300 dark:bg-teal-700\",\n    DEVELOPER_TOOLS: \"bg-fuchsia-300 dark:bg-fuchsia-700\",\n    AGENT: \"bg-lime-300 dark:bg-lime-700\"\n};\nfunction getPrimaryCategoryColor(categories) {\n    if (categories.length === 0) {\n        return \"bg-gray-300 dark:bg-slate-700\";\n    }\n    return categoryColorMap[categories[0].category] || \"bg-gray-300 dark:bg-slate-700\";\n}\nfunction filterBlocksByType(blocks, predicate) {\n    return blocks.filter(predicate);\n}\nvar BehaveAs;\n(function(BehaveAs) {\n    BehaveAs[\"CLOUD\"] = \"CLOUD\";\n    BehaveAs[\"LOCAL\"] = \"LOCAL\";\n})(BehaveAs || (BehaveAs = {}));\nfunction getBehaveAs() {\n    return  false ? 0 : \"LOCAL\";\n}\nvar AppEnv;\n(function(AppEnv) {\n    AppEnv[\"LOCAL\"] = \"local\";\n    AppEnv[\"DEV\"] = \"dev\";\n    AppEnv[\"PROD\"] = \"prod\";\n})(AppEnv || (AppEnv = {}));\nfunction getAppEnv() {\n    const env = \"local\";\n    if (env === \"dev\") return \"dev\";\n    if (env === \"prod\") return \"prod\";\n    // Some places use prod and others production\n    if (env === \"production\") return \"prod\";\n    return \"local\";\n}\nfunction getEnvironmentStr() {\n    return `app:${getAppEnv().toLowerCase()}-behave:${getBehaveAs().toLowerCase()}`;\n}\nfunction rectanglesOverlap(rect1, rect2) {\n    const x1 = rect1.x, y1 = rect1.y, w1 = rect1.width, h1 = rect1.height ?? 100;\n    const x2 = rect2.x, y2 = rect2.y, w2 = rect2.width, h2 = rect2.height ?? 100;\n    // Check if the rectangles do not overlap\n    return !(x1 + w1 <= x2 || x1 >= x2 + w2 || y1 + h1 <= y2 || y1 >= y2 + h2);\n}\nfunction findNewlyAddedBlockCoordinates(nodeDimensions, newWidth, margin, zoom) {\n    const nodeDimensionArray = Object.values(nodeDimensions);\n    for(let i = nodeDimensionArray.length - 1; i >= 0; i--){\n        const lastNode = nodeDimensionArray[i];\n        const lastNodeHeight = lastNode.height ?? 100;\n        // Right of the last node\n        let newX = lastNode.x + lastNode.width + margin;\n        let newY = lastNode.y;\n        let newRect = {\n            x: newX,\n            y: newY,\n            width: newWidth,\n            height: 100 / zoom\n        };\n        const collisionRight = nodeDimensionArray.some((node)=>rectanglesOverlap(newRect, node));\n        if (!collisionRight) {\n            return {\n                x: newX,\n                y: newY\n            };\n        }\n        // Left of the last node\n        newX = lastNode.x - newWidth - margin;\n        newRect = {\n            x: newX,\n            y: newY,\n            width: newWidth,\n            height: 100 / zoom\n        };\n        const collisionLeft = nodeDimensionArray.some((node)=>rectanglesOverlap(newRect, node));\n        if (!collisionLeft) {\n            return {\n                x: newX,\n                y: newY\n            };\n        }\n        // Below the last node\n        newX = lastNode.x;\n        newY = lastNode.y + lastNodeHeight + margin;\n        newRect = {\n            x: newX,\n            y: newY,\n            width: newWidth,\n            height: 100 / zoom\n        };\n        const collisionBelow = nodeDimensionArray.some((node)=>rectanglesOverlap(newRect, node));\n        if (!collisionBelow) {\n            return {\n                x: newX,\n                y: newY\n            };\n        }\n    }\n    // Default position if no space is found\n    return {\n        x: 0,\n        y: 0\n    };\n}\nfunction hasNonNullNonObjectValue(obj) {\n    if (obj !== null && typeof obj === \"object\") {\n        return Object.values(obj).some((value)=>hasNonNullNonObjectValue(value));\n    } else {\n        return obj !== null && typeof obj !== \"object\";\n    }\n}\nfunction parseKeys(key) {\n    const splits = key.split(/_@_|_#_|_\\$_|\\./);\n    const keys = [];\n    let currentKey = null;\n    splits.forEach((split)=>{\n        const isInteger = /^\\d+$/.test(split);\n        if (!isInteger) {\n            if (currentKey !== null) {\n                keys.push({\n                    key: currentKey\n                });\n            }\n            currentKey = split;\n        } else {\n            if (currentKey !== null) {\n                keys.push({\n                    key: currentKey,\n                    index: parseInt(split, 10)\n                });\n                currentKey = null;\n            } else {\n                throw new Error(\"Invalid key format: array index without a key\");\n            }\n        }\n    });\n    if (currentKey !== null) {\n        keys.push({\n            key: currentKey\n        });\n    }\n    return keys;\n}\n/**\n * Get the value of a nested key in an object, handles arrays and objects.\n */ function getValue(key, value) {\n    const keys = parseKeys(key);\n    return keys.reduce((acc, k)=>{\n        if (acc === undefined) return undefined;\n        if (k.index !== undefined) {\n            return Array.isArray(acc[k.key]) ? acc[k.key][k.index] : undefined;\n        }\n        return acc[k.key];\n    }, value);\n}\n/** Check if a string is empty or whitespace */ function isEmptyOrWhitespace(str) {\n    return !str || str.trim().length === 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./src/lib/utils.ts\n");

/***/ })

};
;