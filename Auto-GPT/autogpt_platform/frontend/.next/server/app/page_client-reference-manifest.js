globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/global-error.tsx":{"*":{"id":"(ssr)/./src/app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/google-analytics.tsx":{"*":{"id":"(ssr)/./src/components/analytics/google-analytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/OttoChatWidget.tsx":{"*":{"id":"(ssr)/./src/components/OttoChatWidget.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/TallyPopup.tsx":{"*":{"id":"(ssr)/./src/components/TallyPopup.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/BecomeACreator.tsx":{"*":{"id":"(ssr)/./src/components/agptui/BecomeACreator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/composite/AgentsSection.tsx":{"*":{"id":"(ssr)/./src/components/agptui/composite/AgentsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/composite/FeaturedCreators.tsx":{"*":{"id":"(ssr)/./src/components/agptui/composite/FeaturedCreators.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/composite/FeaturedSection.tsx":{"*":{"id":"(ssr)/./src/components/agptui/composite/FeaturedSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/composite/HeroSection.tsx":{"*":{"id":"(ssr)/./src/components/agptui/composite/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/separator.tsx":{"*":{"id":"(ssr)/./src/components/ui/separator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/Button.tsx":{"*":{"id":"(ssr)/./src/components/agptui/Button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/composite/PublishAgentPopout.tsx":{"*":{"id":"(ssr)/./src/components/agptui/composite/PublishAgentPopout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/MobileNavBar.tsx":{"*":{"id":"(ssr)/./src/components/agptui/MobileNavBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/NavbarLink.tsx":{"*":{"id":"(ssr)/./src/components/agptui/NavbarLink.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/ProfilePopoutMenuLogoutButton.tsx":{"*":{"id":"(ssr)/./src/components/agptui/ProfilePopoutMenuLogoutButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/agptui/Wallet.tsx":{"*":{"id":"(ssr)/./src/components/agptui/Wallet.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/avatar.tsx":{"*":{"id":"(ssr)/./src/components/ui/avatar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/popover.tsx":{"*":{"id":"(ssr)/./src/components/ui/popover.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(platform)/login/page.tsx":{"*":{"id":"(ssr)/./src/app/(platform)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(platform)/signup/page.tsx":{"*":{"id":"(ssr)/./src/app/(platform)/signup/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/global-error.tsx":{"id":"(app-pages-browser)/./src/app/global-error.tsx","name":"*","chunks":["app/global-error","static/chunks/app/global-error.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/font/local/target.css?{\"path\":\"node_modules/geist/dist/sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"node_modules/geist/dist/sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/font/local/target.css?{\"path\":\"node_modules/geist/dist/mono.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-mono/GeistMono-Variable.woff2\",\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"],\"weight\":\"100 900\"}],\"variableName\":\"GeistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"node_modules/geist/dist/mono.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-mono/GeistMono-Variable.woff2\",\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"],\"weight\":\"100 900\"}],\"variableName\":\"GeistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/analytics/google-analytics.tsx":{"id":"(app-pages-browser)/./src/components/analytics/google-analytics.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/OttoChatWidget.tsx":{"id":"(app-pages-browser)/./src/components/OttoChatWidget.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/TallyPopup.tsx":{"id":"(app-pages-browser)/./src/components/TallyPopup.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/ui/toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/BecomeACreator.tsx":{"id":"(app-pages-browser)/./src/components/agptui/BecomeACreator.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/composite/AgentsSection.tsx":{"id":"(app-pages-browser)/./src/components/agptui/composite/AgentsSection.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/composite/FeaturedCreators.tsx":{"id":"(app-pages-browser)/./src/components/agptui/composite/FeaturedCreators.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/composite/FeaturedSection.tsx":{"id":"(app-pages-browser)/./src/components/agptui/composite/FeaturedSection.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/composite/HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/agptui/composite/HeroSection.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/ui/separator.tsx":{"id":"(app-pages-browser)/./src/components/ui/separator.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/Button.tsx":{"id":"(app-pages-browser)/./src/components/agptui/Button.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/composite/PublishAgentPopout.tsx":{"id":"(app-pages-browser)/./src/components/agptui/composite/PublishAgentPopout.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/MobileNavBar.tsx":{"id":"(app-pages-browser)/./src/components/agptui/MobileNavBar.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/NavbarLink.tsx":{"id":"(app-pages-browser)/./src/components/agptui/NavbarLink.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/ProfilePopoutMenuLogoutButton.tsx":{"id":"(app-pages-browser)/./src/components/agptui/ProfilePopoutMenuLogoutButton.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/agptui/Wallet.tsx":{"id":"(app-pages-browser)/./src/components/agptui/Wallet.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/ui/avatar.tsx":{"id":"(app-pages-browser)/./src/components/ui/avatar.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/components/ui/popover.tsx":{"id":"(app-pages-browser)/./src/components/ui/popover.tsx","name":"*","chunks":["app/(platform)/layout","static/chunks/app/(platform)/layout.js"],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/(platform)/login/page.tsx":{"id":"(app-pages-browser)/./src/app/(platform)/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/(platform)/signup/page.tsx":{"id":"(app-pages-browser)/./src/app/(platform)/signup/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/":[],"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/global-error":[],"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/page":[],"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/error":[],"/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/frontend/src/app/(platform)/layout":[]}}