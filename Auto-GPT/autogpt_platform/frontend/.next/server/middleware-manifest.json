{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "f15mUSR8QtTGcBHdGNWJW/Wx7R71qhNwVgblN6MKcGY="}}}, "functions": {}, "sortedMiddleware": ["/"]}