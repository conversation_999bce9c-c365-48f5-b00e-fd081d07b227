"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark/dev/lib/constructs.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/constructs.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/** @satisfies {Extension['document']} */\nconst document = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nconst contentInitial = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nconst flowInitial = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nconst flow = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nconst string = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nconst text = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nconst insideSpan = {null: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention, _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nconst attentionMarkers = {null: [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nconst disable = {null: []}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nfunction createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === 'string', 'expected string type')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === 'string', 'expected string type')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, 'expected exit token to match current token')\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, 'expected non-negative end buffer index')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n      } else {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab: {\n          value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space\n\n          break\n        }\n\n        default: {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvY3JlYXRlLXRva2VuaXplci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBLGNBQWMsUUFBUTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsV0FBVztBQUN0QjtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLGFBQWE7QUFDYjtBQUNBOztBQUUrQjtBQUNJO0FBQ3dCO0FBQ1I7QUFDRTtBQUNGOztBQUVuRCxjQUFjLGtDQUFXOztBQUV6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekI7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QjtBQUNBLFdBQVcsb0RBQW9EO0FBQy9EO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDO0FBQ0EsYUFBYSxrQkFBa0I7QUFDL0I7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQSxhQUFhLHFCQUFxQjtBQUNsQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0QsZ0JBQWdCO0FBQ3BFOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsVUFBVSx3REFBSztBQUNmLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsd0RBQUs7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLGFBQWEsMEJBQTBCO0FBQ3ZDO0FBQ0EsYUFBYSw0REFBSTs7QUFFakI7O0FBRUE7QUFDQSxzQ0FBc0Msd0RBQUs7QUFDM0M7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLHFCQUFxQixzRUFBVTs7QUFFL0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsYUFBYSxtQ0FBbUM7QUFDaEQ7QUFDQTtBQUNBOztBQUVBLGFBQWEsZ0NBQWdDO0FBQzdDO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLHdCQUF3QjtBQUNyQztBQUNBLDRFQUE0RTtBQUM1RSxXQUFXLDRDQUE0QztBQUN2RCxZQUFZO0FBQ1o7O0FBRUEsYUFBYSwrQkFBK0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLE1BQU07QUFDbkI7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EsSUFBSSwwQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07QUFDVjtBQUNBOztBQUVBLGFBQWEsb0JBQW9CO0FBQ2pDO0FBQ0EsSUFBSSwwQ0FBTTs7QUFFVjs7QUFFQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxRQUFRLDRFQUFrQjtBQUMxQjtBQUNBO0FBQ0EsK0JBQStCLHdEQUFLO0FBQ3BDO0FBQ0E7QUFDQSxNQUFNLGtCQUFrQix3REFBSztBQUM3QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGFBQWEsa0JBQWtCO0FBQy9CO0FBQ0EsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBOztBQUVBLElBQUksMkNBQU07QUFDVixJQUFJLDJDQUFNO0FBQ1Y7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQSxhQUFhLGlCQUFpQjtBQUM5QjtBQUNBLElBQUksMENBQU07QUFDVixJQUFJLDJDQUFNOztBQUVWO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWOztBQUVBLElBQUksMkNBQU07O0FBRVYsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQSxjQUFjLGlDQUFpQyxhQUFhO0FBQzVEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxnREFBZ0Q7QUFDL0Q7QUFDQSxlQUFlLE9BQU87QUFDdEI7QUFDQSxlQUFlLG1CQUFtQjtBQUNsQztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsMEJBQTBCO0FBQzNDO0FBQ0EsaUJBQWlCLFFBQVE7QUFDekI7QUFDQSxpQkFBaUIsV0FBVztBQUM1QjtBQUNBLGlCQUFpQixNQUFNO0FBQ3ZCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsV0FBVztBQUMxRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsaUJBQWlCO0FBQ2xDO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1CQUFtQixPQUFPO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLDBCQUEwQjtBQUMzQztBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVSwwQ0FBTTtBQUNoQjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFdBQVc7QUFDNUI7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsbUJBQW1CLE9BQU87QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVSwwQ0FBTTtBQUNoQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCLE9BQU87QUFDeEI7QUFDQSxRQUFRLDBDQUFNO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCLE9BQU87QUFDeEI7QUFDQSxRQUFRLDBDQUFNO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLFdBQVc7QUFDeEI7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsTUFBTSw4REFBTTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsSUFBSSwwQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxZQUFZOztBQUVaO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsc0JBQXNCO0FBQ2pDO0FBQ0EsV0FBVyw4QkFBOEI7QUFDekM7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGNBQWM7QUFDM0I7O0FBRUE7QUFDQSxJQUFJLDBDQUFNO0FBQ1YsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1IsUUFBUSwwQ0FBTTtBQUNkO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakM7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsZUFBZTtBQUM1QjtBQUNBLGFBQWEscUJBQXFCO0FBQ2xDOztBQUVBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLGFBQWEsd0RBQUs7QUFDbEIsa0JBQWtCLHlEQUFNOztBQUV4QjtBQUNBOztBQUVBLGFBQWEsd0RBQUs7QUFDbEIsa0JBQWtCLHlEQUFNOztBQUV4QjtBQUNBOztBQUVBLGFBQWEsd0RBQUs7QUFDbEIsa0JBQWtCLHlEQUFNLE1BQU0seURBQU07O0FBRXBDO0FBQ0E7O0FBRUEsYUFBYSx3REFBSztBQUNsQiwrQkFBK0IseURBQU0sU0FBUyx5REFBTTs7QUFFcEQ7QUFDQTs7QUFFQSxhQUFhLHdEQUFLO0FBQ2xCO0FBQ0Esa0JBQWtCLHlEQUFNOztBQUV4QjtBQUNBOztBQUVBO0FBQ0EsVUFBVSwwQ0FBTTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0Isd0RBQUs7QUFDM0I7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvY3JlYXRlLXRva2VuaXplci5qcz8zNmFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7XG4gKiAgIENodW5rLFxuICogICBDb2RlLFxuICogICBDb25zdHJ1Y3RSZWNvcmQsXG4gKiAgIENvbnN0cnVjdCxcbiAqICAgRWZmZWN0cyxcbiAqICAgSW5pdGlhbENvbnN0cnVjdCxcbiAqICAgUGFyc2VDb250ZXh0LFxuICogICBQb2ludCxcbiAqICAgU3RhdGUsXG4gKiAgIFRva2VuaXplQ29udGV4dCxcbiAqICAgVG9rZW5cbiAqIH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgUmVzdG9yZVxuICogICBSZXN0b3JlIHRoZSBzdGF0ZS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKlxuICogQHR5cGVkZWYgSW5mb1xuICogICBJbmZvLlxuICogQHByb3BlcnR5IHtSZXN0b3JlfSByZXN0b3JlXG4gKiAgIFJlc3RvcmUuXG4gKiBAcHJvcGVydHkge251bWJlcn0gZnJvbVxuICogICBGcm9tLlxuICpcbiAqIEBjYWxsYmFjayBSZXR1cm5IYW5kbGVcbiAqICAgSGFuZGxlIGEgc3VjY2Vzc2Z1bCBydW4uXG4gKiBAcGFyYW0ge0NvbnN0cnVjdH0gY29uc3RydWN0XG4gKiAgIENvbnN0cnVjdC5cbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogICBJbmZvLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlRGVidWcgZnJvbSAnZGVidWcnXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAnZGV2bG9wJ1xuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWNoYXJhY3RlcidcbmltcG9ydCB7cHVzaCwgc3BsaWNlfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaHVua2VkJ1xuaW1wb3J0IHtyZXNvbHZlQWxsfSBmcm9tICdtaWNyb21hcmstdXRpbC1yZXNvbHZlLWFsbCdcbmltcG9ydCB7Y29kZXMsIHZhbHVlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuXG5jb25zdCBkZWJ1ZyA9IGNyZWF0ZURlYnVnKCdtaWNyb21hcmsnKVxuXG4vKipcbiAqIENyZWF0ZSBhIHRva2VuaXplci5cbiAqIFRva2VuaXplcnMgZGVhbCB3aXRoIG9uZSB0eXBlIG9mIGRhdGEgKGUuZy4sIGNvbnRhaW5lcnMsIGZsb3csIHRleHQpLlxuICogVGhlIHBhcnNlciBpcyB0aGUgb2JqZWN0IGRlYWxpbmcgd2l0aCBpdCBhbGwuXG4gKiBgaW5pdGlhbGl6ZWAgd29ya3MgbGlrZSBvdGhlciBjb25zdHJ1Y3RzLCBleGNlcHQgdGhhdCBvbmx5IGl0cyBgdG9rZW5pemVgXG4gKiBmdW5jdGlvbiBpcyB1c2VkLCBpbiB3aGljaCBjYXNlIGl0IGRvZXNu4oCZdCByZWNlaXZlIGFuIGBva2Agb3IgYG5va2AuXG4gKiBgZnJvbWAgY2FuIGJlIGdpdmVuIHRvIHNldCB0aGUgcG9pbnQgYmVmb3JlIHRoZSBmaXJzdCBjaGFyYWN0ZXIsIGFsdGhvdWdoXG4gKiB3aGVuIGZ1cnRoZXIgbGluZXMgYXJlIGluZGVudGVkLCB0aGV5IG11c3QgYmUgc2V0IHdpdGggYGRlZmluZVNraXBgLlxuICpcbiAqIEBwYXJhbSB7UGFyc2VDb250ZXh0fSBwYXJzZXJcbiAqICAgUGFyc2VyLlxuICogQHBhcmFtIHtJbml0aWFsQ29uc3RydWN0fSBpbml0aWFsaXplXG4gKiAgIENvbnN0cnVjdC5cbiAqIEBwYXJhbSB7T21pdDxQb2ludCwgJ19idWZmZXJJbmRleCcgfCAnX2luZGV4Jz4gfCB1bmRlZmluZWR9IFtmcm9tXVxuICogICBQb2ludCAob3B0aW9uYWwpLlxuICogQHJldHVybnMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgQ29udGV4dC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVRva2VuaXplcihwYXJzZXIsIGluaXRpYWxpemUsIGZyb20pIHtcbiAgLyoqIEB0eXBlIHtQb2ludH0gKi9cbiAgbGV0IHBvaW50ID0ge1xuICAgIF9idWZmZXJJbmRleDogLTEsXG4gICAgX2luZGV4OiAwLFxuICAgIGxpbmU6IChmcm9tICYmIGZyb20ubGluZSkgfHwgMSxcbiAgICBjb2x1bW46IChmcm9tICYmIGZyb20uY29sdW1uKSB8fCAxLFxuICAgIG9mZnNldDogKGZyb20gJiYgZnJvbS5vZmZzZXQpIHx8IDBcbiAgfVxuICAvKiogQHR5cGUge1JlY29yZDxzdHJpbmcsIG51bWJlcj59ICovXG4gIGNvbnN0IGNvbHVtblN0YXJ0ID0ge31cbiAgLyoqIEB0eXBlIHtBcnJheTxDb25zdHJ1Y3Q+fSAqL1xuICBjb25zdCByZXNvbHZlQWxsQ29uc3RydWN0cyA9IFtdXG4gIC8qKiBAdHlwZSB7QXJyYXk8Q2h1bms+fSAqL1xuICBsZXQgY2h1bmtzID0gW11cbiAgLyoqIEB0eXBlIHtBcnJheTxUb2tlbj59ICovXG4gIGxldCBzdGFjayA9IFtdXG4gIC8qKiBAdHlwZSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IGNvbnN1bWVkID0gdHJ1ZVxuXG4gIC8qKlxuICAgKiBUb29scyB1c2VkIGZvciB0b2tlbml6aW5nLlxuICAgKlxuICAgKiBAdHlwZSB7RWZmZWN0c31cbiAgICovXG4gIGNvbnN0IGVmZmVjdHMgPSB7XG4gICAgYXR0ZW1wdDogY29uc3RydWN0RmFjdG9yeShvbnN1Y2Nlc3NmdWxjb25zdHJ1Y3QpLFxuICAgIGNoZWNrOiBjb25zdHJ1Y3RGYWN0b3J5KG9uc3VjY2Vzc2Z1bGNoZWNrKSxcbiAgICBjb25zdW1lLFxuICAgIGVudGVyLFxuICAgIGV4aXQsXG4gICAgaW50ZXJydXB0OiBjb25zdHJ1Y3RGYWN0b3J5KG9uc3VjY2Vzc2Z1bGNoZWNrLCB7aW50ZXJydXB0OiB0cnVlfSlcbiAgfVxuXG4gIC8qKlxuICAgKiBTdGF0ZSBhbmQgdG9vbHMgZm9yIHJlc29sdmluZyBhbmQgc2VyaWFsaXppbmcuXG4gICAqXG4gICAqIEB0eXBlIHtUb2tlbml6ZUNvbnRleHR9XG4gICAqL1xuICBjb25zdCBjb250ZXh0ID0ge1xuICAgIGNvZGU6IGNvZGVzLmVvZixcbiAgICBjb250YWluZXJTdGF0ZToge30sXG4gICAgZGVmaW5lU2tpcCxcbiAgICBldmVudHM6IFtdLFxuICAgIG5vdyxcbiAgICBwYXJzZXIsXG4gICAgcHJldmlvdXM6IGNvZGVzLmVvZixcbiAgICBzbGljZVNlcmlhbGl6ZSxcbiAgICBzbGljZVN0cmVhbSxcbiAgICB3cml0ZVxuICB9XG5cbiAgLyoqXG4gICAqIFRoZSBzdGF0ZSBmdW5jdGlvbi5cbiAgICpcbiAgICogQHR5cGUge1N0YXRlIHwgdW5kZWZpbmVkfVxuICAgKi9cbiAgbGV0IHN0YXRlID0gaW5pdGlhbGl6ZS50b2tlbml6ZS5jYWxsKGNvbnRleHQsIGVmZmVjdHMpXG5cbiAgLyoqXG4gICAqIFRyYWNrIHdoaWNoIGNoYXJhY3RlciB3ZSBleHBlY3QgdG8gYmUgY29uc3VtZWQsIHRvIGNhdGNoIGJ1Z3MuXG4gICAqXG4gICAqIEB0eXBlIHtDb2RlfVxuICAgKi9cbiAgbGV0IGV4cGVjdGVkQ29kZVxuXG4gIGlmIChpbml0aWFsaXplLnJlc29sdmVBbGwpIHtcbiAgICByZXNvbHZlQWxsQ29uc3RydWN0cy5wdXNoKGluaXRpYWxpemUpXG4gIH1cblxuICByZXR1cm4gY29udGV4dFxuXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0Wyd3cml0ZSddfSAqL1xuICBmdW5jdGlvbiB3cml0ZShzbGljZSkge1xuICAgIGNodW5rcyA9IHB1c2goY2h1bmtzLCBzbGljZSlcblxuICAgIG1haW4oKVxuXG4gICAgLy8gRXhpdCBpZiB3ZeKAmXJlIG5vdCBkb25lLCByZXNvbHZlIG1pZ2h0IGNoYW5nZSBzdHVmZi5cbiAgICBpZiAoY2h1bmtzW2NodW5rcy5sZW5ndGggLSAxXSAhPT0gY29kZXMuZW9mKSB7XG4gICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBhZGRSZXN1bHQoaW5pdGlhbGl6ZSwgMClcblxuICAgIC8vIE90aGVyd2lzZSwgcmVzb2x2ZSwgYW5kIGV4aXQuXG4gICAgY29udGV4dC5ldmVudHMgPSByZXNvbHZlQWxsKHJlc29sdmVBbGxDb25zdHJ1Y3RzLCBjb250ZXh0LmV2ZW50cywgY29udGV4dClcblxuICAgIHJldHVybiBjb250ZXh0LmV2ZW50c1xuICB9XG5cbiAgLy9cbiAgLy8gVG9vbHMuXG4gIC8vXG5cbiAgLyoqIEB0eXBlIHtUb2tlbml6ZUNvbnRleHRbJ3NsaWNlU2VyaWFsaXplJ119ICovXG4gIGZ1bmN0aW9uIHNsaWNlU2VyaWFsaXplKHRva2VuLCBleHBhbmRUYWJzKSB7XG4gICAgcmV0dXJuIHNlcmlhbGl6ZUNodW5rcyhzbGljZVN0cmVhbSh0b2tlbiksIGV4cGFuZFRhYnMpXG4gIH1cblxuICAvKiogQHR5cGUge1Rva2VuaXplQ29udGV4dFsnc2xpY2VTdHJlYW0nXX0gKi9cbiAgZnVuY3Rpb24gc2xpY2VTdHJlYW0odG9rZW4pIHtcbiAgICByZXR1cm4gc2xpY2VDaHVua3MoY2h1bmtzLCB0b2tlbilcbiAgfVxuXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0Wydub3cnXX0gKi9cbiAgZnVuY3Rpb24gbm93KCkge1xuICAgIC8vIFRoaXMgaXMgYSBob3QgcGF0aCwgc28gd2UgY2xvbmUgbWFudWFsbHkgaW5zdGVhZCBvZiBgT2JqZWN0LmFzc2lnbih7fSwgcG9pbnQpYFxuICAgIGNvbnN0IHtfYnVmZmVySW5kZXgsIF9pbmRleCwgbGluZSwgY29sdW1uLCBvZmZzZXR9ID0gcG9pbnRcbiAgICByZXR1cm4ge19idWZmZXJJbmRleCwgX2luZGV4LCBsaW5lLCBjb2x1bW4sIG9mZnNldH1cbiAgfVxuXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0WydkZWZpbmVTa2lwJ119ICovXG4gIGZ1bmN0aW9uIGRlZmluZVNraXAodmFsdWUpIHtcbiAgICBjb2x1bW5TdGFydFt2YWx1ZS5saW5lXSA9IHZhbHVlLmNvbHVtblxuICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICBkZWJ1ZygncG9zaXRpb246IGRlZmluZSBza2lwOiBgJWpgJywgcG9pbnQpXG4gIH1cblxuICAvL1xuICAvLyBTdGF0ZSBtYW5hZ2VtZW50LlxuICAvL1xuXG4gIC8qKlxuICAgKiBNYWluIGxvb3AgKG5vdGUgdGhhdCBgX2luZGV4YCBhbmQgYF9idWZmZXJJbmRleGAgaW4gYHBvaW50YCBhcmUgbW9kaWZpZWQgYnlcbiAgICogYGNvbnN1bWVgKS5cbiAgICogSGVyZSBpcyB3aGVyZSB3ZSB3YWxrIHRocm91Z2ggdGhlIGNodW5rcywgd2hpY2ggZWl0aGVyIGluY2x1ZGUgc3RyaW5ncyBvZlxuICAgKiBzZXZlcmFsIGNoYXJhY3RlcnMsIG9yIG51bWVyaWNhbCBjaGFyYWN0ZXIgY29kZXMuXG4gICAqIFRoZSByZWFzb24gdG8gZG8gdGhpcyBpbiBhIGxvb3AgaW5zdGVhZCBvZiBhIGNhbGwgaXMgc28gdGhlIHN0YWNrIGNhblxuICAgKiBkcmFpbi5cbiAgICpcbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgZnVuY3Rpb24gbWFpbigpIHtcbiAgICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgICBsZXQgY2h1bmtJbmRleFxuXG4gICAgd2hpbGUgKHBvaW50Ll9pbmRleCA8IGNodW5rcy5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IGNodW5rID0gY2h1bmtzW3BvaW50Ll9pbmRleF1cblxuICAgICAgLy8gSWYgd2XigJlyZSBpbiBhIGJ1ZmZlciBjaHVuaywgbG9vcCB0aHJvdWdoIGl0LlxuICAgICAgaWYgKHR5cGVvZiBjaHVuayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgY2h1bmtJbmRleCA9IHBvaW50Ll9pbmRleFxuXG4gICAgICAgIGlmIChwb2ludC5fYnVmZmVySW5kZXggPCAwKSB7XG4gICAgICAgICAgcG9pbnQuX2J1ZmZlckluZGV4ID0gMFxuICAgICAgICB9XG5cbiAgICAgICAgd2hpbGUgKFxuICAgICAgICAgIHBvaW50Ll9pbmRleCA9PT0gY2h1bmtJbmRleCAmJlxuICAgICAgICAgIHBvaW50Ll9idWZmZXJJbmRleCA8IGNodW5rLmxlbmd0aFxuICAgICAgICApIHtcbiAgICAgICAgICBnbyhjaHVuay5jaGFyQ29kZUF0KHBvaW50Ll9idWZmZXJJbmRleCkpXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGdvKGNodW5rKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZWFsIHdpdGggb25lIGNvZGUuXG4gICAqXG4gICAqIEBwYXJhbSB7Q29kZX0gY29kZVxuICAgKiAgIENvZGUuXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIGZ1bmN0aW9uIGdvKGNvZGUpIHtcbiAgICBhc3NlcnQoY29uc3VtZWQgPT09IHRydWUsICdleHBlY3RlZCBjaGFyYWN0ZXIgdG8gYmUgY29uc3VtZWQnKVxuICAgIGNvbnN1bWVkID0gdW5kZWZpbmVkXG4gICAgZGVidWcoJ21haW46IHBhc3NpbmcgYCVzYCB0byAlcycsIGNvZGUsIHN0YXRlICYmIHN0YXRlLm5hbWUpXG4gICAgZXhwZWN0ZWRDb2RlID0gY29kZVxuICAgIGFzc2VydCh0eXBlb2Ygc3RhdGUgPT09ICdmdW5jdGlvbicsICdleHBlY3RlZCBzdGF0ZScpXG4gICAgc3RhdGUgPSBzdGF0ZShjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFZmZlY3RzWydjb25zdW1lJ119ICovXG4gIGZ1bmN0aW9uIGNvbnN1bWUoY29kZSkge1xuICAgIGFzc2VydChjb2RlID09PSBleHBlY3RlZENvZGUsICdleHBlY3RlZCBnaXZlbiBjb2RlIHRvIGVxdWFsIGV4cGVjdGVkIGNvZGUnKVxuXG4gICAgZGVidWcoJ2NvbnN1bWU6IGAlc2AnLCBjb2RlKVxuXG4gICAgYXNzZXJ0KFxuICAgICAgY29uc3VtZWQgPT09IHVuZGVmaW5lZCxcbiAgICAgICdleHBlY3RlZCBjb2RlIHRvIG5vdCBoYXZlIGJlZW4gY29uc3VtZWQ6IHRoaXMgbWlnaHQgYmUgYmVjYXVzZSBgcmV0dXJuIHgoY29kZSlgIGluc3RlYWQgb2YgYHJldHVybiB4YCB3YXMgdXNlZCdcbiAgICApXG4gICAgYXNzZXJ0KFxuICAgICAgY29kZSA9PT0gbnVsbFxuICAgICAgICA/IGNvbnRleHQuZXZlbnRzLmxlbmd0aCA9PT0gMCB8fFxuICAgICAgICAgICAgY29udGV4dC5ldmVudHNbY29udGV4dC5ldmVudHMubGVuZ3RoIC0gMV1bMF0gPT09ICdleGl0J1xuICAgICAgICA6IGNvbnRleHQuZXZlbnRzW2NvbnRleHQuZXZlbnRzLmxlbmd0aCAtIDFdWzBdID09PSAnZW50ZXInLFxuICAgICAgJ2V4cGVjdGVkIGxhc3QgdG9rZW4gdG8gYmUgb3BlbidcbiAgICApXG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICBwb2ludC5saW5lKytcbiAgICAgIHBvaW50LmNvbHVtbiA9IDFcbiAgICAgIHBvaW50Lm9mZnNldCArPSBjb2RlID09PSBjb2Rlcy5jYXJyaWFnZVJldHVybkxpbmVGZWVkID8gMiA6IDFcbiAgICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICAgIGRlYnVnKCdwb3NpdGlvbjogYWZ0ZXIgZW9sOiBgJWpgJywgcG9pbnQpXG4gICAgfSBlbHNlIGlmIChjb2RlICE9PSBjb2Rlcy52aXJ0dWFsU3BhY2UpIHtcbiAgICAgIHBvaW50LmNvbHVtbisrXG4gICAgICBwb2ludC5vZmZzZXQrK1xuICAgIH1cblxuICAgIC8vIE5vdCBpbiBhIHN0cmluZyBjaHVuay5cbiAgICBpZiAocG9pbnQuX2J1ZmZlckluZGV4IDwgMCkge1xuICAgICAgcG9pbnQuX2luZGV4KytcbiAgICB9IGVsc2Uge1xuICAgICAgcG9pbnQuX2J1ZmZlckluZGV4KytcblxuICAgICAgLy8gQXQgZW5kIG9mIHN0cmluZyBjaHVuay5cbiAgICAgIGlmIChcbiAgICAgICAgcG9pbnQuX2J1ZmZlckluZGV4ID09PVxuICAgICAgICAvLyBQb2ludHMgdy8gbm9uLW5lZ2F0aXZlIGBfYnVmZmVySW5kZXhgIHJlZmVyZW5jZVxuICAgICAgICAvLyBzdHJpbmdzLlxuICAgICAgICAvKiogQHR5cGUge3N0cmluZ30gKi8gKGNodW5rc1twb2ludC5faW5kZXhdKS5sZW5ndGhcbiAgICAgICkge1xuICAgICAgICBwb2ludC5fYnVmZmVySW5kZXggPSAtMVxuICAgICAgICBwb2ludC5faW5kZXgrK1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEV4cG9zZSB0aGUgcHJldmlvdXMgY2hhcmFjdGVyLlxuICAgIGNvbnRleHQucHJldmlvdXMgPSBjb2RlXG5cbiAgICAvLyBNYXJrIGFzIGNvbnN1bWVkLlxuICAgIGNvbnN1bWVkID0gdHJ1ZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFZmZlY3RzWydlbnRlciddfSAqL1xuICBmdW5jdGlvbiBlbnRlcih0eXBlLCBmaWVsZHMpIHtcbiAgICAvKiogQHR5cGUge1Rva2VufSAqL1xuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgUGF0Y2ggaW5zdGVhZCBvZiBhc3NpZ24gcmVxdWlyZWQgZmllbGRzIHRvIGhlbHAgR0MuXG4gICAgY29uc3QgdG9rZW4gPSBmaWVsZHMgfHwge31cbiAgICB0b2tlbi50eXBlID0gdHlwZVxuICAgIHRva2VuLnN0YXJ0ID0gbm93KClcblxuICAgIGFzc2VydCh0eXBlb2YgdHlwZSA9PT0gJ3N0cmluZycsICdleHBlY3RlZCBzdHJpbmcgdHlwZScpXG4gICAgYXNzZXJ0KHR5cGUubGVuZ3RoID4gMCwgJ2V4cGVjdGVkIG5vbi1lbXB0eSBzdHJpbmcnKVxuICAgIGRlYnVnKCdlbnRlcjogYCVzYCcsIHR5cGUpXG5cbiAgICBjb250ZXh0LmV2ZW50cy5wdXNoKFsnZW50ZXInLCB0b2tlbiwgY29udGV4dF0pXG5cbiAgICBzdGFjay5wdXNoKHRva2VuKVxuXG4gICAgcmV0dXJuIHRva2VuXG4gIH1cblxuICAvKiogQHR5cGUge0VmZmVjdHNbJ2V4aXQnXX0gKi9cbiAgZnVuY3Rpb24gZXhpdCh0eXBlKSB7XG4gICAgYXNzZXJ0KHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJywgJ2V4cGVjdGVkIHN0cmluZyB0eXBlJylcbiAgICBhc3NlcnQodHlwZS5sZW5ndGggPiAwLCAnZXhwZWN0ZWQgbm9uLWVtcHR5IHN0cmluZycpXG5cbiAgICBjb25zdCB0b2tlbiA9IHN0YWNrLnBvcCgpXG4gICAgYXNzZXJ0KHRva2VuLCAnY2Fubm90IGNsb3NlIHcvbyBvcGVuIHRva2VucycpXG4gICAgdG9rZW4uZW5kID0gbm93KClcblxuICAgIGFzc2VydCh0eXBlID09PSB0b2tlbi50eXBlLCAnZXhwZWN0ZWQgZXhpdCB0b2tlbiB0byBtYXRjaCBjdXJyZW50IHRva2VuJylcblxuICAgIGFzc2VydChcbiAgICAgICEoXG4gICAgICAgIHRva2VuLnN0YXJ0Ll9pbmRleCA9PT0gdG9rZW4uZW5kLl9pbmRleCAmJlxuICAgICAgICB0b2tlbi5zdGFydC5fYnVmZmVySW5kZXggPT09IHRva2VuLmVuZC5fYnVmZmVySW5kZXhcbiAgICAgICksXG4gICAgICAnZXhwZWN0ZWQgbm9uLWVtcHR5IHRva2VuIChgJyArIHR5cGUgKyAnYCknXG4gICAgKVxuXG4gICAgZGVidWcoJ2V4aXQ6IGAlc2AnLCB0b2tlbi50eXBlKVxuICAgIGNvbnRleHQuZXZlbnRzLnB1c2goWydleGl0JywgdG9rZW4sIGNvbnRleHRdKVxuXG4gICAgcmV0dXJuIHRva2VuXG4gIH1cblxuICAvKipcbiAgICogVXNlIHJlc3VsdHMuXG4gICAqXG4gICAqIEB0eXBlIHtSZXR1cm5IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBvbnN1Y2Nlc3NmdWxjb25zdHJ1Y3QoY29uc3RydWN0LCBpbmZvKSB7XG4gICAgYWRkUmVzdWx0KGNvbnN0cnVjdCwgaW5mby5mcm9tKVxuICB9XG5cbiAgLyoqXG4gICAqIERpc2NhcmQgcmVzdWx0cy5cbiAgICpcbiAgICogQHR5cGUge1JldHVybkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uc3VjY2Vzc2Z1bGNoZWNrKF8sIGluZm8pIHtcbiAgICBpbmZvLnJlc3RvcmUoKVxuICB9XG5cbiAgLyoqXG4gICAqIEZhY3RvcnkgdG8gYXR0ZW1wdC9jaGVjay9pbnRlcnJ1cHQuXG4gICAqXG4gICAqIEBwYXJhbSB7UmV0dXJuSGFuZGxlfSBvbnJldHVyblxuICAgKiAgIENhbGxiYWNrLlxuICAgKiBAcGFyYW0ge3tpbnRlcnJ1cHQ/OiBib29sZWFuIHwgdW5kZWZpbmVkfSB8IHVuZGVmaW5lZH0gW2ZpZWxkc11cbiAgICogICBGaWVsZHMuXG4gICAqL1xuICBmdW5jdGlvbiBjb25zdHJ1Y3RGYWN0b3J5KG9ucmV0dXJuLCBmaWVsZHMpIHtcbiAgICByZXR1cm4gaG9va1xuXG4gICAgLyoqXG4gICAgICogSGFuZGxlIGVpdGhlciBhbiBvYmplY3QgbWFwcGluZyBjb2RlcyB0byBjb25zdHJ1Y3RzLCBhIGxpc3Qgb2ZcbiAgICAgKiBjb25zdHJ1Y3RzLCBvciBhIHNpbmdsZSBjb25zdHJ1Y3QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0FycmF5PENvbnN0cnVjdD4gfCBDb25zdHJ1Y3RSZWNvcmQgfCBDb25zdHJ1Y3R9IGNvbnN0cnVjdHNcbiAgICAgKiAgIENvbnN0cnVjdHMuXG4gICAgICogQHBhcmFtIHtTdGF0ZX0gcmV0dXJuU3RhdGVcbiAgICAgKiAgIFN0YXRlLlxuICAgICAqIEBwYXJhbSB7U3RhdGUgfCB1bmRlZmluZWR9IFtib2d1c1N0YXRlXVxuICAgICAqICAgU3RhdGUuXG4gICAgICogQHJldHVybnMge1N0YXRlfVxuICAgICAqICAgU3RhdGUuXG4gICAgICovXG4gICAgZnVuY3Rpb24gaG9vayhjb25zdHJ1Y3RzLCByZXR1cm5TdGF0ZSwgYm9ndXNTdGF0ZSkge1xuICAgICAgLyoqIEB0eXBlIHtSZWFkb25seUFycmF5PENvbnN0cnVjdD59ICovXG4gICAgICBsZXQgbGlzdE9mQ29uc3RydWN0c1xuICAgICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgICBsZXQgY29uc3RydWN0SW5kZXhcbiAgICAgIC8qKiBAdHlwZSB7Q29uc3RydWN0fSAqL1xuICAgICAgbGV0IGN1cnJlbnRDb25zdHJ1Y3RcbiAgICAgIC8qKiBAdHlwZSB7SW5mb30gKi9cbiAgICAgIGxldCBpbmZvXG5cbiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KGNvbnN0cnVjdHMpXG4gICAgICAgID8gLyogYzggaWdub3JlIG5leHQgMSAqL1xuICAgICAgICAgIGhhbmRsZUxpc3RPZkNvbnN0cnVjdHMoY29uc3RydWN0cylcbiAgICAgICAgOiAndG9rZW5pemUnIGluIGNvbnN0cnVjdHNcbiAgICAgICAgICA/IC8vIExvb2tzIGxpa2UgYSBjb25zdHJ1Y3QuXG4gICAgICAgICAgICBoYW5kbGVMaXN0T2ZDb25zdHJ1Y3RzKFsvKiogQHR5cGUge0NvbnN0cnVjdH0gKi8gKGNvbnN0cnVjdHMpXSlcbiAgICAgICAgICA6IGhhbmRsZU1hcE9mQ29uc3RydWN0cyhjb25zdHJ1Y3RzKVxuXG4gICAgICAvKipcbiAgICAgICAqIEhhbmRsZSBhIGxpc3Qgb2YgY29uc3RydWN0LlxuICAgICAgICpcbiAgICAgICAqIEBwYXJhbSB7Q29uc3RydWN0UmVjb3JkfSBtYXBcbiAgICAgICAqICAgQ29uc3RydWN0cy5cbiAgICAgICAqIEByZXR1cm5zIHtTdGF0ZX1cbiAgICAgICAqICAgU3RhdGUuXG4gICAgICAgKi9cbiAgICAgIGZ1bmN0aW9uIGhhbmRsZU1hcE9mQ29uc3RydWN0cyhtYXApIHtcbiAgICAgICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAgICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICAgICAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgICAgICAgIGNvbnN0IGxlZnQgPSBjb2RlICE9PSBudWxsICYmIG1hcFtjb2RlXVxuICAgICAgICAgIGNvbnN0IGFsbCA9IGNvZGUgIT09IG51bGwgJiYgbWFwLm51bGxcbiAgICAgICAgICBjb25zdCBsaXN0ID0gW1xuICAgICAgICAgICAgLy8gVG8gZG86IGFkZCBtb3JlIGV4dGVuc2lvbiB0ZXN0cy5cbiAgICAgICAgICAgIC8qIGM4IGlnbm9yZSBuZXh0IDIgKi9cbiAgICAgICAgICAgIC4uLihBcnJheS5pc0FycmF5KGxlZnQpID8gbGVmdCA6IGxlZnQgPyBbbGVmdF0gOiBbXSksXG4gICAgICAgICAgICAuLi4oQXJyYXkuaXNBcnJheShhbGwpID8gYWxsIDogYWxsID8gW2FsbF0gOiBbXSlcbiAgICAgICAgICBdXG5cbiAgICAgICAgICByZXR1cm4gaGFuZGxlTGlzdE9mQ29uc3RydWN0cyhsaXN0KShjb2RlKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8qKlxuICAgICAgICogSGFuZGxlIGEgbGlzdCBvZiBjb25zdHJ1Y3QuXG4gICAgICAgKlxuICAgICAgICogQHBhcmFtIHtSZWFkb25seUFycmF5PENvbnN0cnVjdD59IGxpc3RcbiAgICAgICAqICAgQ29uc3RydWN0cy5cbiAgICAgICAqIEByZXR1cm5zIHtTdGF0ZX1cbiAgICAgICAqICAgU3RhdGUuXG4gICAgICAgKi9cbiAgICAgIGZ1bmN0aW9uIGhhbmRsZUxpc3RPZkNvbnN0cnVjdHMobGlzdCkge1xuICAgICAgICBsaXN0T2ZDb25zdHJ1Y3RzID0gbGlzdFxuICAgICAgICBjb25zdHJ1Y3RJbmRleCA9IDBcblxuICAgICAgICBpZiAobGlzdC5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICBhc3NlcnQoYm9ndXNTdGF0ZSwgJ2V4cGVjdGVkIGBib2d1c1N0YXRlYCB0byBiZSBnaXZlbicpXG4gICAgICAgICAgcmV0dXJuIGJvZ3VzU3RhdGVcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBoYW5kbGVDb25zdHJ1Y3QobGlzdFtjb25zdHJ1Y3RJbmRleF0pXG4gICAgICB9XG5cbiAgICAgIC8qKlxuICAgICAgICogSGFuZGxlIGEgc2luZ2xlIGNvbnN0cnVjdC5cbiAgICAgICAqXG4gICAgICAgKiBAcGFyYW0ge0NvbnN0cnVjdH0gY29uc3RydWN0XG4gICAgICAgKiAgIENvbnN0cnVjdC5cbiAgICAgICAqIEByZXR1cm5zIHtTdGF0ZX1cbiAgICAgICAqICAgU3RhdGUuXG4gICAgICAgKi9cbiAgICAgIGZ1bmN0aW9uIGhhbmRsZUNvbnN0cnVjdChjb25zdHJ1Y3QpIHtcbiAgICAgICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAgICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICAgICAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgICAgICAgIC8vIFRvIGRvOiBub3QgbmVlZGVkIHRvIHN0b3JlIGlmIHRoZXJlIGlzIG5vIGJvZ3VzIHN0YXRlLCBwcm9iYWJseT9cbiAgICAgICAgICAvLyBDdXJyZW50bHkgZG9lc27igJl0IHdvcmsgYmVjYXVzZSBgaW5zcGVjdGAgaW4gZG9jdW1lbnQgZG9lcyBhIGNoZWNrXG4gICAgICAgICAgLy8gdy9vIGEgYm9ndXMsIHdoaWNoIGRvZXNu4oCZdCBtYWtlIHNlbnNlLiBCdXQgaXQgZG9lcyBzZWVtIHRvIGhlbHAgcGVyZlxuICAgICAgICAgIC8vIGJ5IG5vdCBzdG9yaW5nLlxuICAgICAgICAgIGluZm8gPSBzdG9yZSgpXG4gICAgICAgICAgY3VycmVudENvbnN0cnVjdCA9IGNvbnN0cnVjdFxuXG4gICAgICAgICAgaWYgKCFjb25zdHJ1Y3QucGFydGlhbCkge1xuICAgICAgICAgICAgY29udGV4dC5jdXJyZW50Q29uc3RydWN0ID0gY29uc3RydWN0XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQWx3YXlzIHBvcHVsYXRlZCBieSBkZWZhdWx0cy5cbiAgICAgICAgICBhc3NlcnQoXG4gICAgICAgICAgICBjb250ZXh0LnBhcnNlci5jb25zdHJ1Y3RzLmRpc2FibGUubnVsbCxcbiAgICAgICAgICAgICdleHBlY3RlZCBgZGlzYWJsZS5udWxsYCB0byBiZSBwb3B1bGF0ZWQnXG4gICAgICAgICAgKVxuXG4gICAgICAgICAgaWYgKFxuICAgICAgICAgICAgY29uc3RydWN0Lm5hbWUgJiZcbiAgICAgICAgICAgIGNvbnRleHQucGFyc2VyLmNvbnN0cnVjdHMuZGlzYWJsZS5udWxsLmluY2x1ZGVzKGNvbnN0cnVjdC5uYW1lKVxuICAgICAgICAgICkge1xuICAgICAgICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIHJldHVybiBjb25zdHJ1Y3QudG9rZW5pemUuY2FsbChcbiAgICAgICAgICAgIC8vIElmIHdlIGRvIGhhdmUgZmllbGRzLCBjcmVhdGUgYW4gb2JqZWN0IHcvIGBjb250ZXh0YCBhcyBpdHNcbiAgICAgICAgICAgIC8vIHByb3RvdHlwZS5cbiAgICAgICAgICAgIC8vIFRoaXMgYWxsb3dzIGEg4oCcbGl2ZSBiaW5kaW5n4oCdLCB3aGljaCBpcyBuZWVkZWQgZm9yIGBpbnRlcnJ1cHRgLlxuICAgICAgICAgICAgZmllbGRzID8gT2JqZWN0LmFzc2lnbihPYmplY3QuY3JlYXRlKGNvbnRleHQpLCBmaWVsZHMpIDogY29udGV4dCxcbiAgICAgICAgICAgIGVmZmVjdHMsXG4gICAgICAgICAgICBvayxcbiAgICAgICAgICAgIG5va1xuICAgICAgICAgICkoY29kZSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgICAgZnVuY3Rpb24gb2soY29kZSkge1xuICAgICAgICBhc3NlcnQoY29kZSA9PT0gZXhwZWN0ZWRDb2RlLCAnZXhwZWN0ZWQgY29kZScpXG4gICAgICAgIGNvbnN1bWVkID0gdHJ1ZVxuICAgICAgICBvbnJldHVybihjdXJyZW50Q29uc3RydWN0LCBpbmZvKVxuICAgICAgICByZXR1cm4gcmV0dXJuU3RhdGVcbiAgICAgIH1cblxuICAgICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICAgIGZ1bmN0aW9uIG5vayhjb2RlKSB7XG4gICAgICAgIGFzc2VydChjb2RlID09PSBleHBlY3RlZENvZGUsICdleHBlY3RlZCBjb2RlJylcbiAgICAgICAgY29uc3VtZWQgPSB0cnVlXG4gICAgICAgIGluZm8ucmVzdG9yZSgpXG5cbiAgICAgICAgaWYgKCsrY29uc3RydWN0SW5kZXggPCBsaXN0T2ZDb25zdHJ1Y3RzLmxlbmd0aCkge1xuICAgICAgICAgIHJldHVybiBoYW5kbGVDb25zdHJ1Y3QobGlzdE9mQ29uc3RydWN0c1tjb25zdHJ1Y3RJbmRleF0pXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gYm9ndXNTdGF0ZVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge0NvbnN0cnVjdH0gY29uc3RydWN0XG4gICAqICAgQ29uc3RydWN0LlxuICAgKiBAcGFyYW0ge251bWJlcn0gZnJvbVxuICAgKiAgIEZyb20uXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIGZ1bmN0aW9uIGFkZFJlc3VsdChjb25zdHJ1Y3QsIGZyb20pIHtcbiAgICBpZiAoY29uc3RydWN0LnJlc29sdmVBbGwgJiYgIXJlc29sdmVBbGxDb25zdHJ1Y3RzLmluY2x1ZGVzKGNvbnN0cnVjdCkpIHtcbiAgICAgIHJlc29sdmVBbGxDb25zdHJ1Y3RzLnB1c2goY29uc3RydWN0KVxuICAgIH1cblxuICAgIGlmIChjb25zdHJ1Y3QucmVzb2x2ZSkge1xuICAgICAgc3BsaWNlKFxuICAgICAgICBjb250ZXh0LmV2ZW50cyxcbiAgICAgICAgZnJvbSxcbiAgICAgICAgY29udGV4dC5ldmVudHMubGVuZ3RoIC0gZnJvbSxcbiAgICAgICAgY29uc3RydWN0LnJlc29sdmUoY29udGV4dC5ldmVudHMuc2xpY2UoZnJvbSksIGNvbnRleHQpXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKGNvbnN0cnVjdC5yZXNvbHZlVG8pIHtcbiAgICAgIGNvbnRleHQuZXZlbnRzID0gY29uc3RydWN0LnJlc29sdmVUbyhjb250ZXh0LmV2ZW50cywgY29udGV4dClcbiAgICB9XG5cbiAgICBhc3NlcnQoXG4gICAgICBjb25zdHJ1Y3QucGFydGlhbCB8fFxuICAgICAgICBjb250ZXh0LmV2ZW50cy5sZW5ndGggPT09IDAgfHxcbiAgICAgICAgY29udGV4dC5ldmVudHNbY29udGV4dC5ldmVudHMubGVuZ3RoIC0gMV1bMF0gPT09ICdleGl0JyxcbiAgICAgICdleHBlY3RlZCBsYXN0IHRva2VuIHRvIGVuZCdcbiAgICApXG4gIH1cblxuICAvKipcbiAgICogU3RvcmUgc3RhdGUuXG4gICAqXG4gICAqIEByZXR1cm5zIHtJbmZvfVxuICAgKiAgIEluZm8uXG4gICAqL1xuICBmdW5jdGlvbiBzdG9yZSgpIHtcbiAgICBjb25zdCBzdGFydFBvaW50ID0gbm93KClcbiAgICBjb25zdCBzdGFydFByZXZpb3VzID0gY29udGV4dC5wcmV2aW91c1xuICAgIGNvbnN0IHN0YXJ0Q3VycmVudENvbnN0cnVjdCA9IGNvbnRleHQuY3VycmVudENvbnN0cnVjdFxuICAgIGNvbnN0IHN0YXJ0RXZlbnRzSW5kZXggPSBjb250ZXh0LmV2ZW50cy5sZW5ndGhcbiAgICBjb25zdCBzdGFydFN0YWNrID0gQXJyYXkuZnJvbShzdGFjaylcblxuICAgIHJldHVybiB7ZnJvbTogc3RhcnRFdmVudHNJbmRleCwgcmVzdG9yZX1cblxuICAgIC8qKlxuICAgICAqIFJlc3RvcmUgc3RhdGUuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICAgICAqICAgTm90aGluZy5cbiAgICAgKi9cbiAgICBmdW5jdGlvbiByZXN0b3JlKCkge1xuICAgICAgcG9pbnQgPSBzdGFydFBvaW50XG4gICAgICBjb250ZXh0LnByZXZpb3VzID0gc3RhcnRQcmV2aW91c1xuICAgICAgY29udGV4dC5jdXJyZW50Q29uc3RydWN0ID0gc3RhcnRDdXJyZW50Q29uc3RydWN0XG4gICAgICBjb250ZXh0LmV2ZW50cy5sZW5ndGggPSBzdGFydEV2ZW50c0luZGV4XG4gICAgICBzdGFjayA9IHN0YXJ0U3RhY2tcbiAgICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICAgIGRlYnVnKCdwb3NpdGlvbjogcmVzdG9yZTogYCVqYCcsIHBvaW50KVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBNb3ZlIHRoZSBjdXJyZW50IHBvaW50IGEgYml0IGZvcndhcmQgaW4gdGhlIGxpbmUgd2hlbiBpdOKAmXMgb24gYSBjb2x1bW5cbiAgICogc2tpcC5cbiAgICpcbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgZnVuY3Rpb24gYWNjb3VudEZvclBvdGVudGlhbFNraXAoKSB7XG4gICAgaWYgKHBvaW50LmxpbmUgaW4gY29sdW1uU3RhcnQgJiYgcG9pbnQuY29sdW1uIDwgMikge1xuICAgICAgcG9pbnQuY29sdW1uID0gY29sdW1uU3RhcnRbcG9pbnQubGluZV1cbiAgICAgIHBvaW50Lm9mZnNldCArPSBjb2x1bW5TdGFydFtwb2ludC5saW5lXSAtIDFcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgdGhlIGNodW5rcyBmcm9tIGEgc2xpY2Ugb2YgY2h1bmtzIGluIHRoZSByYW5nZSBvZiBhIHRva2VuLlxuICpcbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxDaHVuaz59IGNodW5rc1xuICogICBDaHVua3MuXG4gKiBAcGFyYW0ge1BpY2s8VG9rZW4sICdlbmQnIHwgJ3N0YXJ0Jz59IHRva2VuXG4gKiAgIFRva2VuLlxuICogQHJldHVybnMge0FycmF5PENodW5rPn1cbiAqICAgQ2h1bmtzLlxuICovXG5mdW5jdGlvbiBzbGljZUNodW5rcyhjaHVua3MsIHRva2VuKSB7XG4gIGNvbnN0IHN0YXJ0SW5kZXggPSB0b2tlbi5zdGFydC5faW5kZXhcbiAgY29uc3Qgc3RhcnRCdWZmZXJJbmRleCA9IHRva2VuLnN0YXJ0Ll9idWZmZXJJbmRleFxuICBjb25zdCBlbmRJbmRleCA9IHRva2VuLmVuZC5faW5kZXhcbiAgY29uc3QgZW5kQnVmZmVySW5kZXggPSB0b2tlbi5lbmQuX2J1ZmZlckluZGV4XG4gIC8qKiBAdHlwZSB7QXJyYXk8Q2h1bms+fSAqL1xuICBsZXQgdmlld1xuXG4gIGlmIChzdGFydEluZGV4ID09PSBlbmRJbmRleCkge1xuICAgIGFzc2VydChlbmRCdWZmZXJJbmRleCA+IC0xLCAnZXhwZWN0ZWQgbm9uLW5lZ2F0aXZlIGVuZCBidWZmZXIgaW5kZXgnKVxuICAgIGFzc2VydChzdGFydEJ1ZmZlckluZGV4ID4gLTEsICdleHBlY3RlZCBub24tbmVnYXRpdmUgc3RhcnQgYnVmZmVyIGluZGV4JylcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIGBfYnVmZmVySW5kZXhgIGlzIHVzZWQgb24gc3RyaW5nIGNodW5rcy5cbiAgICB2aWV3ID0gW2NodW5rc1tzdGFydEluZGV4XS5zbGljZShzdGFydEJ1ZmZlckluZGV4LCBlbmRCdWZmZXJJbmRleCldXG4gIH0gZWxzZSB7XG4gICAgdmlldyA9IGNodW5rcy5zbGljZShzdGFydEluZGV4LCBlbmRJbmRleClcblxuICAgIGlmIChzdGFydEJ1ZmZlckluZGV4ID4gLTEpIHtcbiAgICAgIGNvbnN0IGhlYWQgPSB2aWV3WzBdXG4gICAgICBpZiAodHlwZW9mIGhlYWQgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHZpZXdbMF0gPSBoZWFkLnNsaWNlKHN0YXJ0QnVmZmVySW5kZXgpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhc3NlcnQoc3RhcnRCdWZmZXJJbmRleCA9PT0gMCwgJ2V4cGVjdGVkIGBzdGFydEJ1ZmZlckluZGV4YCB0byBiZSBgMGAnKVxuICAgICAgICB2aWV3LnNoaWZ0KClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoZW5kQnVmZmVySW5kZXggPiAwKSB7XG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIGBfYnVmZmVySW5kZXhgIGlzIHVzZWQgb24gc3RyaW5nIGNodW5rcy5cbiAgICAgIHZpZXcucHVzaChjaHVua3NbZW5kSW5kZXhdLnNsaWNlKDAsIGVuZEJ1ZmZlckluZGV4KSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdmlld1xufVxuXG4vKipcbiAqIEdldCB0aGUgc3RyaW5nIHZhbHVlIG9mIGEgc2xpY2Ugb2YgY2h1bmtzLlxuICpcbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxDaHVuaz59IGNodW5rc1xuICogICBDaHVua3MuXG4gKiBAcGFyYW0ge2Jvb2xlYW4gfCB1bmRlZmluZWR9IFtleHBhbmRUYWJzPWZhbHNlXVxuICogICBXaGV0aGVyIHRvIGV4cGFuZCB0YWJzIChkZWZhdWx0OiBgZmFsc2VgKS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFJlc3VsdC5cbiAqL1xuZnVuY3Rpb24gc2VyaWFsaXplQ2h1bmtzKGNodW5rcywgZXhwYW5kVGFicykge1xuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gIGNvbnN0IHJlc3VsdCA9IFtdXG4gIC8qKiBAdHlwZSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IGF0VGFiXG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaHVua3MubGVuZ3RoKSB7XG4gICAgY29uc3QgY2h1bmsgPSBjaHVua3NbaW5kZXhdXG4gICAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gICAgbGV0IHZhbHVlXG5cbiAgICBpZiAodHlwZW9mIGNodW5rID09PSAnc3RyaW5nJykge1xuICAgICAgdmFsdWUgPSBjaHVua1xuICAgIH0gZWxzZVxuICAgICAgc3dpdGNoIChjaHVuaykge1xuICAgICAgICBjYXNlIGNvZGVzLmNhcnJpYWdlUmV0dXJuOiB7XG4gICAgICAgICAgdmFsdWUgPSB2YWx1ZXMuY3JcblxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cblxuICAgICAgICBjYXNlIGNvZGVzLmxpbmVGZWVkOiB7XG4gICAgICAgICAgdmFsdWUgPSB2YWx1ZXMubGZcblxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cblxuICAgICAgICBjYXNlIGNvZGVzLmNhcnJpYWdlUmV0dXJuTGluZUZlZWQ6IHtcbiAgICAgICAgICB2YWx1ZSA9IHZhbHVlcy5jciArIHZhbHVlcy5sZlxuXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuXG4gICAgICAgIGNhc2UgY29kZXMuaG9yaXpvbnRhbFRhYjoge1xuICAgICAgICAgIHZhbHVlID0gZXhwYW5kVGFicyA/IHZhbHVlcy5zcGFjZSA6IHZhbHVlcy5odFxuXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuXG4gICAgICAgIGNhc2UgY29kZXMudmlydHVhbFNwYWNlOiB7XG4gICAgICAgICAgaWYgKCFleHBhbmRUYWJzICYmIGF0VGFiKSBjb250aW51ZVxuICAgICAgICAgIHZhbHVlID0gdmFsdWVzLnNwYWNlXG5cbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG5cbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgIGFzc2VydCh0eXBlb2YgY2h1bmsgPT09ICdudW1iZXInLCAnZXhwZWN0ZWQgbnVtYmVyJylcbiAgICAgICAgICAvLyBDdXJyZW50bHkgb25seSByZXBsYWNlbWVudCBjaGFyYWN0ZXIuXG4gICAgICAgICAgdmFsdWUgPSBTdHJpbmcuZnJvbUNoYXJDb2RlKGNodW5rKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICBhdFRhYiA9IGNodW5rID09PSBjb2Rlcy5ob3Jpem9udGFsVGFiXG4gICAgcmVzdWx0LnB1c2godmFsdWUpXG4gIH1cblxuICByZXR1cm4gcmVzdWx0LmpvaW4oJycpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/content.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/document.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */\n\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n      _tokenizer: childFlow,\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n      previous: childToken\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n      writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function writeToChild(token, endOfFile) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (endOfFile) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/flow.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)\n        ),\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\n\n\n\nconst resolver = {resolveAll: createResolver()}\nconst string = initializeFactory('string')\nconst text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */\nfunction initializeFactory(field) {\n  return {\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    ),\n    tokenize: initializeText\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */\n    function atBreak(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) &&\n      events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin\n              ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix\n              : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n          start: {\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex,\n            _index: data.start._index + index,\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size\n          },\n          end: {...data.end}\n        }\n\n        data.end = {...token.start}\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVtQztBQUMwQjs7QUFFdEQsa0JBQWtCO0FBQ2xCO0FBQ0E7O0FBRVA7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1o7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBOztBQUVBLGVBQWUsT0FBTztBQUN0QjtBQUNBLG1CQUFtQix3REFBSztBQUN4QjtBQUNBO0FBQ0E7O0FBRUEsb0JBQW9CLHdEQUFLO0FBQ3pCO0FBQ0E7QUFDQTs7QUFFQSxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBLHFCQUFxQix3REFBSztBQUMxQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxNQUFNO0FBQ3JCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix3REFBSztBQUN4QjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVEsMENBQU07O0FBRWQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLHNCQUFzQjtBQUNqQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLFVBQVU7QUFDdkI7QUFDQTtBQUNBLGVBQWUsb0JBQW9CO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELHdEQUFLO0FBQzVEO0FBQ0E7QUFDQTtBQUNBLFFBQVEscURBQXFELHdEQUFLO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLHdEQUFLO0FBQzVDLHlDQUF5Qyx3REFBSztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIscUJBQXFCO0FBQ3RDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSx1REFBdUQsd0RBQUs7QUFDNUQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHdEQUFLO0FBQ2hDO0FBQ0E7QUFDQSxVQUFVLG1CQUFtQix3REFBSztBQUNsQztBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNERBQVM7QUFDNUIsZ0JBQWdCLHdEQUFLO0FBQ3JCLGdCQUFnQix3REFBSztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLGdCQUFnQjtBQUNoQjs7QUFFQSxvQkFBb0I7O0FBRXBCO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS90ZXh0LmpzPzhlZDEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtcbiAqICAgQ29kZSxcbiAqICAgSW5pdGlhbENvbnN0cnVjdCxcbiAqICAgSW5pdGlhbGl6ZXIsXG4gKiAgIFJlc29sdmVyLFxuICogICBTdGF0ZSxcbiAqICAgVG9rZW5pemVDb250ZXh0XG4gKiB9IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICdkZXZsb3AnXG5pbXBvcnQge2NvZGVzLCBjb25zdGFudHMsIHR5cGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wnXG5cbmV4cG9ydCBjb25zdCByZXNvbHZlciA9IHtyZXNvbHZlQWxsOiBjcmVhdGVSZXNvbHZlcigpfVxuZXhwb3J0IGNvbnN0IHN0cmluZyA9IGluaXRpYWxpemVGYWN0b3J5KCdzdHJpbmcnKVxuZXhwb3J0IGNvbnN0IHRleHQgPSBpbml0aWFsaXplRmFjdG9yeSgndGV4dCcpXG5cbi8qKlxuICogQHBhcmFtIHsnc3RyaW5nJyB8ICd0ZXh0J30gZmllbGRcbiAqICAgRmllbGQuXG4gKiBAcmV0dXJucyB7SW5pdGlhbENvbnN0cnVjdH1cbiAqICAgQ29uc3RydWN0LlxuICovXG5mdW5jdGlvbiBpbml0aWFsaXplRmFjdG9yeShmaWVsZCkge1xuICByZXR1cm4ge1xuICAgIHJlc29sdmVBbGw6IGNyZWF0ZVJlc29sdmVyKFxuICAgICAgZmllbGQgPT09ICd0ZXh0JyA/IHJlc29sdmVBbGxMaW5lU3VmZml4ZXMgOiB1bmRlZmluZWRcbiAgICApLFxuICAgIHRva2VuaXplOiBpbml0aWFsaXplVGV4dFxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gICAqICAgQ29udGV4dC5cbiAgICogQHR5cGUge0luaXRpYWxpemVyfVxuICAgKi9cbiAgZnVuY3Rpb24gaW5pdGlhbGl6ZVRleHQoZWZmZWN0cykge1xuICAgIGNvbnN0IHNlbGYgPSB0aGlzXG4gICAgY29uc3QgY29uc3RydWN0cyA9IHRoaXMucGFyc2VyLmNvbnN0cnVjdHNbZmllbGRdXG4gICAgY29uc3QgdGV4dCA9IGVmZmVjdHMuYXR0ZW1wdChjb25zdHJ1Y3RzLCBzdGFydCwgbm90VGV4dClcblxuICAgIHJldHVybiBzdGFydFxuXG4gICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICBmdW5jdGlvbiBzdGFydChjb2RlKSB7XG4gICAgICByZXR1cm4gYXRCcmVhayhjb2RlKSA/IHRleHQoY29kZSkgOiBub3RUZXh0KGNvZGUpXG4gICAgfVxuXG4gICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICBmdW5jdGlvbiBub3RUZXh0KGNvZGUpIHtcbiAgICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBlZmZlY3RzLmVudGVyKHR5cGVzLmRhdGEpXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBkYXRhXG4gICAgfVxuXG4gICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICBmdW5jdGlvbiBkYXRhKGNvZGUpIHtcbiAgICAgIGlmIChhdEJyZWFrKGNvZGUpKSB7XG4gICAgICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5kYXRhKVxuICAgICAgICByZXR1cm4gdGV4dChjb2RlKVxuICAgICAgfVxuXG4gICAgICAvLyBEYXRhLlxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gZGF0YVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7Q29kZX0gY29kZVxuICAgICAqICAgQ29kZS5cbiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICAgKiAgIFdoZXRoZXIgdGhlIGNvZGUgaXMgYSBicmVhay5cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBhdEJyZWFrKGNvZGUpIHtcbiAgICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgIH1cblxuICAgICAgY29uc3QgbGlzdCA9IGNvbnN0cnVjdHNbY29kZV1cbiAgICAgIGxldCBpbmRleCA9IC0xXG5cbiAgICAgIGlmIChsaXN0KSB7XG4gICAgICAgIC8vIEFsd2F5cyBwb3B1bGF0ZWQgYnkgZGVmYXVsdHMuXG4gICAgICAgIGFzc2VydChBcnJheS5pc0FycmF5KGxpc3QpLCAnZXhwZWN0ZWQgYGRpc2FibGUubnVsbGAgdG8gYmUgcG9wdWxhdGVkJylcblxuICAgICAgICB3aGlsZSAoKytpbmRleCA8IGxpc3QubGVuZ3RoKSB7XG4gICAgICAgICAgY29uc3QgaXRlbSA9IGxpc3RbaW5kZXhdXG4gICAgICAgICAgaWYgKCFpdGVtLnByZXZpb3VzIHx8IGl0ZW0ucHJldmlvdXMuY2FsbChzZWxmLCBzZWxmLnByZXZpb3VzKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQHBhcmFtIHtSZXNvbHZlciB8IHVuZGVmaW5lZH0gW2V4dHJhUmVzb2x2ZXJdXG4gKiAgIFJlc29sdmVyLlxuICogQHJldHVybnMge1Jlc29sdmVyfVxuICogICBSZXNvbHZlci5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlUmVzb2x2ZXIoZXh0cmFSZXNvbHZlcikge1xuICByZXR1cm4gcmVzb2x2ZUFsbFRleHRcblxuICAvKiogQHR5cGUge1Jlc29sdmVyfSAqL1xuICBmdW5jdGlvbiByZXNvbHZlQWxsVGV4dChldmVudHMsIGNvbnRleHQpIHtcbiAgICBsZXQgaW5kZXggPSAtMVxuICAgIC8qKiBAdHlwZSB7bnVtYmVyIHwgdW5kZWZpbmVkfSAqL1xuICAgIGxldCBlbnRlclxuXG4gICAgLy8gQSByYXRoZXIgYm9yaW5nIGNvbXB1dGF0aW9uICh0byBtZXJnZSBhZGphY2VudCBgZGF0YWAgZXZlbnRzKSB3aGljaFxuICAgIC8vIGltcHJvdmVzIG1tIHBlcmZvcm1hbmNlIGJ5IDI5JS5cbiAgICB3aGlsZSAoKytpbmRleCA8PSBldmVudHMubGVuZ3RoKSB7XG4gICAgICBpZiAoZW50ZXIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICBpZiAoZXZlbnRzW2luZGV4XSAmJiBldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09IHR5cGVzLmRhdGEpIHtcbiAgICAgICAgICBlbnRlciA9IGluZGV4XG4gICAgICAgICAgaW5kZXgrK1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKCFldmVudHNbaW5kZXhdIHx8IGV2ZW50c1tpbmRleF1bMV0udHlwZSAhPT0gdHlwZXMuZGF0YSkge1xuICAgICAgICAvLyBEb27igJl0IGRvIGFueXRoaW5nIGlmIHRoZXJlIGlzIG9uZSBkYXRhIHRva2VuLlxuICAgICAgICBpZiAoaW5kZXggIT09IGVudGVyICsgMikge1xuICAgICAgICAgIGV2ZW50c1tlbnRlcl1bMV0uZW5kID0gZXZlbnRzW2luZGV4IC0gMV1bMV0uZW5kXG4gICAgICAgICAgZXZlbnRzLnNwbGljZShlbnRlciArIDIsIGluZGV4IC0gZW50ZXIgLSAyKVxuICAgICAgICAgIGluZGV4ID0gZW50ZXIgKyAyXG4gICAgICAgIH1cblxuICAgICAgICBlbnRlciA9IHVuZGVmaW5lZFxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBleHRyYVJlc29sdmVyID8gZXh0cmFSZXNvbHZlcihldmVudHMsIGNvbnRleHQpIDogZXZlbnRzXG4gIH1cbn1cblxuLyoqXG4gKiBBIHJhdGhlciB1Z2x5IHNldCBvZiBpbnN0cnVjdGlvbnMgd2hpY2ggYWdhaW4gbG9va3MgYXQgY2h1bmtzIGluIHRoZSBpbnB1dFxuICogc3RyZWFtLlxuICogVGhlIHJlYXNvbiB0byBkbyB0aGlzIGhlcmUgaXMgdGhhdCBpdCBpcyAqbXVjaCogZmFzdGVyIHRvIHBhcnNlIGluIHJldmVyc2UuXG4gKiBBbmQgdGhhdCB3ZSBjYW7igJl0IGhvb2sgaW50byBgbnVsbGAgdG8gc3BsaXQgdGhlIGxpbmUgc3VmZml4IGJlZm9yZSBhbiBFT0YuXG4gKiBUbyBkbzogZmlndXJlIG91dCBpZiB3ZSBjYW4gbWFrZSB0aGlzIGludG8gYSBjbGVhbiB1dGlsaXR5LCBvciBldmVuIGluIGNvcmUuXG4gKiBBcyBpdCB3aWxsIGJlIHVzZWZ1bCBmb3IgR0ZNcyBsaXRlcmFsIGF1dG9saW5rIGV4dGVuc2lvbiAoYW5kIG1heWJlIGV2ZW5cbiAqIHRhYmxlcz8pXG4gKlxuICogQHR5cGUge1Jlc29sdmVyfVxuICovXG5mdW5jdGlvbiByZXNvbHZlQWxsTGluZVN1ZmZpeGVzKGV2ZW50cywgY29udGV4dCkge1xuICBsZXQgZXZlbnRJbmRleCA9IDAgLy8gU2tpcCBmaXJzdC5cblxuICB3aGlsZSAoKytldmVudEluZGV4IDw9IGV2ZW50cy5sZW5ndGgpIHtcbiAgICBpZiAoXG4gICAgICAoZXZlbnRJbmRleCA9PT0gZXZlbnRzLmxlbmd0aCB8fFxuICAgICAgICBldmVudHNbZXZlbnRJbmRleF1bMV0udHlwZSA9PT0gdHlwZXMubGluZUVuZGluZykgJiZcbiAgICAgIGV2ZW50c1tldmVudEluZGV4IC0gMV1bMV0udHlwZSA9PT0gdHlwZXMuZGF0YVxuICAgICkge1xuICAgICAgY29uc3QgZGF0YSA9IGV2ZW50c1tldmVudEluZGV4IC0gMV1bMV1cbiAgICAgIGNvbnN0IGNodW5rcyA9IGNvbnRleHQuc2xpY2VTdHJlYW0oZGF0YSlcbiAgICAgIGxldCBpbmRleCA9IGNodW5rcy5sZW5ndGhcbiAgICAgIGxldCBidWZmZXJJbmRleCA9IC0xXG4gICAgICBsZXQgc2l6ZSA9IDBcbiAgICAgIC8qKiBAdHlwZSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gKi9cbiAgICAgIGxldCB0YWJzXG5cbiAgICAgIHdoaWxlIChpbmRleC0tKSB7XG4gICAgICAgIGNvbnN0IGNodW5rID0gY2h1bmtzW2luZGV4XVxuXG4gICAgICAgIGlmICh0eXBlb2YgY2h1bmsgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgYnVmZmVySW5kZXggPSBjaHVuay5sZW5ndGhcblxuICAgICAgICAgIHdoaWxlIChjaHVuay5jaGFyQ29kZUF0KGJ1ZmZlckluZGV4IC0gMSkgPT09IGNvZGVzLnNwYWNlKSB7XG4gICAgICAgICAgICBzaXplKytcbiAgICAgICAgICAgIGJ1ZmZlckluZGV4LS1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoYnVmZmVySW5kZXgpIGJyZWFrXG4gICAgICAgICAgYnVmZmVySW5kZXggPSAtMVxuICAgICAgICB9XG4gICAgICAgIC8vIE51bWJlclxuICAgICAgICBlbHNlIGlmIChjaHVuayA9PT0gY29kZXMuaG9yaXpvbnRhbFRhYikge1xuICAgICAgICAgIHRhYnMgPSB0cnVlXG4gICAgICAgICAgc2l6ZSsrXG4gICAgICAgIH0gZWxzZSBpZiAoY2h1bmsgPT09IGNvZGVzLnZpcnR1YWxTcGFjZSkge1xuICAgICAgICAgIC8vIEVtcHR5XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gUmVwbGFjZW1lbnQgY2hhcmFjdGVyLCBleGl0LlxuICAgICAgICAgIGluZGV4KytcbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChzaXplKSB7XG4gICAgICAgIGNvbnN0IHRva2VuID0ge1xuICAgICAgICAgIHR5cGU6XG4gICAgICAgICAgICBldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoIHx8XG4gICAgICAgICAgICB0YWJzIHx8XG4gICAgICAgICAgICBzaXplIDwgY29uc3RhbnRzLmhhcmRCcmVha1ByZWZpeFNpemVNaW5cbiAgICAgICAgICAgICAgPyB0eXBlcy5saW5lU3VmZml4XG4gICAgICAgICAgICAgIDogdHlwZXMuaGFyZEJyZWFrVHJhaWxpbmcsXG4gICAgICAgICAgc3RhcnQ6IHtcbiAgICAgICAgICAgIF9idWZmZXJJbmRleDogaW5kZXhcbiAgICAgICAgICAgICAgPyBidWZmZXJJbmRleFxuICAgICAgICAgICAgICA6IGRhdGEuc3RhcnQuX2J1ZmZlckluZGV4ICsgYnVmZmVySW5kZXgsXG4gICAgICAgICAgICBfaW5kZXg6IGRhdGEuc3RhcnQuX2luZGV4ICsgaW5kZXgsXG4gICAgICAgICAgICBsaW5lOiBkYXRhLmVuZC5saW5lLFxuICAgICAgICAgICAgY29sdW1uOiBkYXRhLmVuZC5jb2x1bW4gLSBzaXplLFxuICAgICAgICAgICAgb2Zmc2V0OiBkYXRhLmVuZC5vZmZzZXQgLSBzaXplXG4gICAgICAgICAgfSxcbiAgICAgICAgICBlbmQ6IHsuLi5kYXRhLmVuZH1cbiAgICAgICAgfVxuXG4gICAgICAgIGRhdGEuZW5kID0gey4uLnRva2VuLnN0YXJ0fVxuXG4gICAgICAgIGlmIChkYXRhLnN0YXJ0Lm9mZnNldCA9PT0gZGF0YS5lbmQub2Zmc2V0KSB7XG4gICAgICAgICAgT2JqZWN0LmFzc2lnbihkYXRhLCB0b2tlbilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBldmVudHMuc3BsaWNlKFxuICAgICAgICAgICAgZXZlbnRJbmRleCxcbiAgICAgICAgICAgIDAsXG4gICAgICAgICAgICBbJ2VudGVyJywgdG9rZW4sIGNvbnRleHRdLFxuICAgICAgICAgICAgWydleGl0JywgdG9rZW4sIGNvbnRleHRdXG4gICAgICAgICAgKVxuICAgICAgICAgIGV2ZW50SW5kZXggKz0gMlxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGV2ZW50SW5kZXgrK1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/micromark/dev/lib/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(ssr)/./node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(ssr)/./node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nfunction parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([_constructs_js__WEBPACK_IMPORTED_MODULE_1__, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n    defined: [],\n    document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n    flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n    lazy: {},\n    string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n    text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/postprocess.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/postprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nfunction postprocess(events) {\n  while (!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)) {\n    // Empty\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQjs7QUFFc0Q7O0FBRXREO0FBQ0EsV0FBVyxjQUFjO0FBQ3pCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLFVBQVUsdUVBQVc7QUFDckI7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanM/YzRhOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0V2ZW50fSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge3N1YnRva2VuaXplfSBmcm9tICdtaWNyb21hcmstdXRpbC1zdWJ0b2tlbml6ZSdcblxuLyoqXG4gKiBAcGFyYW0ge0FycmF5PEV2ZW50Pn0gZXZlbnRzXG4gKiAgIEV2ZW50cy5cbiAqIEByZXR1cm5zIHtBcnJheTxFdmVudD59XG4gKiAgIEV2ZW50cy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBvc3Rwcm9jZXNzKGV2ZW50cykge1xuICB3aGlsZSAoIXN1YnRva2VuaXplKGV2ZW50cykpIHtcbiAgICAvLyBFbXB0eVxuICB9XG5cbiAgcmV0dXJuIGV2ZW50c1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/preprocess.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/preprocess.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\n\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nfunction preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul: {\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht: {\n            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab)\n            while (column++ < next) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace)\n\n            break\n          }\n\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf: {\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof)\n    }\n\n    return chunks\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;