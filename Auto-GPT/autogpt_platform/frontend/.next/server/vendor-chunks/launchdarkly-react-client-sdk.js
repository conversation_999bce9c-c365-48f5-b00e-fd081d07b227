"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/launchdarkly-react-client-sdk";
exports.ids = ["vendor-chunks/launchdarkly-react-client-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/launchdarkly-react-client-sdk/lib/esm/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/launchdarkly-react-client-sdk/lib/esm/index.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LDProvider: () => (/* binding */ D),\n/* harmony export */   asyncWithLDProvider: () => (/* binding */ W),\n/* harmony export */   basicLogger: () => (/* reexport safe */ launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__.basicLogger),\n/* harmony export */   camelCaseKeys: () => (/* binding */ d),\n/* harmony export */   createConsoleLogger: () => (/* reexport safe */ launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__.createConsoleLogger),\n/* harmony export */   defaultReactOptions: () => (/* binding */ u),\n/* harmony export */   initialize: () => (/* reexport safe */ launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__.initialize),\n/* harmony export */   reactSdkContextFactory: () => (/* binding */ p),\n/* harmony export */   useFlags: () => (/* binding */ ne),\n/* harmony export */   useLDClient: () => (/* binding */ oe),\n/* harmony export */   useLDClientError: () => (/* binding */ ae),\n/* harmony export */   version: () => (/* reexport safe */ launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__.version),\n/* harmony export */   withLDConsumer: () => (/* binding */ re),\n/* harmony export */   withLDProvider: () => (/* binding */ $)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! launchdarkly-js-client-sdk */ \"(ssr)/./node_modules/launchdarkly-js-client-sdk/dist/ldclient.es.js\");\n/* harmony import */ var lodash_camelcase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.camelcase */ \"(ssr)/./node_modules/lodash.camelcase/index.js\");\n/* harmony import */ var lodash_camelcase__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_camelcase__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__);\nconst p=()=>(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({flags:{},flagKeyMap:{},ldClient:void 0}),u={useCamelCaseFlagKeys:!0,sendEventsOnFlagRead:!0,reactContext:p()},f=e=>{var t;return null!=(t=e.context)?t:e.user},d=e=>{const t={};for(const r in e)0!==r.indexOf(\"$\")&&(t[lodash_camelcase__WEBPACK_IMPORTED_MODULE_2___default()(r)]=e[r]);return t},y=(e,t)=>{const r={};for(const n in e)t&&void 0===t[n]||(r[n]=e[n].current);return r},h=(e,t)=>{const r=e.allFlags();return t?Object.keys(t).reduce(((e,n)=>(e[n]=Object.prototype.hasOwnProperty.call(r,n)?r[n]:t[n],e)),{}):r};function g(e,t,r=u,n){const o=function(e,t){if(void 0===t)return e;return Object.keys(t).reduce(((t,r)=>(O(e,r)&&(t[r]=e[r]),t)),{})}(t,n),{useCamelCaseFlagKeys:a=!0}=r,[i,s={}]=a?function(e){const t={},r={};for(const n in e){if(0===n.indexOf(\"$\"))continue;const o=lodash_camelcase__WEBPACK_IMPORTED_MODULE_2___default()(n);t[o]=e[n],r[o]=n}return[t,r]}(o):[o];return{flags:r.sendEventsOnFlagRead?b(e,i,s,a):i,flagKeyMap:s}}function O(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function b(e,t,r,n){return new Proxy(t,{get(t,o,a){const i=Reflect.get(t,o,a),s=n&&O(r,o)||O(t,o);if(\"symbol\"==typeof o||!s)return i;if(void 0===i)return;const l=n?r[o]:o;return e.variation(l,i)}})}d.camelCaseKeys=d;const m={wrapperName:\"react-client-sdk\",wrapperVersion:\"3.6.1\",sendEventsOnlyForVariation:!0};var v=Object.defineProperty,C=Object.defineProperties,x=Object.getOwnPropertyDescriptors,j=Object.getOwnPropertySymbols,w=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable,F=(e,t,r)=>t in e?v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,E=(e,t)=>{for(var r in t||(t={}))w.call(t,r)&&F(e,r,t[r]);if(j)for(var r of j(t))P.call(t,r)&&F(e,r,t[r]);return e},S=(e,t)=>C(e,x(t)),k=(e,t,r)=>new Promise(((n,o)=>{var a=e=>{try{s(r.next(e))}catch(e){o(e)}},i=e=>{try{s(r.throw(e))}catch(e){o(e)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(a,i);s((r=r.apply(e,t)).next())}));class D extends react__WEBPACK_IMPORTED_MODULE_0__.Component{constructor(e){super(e),this.getReactOptions=()=>E(E({},u),this.props.reactOptions),this.subscribeToChanges=e=>{const{flags:t}=this.props;e.on(\"change\",(r=>{const n=this.getReactOptions(),o=y(r,t),a=E(E({},this.state.unproxiedFlags),o);Object.keys(o).length>0&&this.setState((r=>E(S(E({},r),{unproxiedFlags:a}),g(e,a,n,t))))}))},this.onFailed=(e,t)=>{this.setState((e=>S(E({},e),{error:t})))},this.onReady=(e,t,r)=>{const n=h(e,r);this.setState((o=>E(S(E({},o),{unproxiedFlags:n}),g(e,n,t,r))))},this.prepareLDClient=()=>k(this,null,(function*(){var e;const{clientSideID:t,flags:r,options:n}=this.props;let o=yield this.props.ldClient;const a=this.getReactOptions();let i,l=this.state.unproxiedFlags;if(o)l=h(o,r);else{const c=null!=(e=f(this.props))?e:{anonymous:!0,kind:\"user\"};o=(0,launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__.initialize)(t,c,E(E({},m),n));try{yield o.waitForInitialization(this.props.timeout),l=h(o,r)}catch(e){i=e,(null==i?void 0:i.name.toLowerCase().includes(\"timeout\"))&&(o.on(\"failed\",this.onFailed),o.on(\"ready\",(()=>{this.onReady(o,a,r)})))}}this.setState((e=>S(E(S(E({},e),{unproxiedFlags:l}),g(o,l,a,r)),{ldClient:o,error:i}))),this.subscribeToChanges(o)}));const{options:t}=e;if(this.state={flags:{},unproxiedFlags:{},flagKeyMap:{}},t){const{bootstrap:e}=t;if(e&&\"localStorage\"!==e){const{useCamelCaseFlagKeys:t}=this.getReactOptions();this.state={flags:t?d(e):e,unproxiedFlags:e,flagKeyMap:{}}}}}componentDidMount(){return k(this,null,(function*(){const{deferInitialization:e}=this.props;e&&!f(this.props)||(yield this.prepareLDClient())}))}componentDidUpdate(e){return k(this,null,(function*(){const{deferInitialization:t}=this.props,r=!f(e)&&f(this.props);t&&r&&(yield this.prepareLDClient())}))}render(){const{flags:e,flagKeyMap:r,ldClient:n,error:o}=this.state,{reactContext:a}=this.getReactOptions();return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(a.Provider,{value:{flags:e,flagKeyMap:r,ldClient:n,error:o}},this.props.children)}}var I=Object.defineProperty,K=Object.defineProperties,R=Object.getOwnPropertyDescriptors,M=Object.getOwnPropertySymbols,L=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,T=(e,t,r)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,V=(e,t)=>{for(var r in t||(t={}))L.call(t,r)&&T(e,r,t[r]);if(M)for(var r of M(t))z.call(t,r)&&T(e,r,t[r]);return e};function $(t){return function(r){const{reactOptions:n}=t,o=V(V({},u),n),a=(i=V({},t),K(i,R({reactOptions:o})));var i;function s(t){return react__WEBPACK_IMPORTED_MODULE_0__.createElement(D,V({},a),react__WEBPACK_IMPORTED_MODULE_0__.createElement(r,V({},t)))}return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(s,r),s}}var N=Object.defineProperty,U=Object.defineProperties,q=Object.getOwnPropertyDescriptors,A=Object.getOwnPropertySymbols,B=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,H=(e,t,r)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,J=(e,t)=>{for(var r in t||(t={}))B.call(t,r)&&H(e,r,t[r]);if(A)for(var r of A(t))G.call(t,r)&&H(e,r,t[r]);return e},Q=(e,t)=>U(e,q(t));function W(e){return r=this,n=null,i=function*(){var r,n;const{clientSideID:i,flags:l,options:c,reactOptions:p}=e,d=J(J({},u),p),O=null!=(r=f(e))?r:{anonymous:!0,kind:\"user\"};let b,v={};const C=null!=(n=yield e.ldClient)?n:(0,launchdarkly_js_client_sdk__WEBPACK_IMPORTED_MODULE_1__.initialize)(i,O,J(J({},m),c));try{yield C.waitForInitialization(e.timeout),v=h(C,l)}catch(e){b=e}const x=(null==c?void 0:c.bootstrap)&&\"localStorage\"!==c.bootstrap?c.bootstrap:v;return({children:e})=>{const[r,n]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)((()=>Q(J({unproxiedFlags:x},g(C,x,d,l)),{ldClient:C,error:b})));(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{function e(e){const t=y(e,l);Object.keys(t).length>0&&n((e=>{const r=J(J({},e.unproxiedFlags),t);return J(Q(J({},e),{unproxiedFlags:r}),g(C,r,d,l))}))}function t(){const e=h(C,l);n((t=>J(Q(J({},t),{unproxiedFlags:e}),g(C,e,d,l))))}function r(e){n((t=>Q(J({},t),{error:e})))}return C.on(\"change\",e),(null==b?void 0:b.name.toLowerCase().includes(\"timeout\"))&&(C.on(\"failed\",r),C.on(\"ready\",t)),function(){C.off(\"change\",e),C.off(\"failed\",r),C.off(\"ready\",t)}}),[]);const i=((e,t)=>{var r={};for(var n in e)B.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&A)for(var n of A(e))t.indexOf(n)<0&&G.call(e,n)&&(r[n]=e[n]);return r})(r,[\"unproxiedFlags\"]),{reactContext:s}=d;return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(s.Provider,{value:i},e)}},new Promise(((e,t)=>{var o=e=>{try{s(i.next(e))}catch(e){t(e)}},a=e=>{try{s(i.throw(e))}catch(e){t(e)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(o,a);s((i=i.apply(r,n)).next())}));var r,n,i}var X=Object.defineProperty,Y=Object.getOwnPropertySymbols,Z=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable,ee=(e,t,r)=>t in e?X(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,te=(e,t)=>{for(var r in t||(t={}))Z.call(t,r)&&ee(e,r,t[r]);if(Y)for(var r of Y(t))_.call(t,r)&&ee(e,r,t[r]);return e};function re(t={clientOnly:!1}){return function(r){var n;const o=null!=(n=t.reactContext)?n:u.reactContext;return n=>react__WEBPACK_IMPORTED_MODULE_0__.createElement(o.Consumer,null,(({flags:o,ldClient:a})=>t.clientOnly?react__WEBPACK_IMPORTED_MODULE_0__.createElement(r,te({ldClient:a},n)):react__WEBPACK_IMPORTED_MODULE_0__.createElement(r,te({flags:o,ldClient:a},n))))}}const ne=e=>{const{flags:t}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(null!=e?e:u.reactContext);return t},oe=e=>{const{ldClient:t}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(null!=e?e:u.reactContext);return t};function ae(e){const{error:t}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(null!=e?e:u.reactContext);return t}\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/launchdarkly-react-client-sdk/lib/esm/index.js\n");

/***/ })

};
;