"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/party-js";
exports.ids = ["vendor-chunks/party-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/party-js/lib/components/circle.js":
/*!********************************************************!*\
  !*** ./node_modules/party-js/lib/components/circle.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Circle = void 0;\r\n/**\r\n * Represents a circle.\r\n */\r\nvar Circle = /** @class */ (function () {\r\n    /**\r\n     * Creates a new circle at the specified coordinates, with a default radius of 0.\r\n     */\r\n    function Circle(x, y, radius) {\r\n        if (radius === void 0) { radius = 0; }\r\n        this.x = x;\r\n        this.y = y;\r\n        this.radius = radius;\r\n    }\r\n    Circle.zero = new Circle(0, 0);\r\n    return Circle;\r\n}());\r\nexports.Circle = Circle;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL2NvbXBvbmVudHMvY2lyY2xlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxjQUFjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL2NvbXBvbmVudHMvY2lyY2xlLmpzPzE5MWQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcclxuZXhwb3J0cy5DaXJjbGUgPSB2b2lkIDA7XHJcbi8qKlxyXG4gKiBSZXByZXNlbnRzIGEgY2lyY2xlLlxyXG4gKi9cclxudmFyIENpcmNsZSA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIC8qKlxyXG4gICAgICogQ3JlYXRlcyBhIG5ldyBjaXJjbGUgYXQgdGhlIHNwZWNpZmllZCBjb29yZGluYXRlcywgd2l0aCBhIGRlZmF1bHQgcmFkaXVzIG9mIDAuXHJcbiAgICAgKi9cclxuICAgIGZ1bmN0aW9uIENpcmNsZSh4LCB5LCByYWRpdXMpIHtcclxuICAgICAgICBpZiAocmFkaXVzID09PSB2b2lkIDApIHsgcmFkaXVzID0gMDsgfVxyXG4gICAgICAgIHRoaXMueCA9IHg7XHJcbiAgICAgICAgdGhpcy55ID0geTtcclxuICAgICAgICB0aGlzLnJhZGl1cyA9IHJhZGl1cztcclxuICAgIH1cclxuICAgIENpcmNsZS56ZXJvID0gbmV3IENpcmNsZSgwLCAwKTtcclxuICAgIHJldHVybiBDaXJjbGU7XHJcbn0oKSk7XHJcbmV4cG9ydHMuQ2lyY2xlID0gQ2lyY2xlO1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/circle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/color.js":
/*!*******************************************************!*\
  !*** ./node_modules/party-js/lib/components/color.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Color = void 0;\r\nvar math_1 = __webpack_require__(/*! ../systems/math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\n/**\r\n * Represents a color consisting of RGB values. The components of it are\r\n * represented as integers in the range 0 to 255.\r\n *\r\n * @example\r\n * ```ts\r\n * const a = new Color(12, 59, 219);\r\n * const b = Color.fromHex(\"#ffa68d\");\r\n * const result = a.mix(b);\r\n * ```\r\n */\r\nvar Color = /** @class */ (function () {\r\n    /**\r\n     * Creates a new color instance from the specified RGB components.\r\n     */\r\n    function Color(r, g, b) {\r\n        this.values = new Float32Array(3);\r\n        this.rgb = [r, g, b];\r\n    }\r\n    Object.defineProperty(Color.prototype, \"r\", {\r\n        /**\r\n         * Returns the r-component of the color.\r\n         */\r\n        get: function () {\r\n            return this.values[0];\r\n        },\r\n        /**\r\n         * Modifies the r-component of the color.\r\n         * Note that this also floors the value.\r\n         */\r\n        set: function (value) {\r\n            this.values[0] = Math.floor(value);\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Color.prototype, \"g\", {\r\n        /**\r\n         * Returns the g-component of the color.\r\n         */\r\n        get: function () {\r\n            return this.values[1];\r\n        },\r\n        /**\r\n         * Modifies the g-component of the color.\r\n         * Note that this also floors the value.\r\n         */\r\n        set: function (value) {\r\n            this.values[1] = Math.floor(value);\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Color.prototype, \"b\", {\r\n        /**\r\n         * Returns the b-component of the color.\r\n         * Note that this also floors the value.\r\n         */\r\n        get: function () {\r\n            return this.values[2];\r\n        },\r\n        /**\r\n         * Modifies the b-component of the color.\r\n         */\r\n        set: function (value) {\r\n            this.values[2] = Math.floor(value);\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Color.prototype, \"rgb\", {\r\n        /**\r\n         * Returns the rgb-components of the color, bundled as a copied array.\r\n         */\r\n        get: function () {\r\n            return [this.r, this.g, this.b];\r\n        },\r\n        /**\r\n         * Simultaneously updates the rgb-components of the color, by passing an array.\r\n         */\r\n        set: function (values) {\r\n            this.r = values[0];\r\n            this.g = values[1];\r\n            this.b = values[2];\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    /**\r\n     * Mixes the two color together with an optional mixing weight.\r\n     * This weight is 0.5 by default, perfectly averaging the color.\r\n     */\r\n    Color.prototype.mix = function (color, weight) {\r\n        if (weight === void 0) { weight = 0.5; }\r\n        return new Color(math_1.lerp(this.r, color.r, weight), math_1.lerp(this.g, color.g, weight), math_1.lerp(this.b, color.b, weight));\r\n    };\r\n    /**\r\n     * Returns the hexadecimal representation of the color, prefixed by '#'.\r\n     */\r\n    Color.prototype.toHex = function () {\r\n        var hex = function (v) { return v.toString(16).padStart(2, \"0\"); };\r\n        return \"#\" + hex(this.r) + hex(this.g) + hex(this.b);\r\n    };\r\n    /**\r\n     * Returns a formatted representation of the color.\r\n     */\r\n    Color.prototype.toString = function () {\r\n        return \"rgb(\" + this.values.join(\", \") + \")\";\r\n    };\r\n    /**\r\n     * Creates a color from the specified hexadecimal string.\r\n     * This string can optionally be prefixed by '#'.\r\n     */\r\n    Color.fromHex = function (hex) {\r\n        if (hex.startsWith(\"#\")) {\r\n            hex = hex.substr(1);\r\n        }\r\n        return new Color(parseInt(hex.substr(0, 2), 16), parseInt(hex.substr(2, 2), 16), parseInt(hex.substr(4, 2), 16));\r\n    };\r\n    /**\r\n     * Creates a color from the specified HSL components.\r\n     *\r\n     * @see https://stackoverflow.com/a/9493060/5507624\r\n     */\r\n    Color.fromHsl = function (h, s, l) {\r\n        h /= 360;\r\n        s /= 100;\r\n        l /= 100;\r\n        if (s === 0) {\r\n            return new Color(l, l, l);\r\n        }\r\n        else {\r\n            var hue2rgb = function (p, q, t) {\r\n                if (t < 0)\r\n                    t += 1;\r\n                if (t > 1)\r\n                    t -= 1;\r\n                if (t < 1 / 6)\r\n                    return p + (q - p) * 6 * t;\r\n                if (t < 1 / 2)\r\n                    return q;\r\n                if (t < 2 / 3)\r\n                    return p + (q - p) * (2 / 3 - t) * 6;\r\n                return p;\r\n            };\r\n            var to255 = function (v) { return Math.min(255, 256 * v); };\r\n            var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\r\n            var p = 2 * l - q;\r\n            return new Color(to255(hue2rgb(p, q, h + 1 / 3)), to255(hue2rgb(p, q, h)), to255(hue2rgb(p, q, h - 1 / 3)));\r\n        }\r\n    };\r\n    /**\r\n     * Returns (1, 1, 1).\r\n     */\r\n    Color.white = new Color(255, 255, 255);\r\n    /**\r\n     * Returns (0, 0, 0).\r\n     */\r\n    Color.black = new Color(0, 0, 0);\r\n    return Color;\r\n}());\r\nexports.Color = Color;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/color.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/gradient.js":
/*!**********************************************************!*\
  !*** ./node_modules/party-js/lib/components/gradient.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Gradient = void 0;\r\nvar spline_1 = __webpack_require__(/*! ./spline */ \"(ssr)/./node_modules/party-js/lib/components/spline.js\");\r\n/**\r\n * Represents a gradient that can be used to interpolate between multiple color.\r\n */\r\nvar Gradient = /** @class */ (function (_super) {\r\n    __extends(Gradient, _super);\r\n    function Gradient() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    /**\r\n     * Interpolates between two color on the gradient.\r\n     */\r\n    Gradient.prototype.interpolate = function (a, b, t) {\r\n        return a.mix(b, t);\r\n    };\r\n    /**\r\n     * Returns a solid gradient from the given color.\r\n     */\r\n    Gradient.solid = function (color) {\r\n        return new Gradient({ value: color, time: 0.5 });\r\n    };\r\n    /**\r\n     * Returns a gradient with evenly spaced keys from the given colors.\r\n     */\r\n    Gradient.simple = function () {\r\n        var colors = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            colors[_i] = arguments[_i];\r\n        }\r\n        var step = 1 / (colors.length - 1);\r\n        return new (Gradient.bind.apply(Gradient, __spreadArray([void 0], colors.map(function (color, index) { return ({\r\n            value: color,\r\n            time: index * step,\r\n        }); }))))();\r\n    };\r\n    return Gradient;\r\n}(spline_1.Spline));\r\nexports.Gradient = Gradient;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/gradient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/party-js/lib/components/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\n__exportStar(__webpack_require__(/*! ./circle */ \"(ssr)/./node_modules/party-js/lib/components/circle.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./color */ \"(ssr)/./node_modules/party-js/lib/components/color.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./gradient */ \"(ssr)/./node_modules/party-js/lib/components/gradient.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./numericSpline */ \"(ssr)/./node_modules/party-js/lib/components/numericSpline.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./rect */ \"(ssr)/./node_modules/party-js/lib/components/rect.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./vector */ \"(ssr)/./node_modules/party-js/lib/components/vector.js\"), exports);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL2NvbXBvbmVudHMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0EsbUNBQW1DLG9DQUFvQyxnQkFBZ0I7QUFDdkYsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsbUJBQU8sQ0FBQyx3RUFBVTtBQUMvQixhQUFhLG1CQUFPLENBQUMsc0VBQVM7QUFDOUIsYUFBYSxtQkFBTyxDQUFDLDRFQUFZO0FBQ2pDLGFBQWEsbUJBQU8sQ0FBQyxzRkFBaUI7QUFDdEMsYUFBYSxtQkFBTyxDQUFDLG9FQUFRO0FBQzdCLGFBQWEsbUJBQU8sQ0FBQyx3RUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi9jb21wb25lbnRzL2luZGV4LmpzPzI4ZDQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcclxuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XHJcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XHJcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XHJcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xyXG4gICAgb1trMl0gPSBtW2tdO1xyXG59KSk7XHJcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xyXG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xyXG59O1xyXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XHJcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9jaXJjbGVcIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vY29sb3JcIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZ3JhZGllbnRcIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vbnVtZXJpY1NwbGluZVwiKSwgZXhwb3J0cyk7XHJcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9yZWN0XCIpLCBleHBvcnRzKTtcclxuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3ZlY3RvclwiKSwgZXhwb3J0cyk7XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/numericSpline.js":
/*!***************************************************************!*\
  !*** ./node_modules/party-js/lib/components/numericSpline.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.NumericSpline = void 0;\r\nvar math_1 = __webpack_require__(/*! ../systems/math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\nvar spline_1 = __webpack_require__(/*! ./spline */ \"(ssr)/./node_modules/party-js/lib/components/spline.js\");\r\n/**\r\n * Represents a spline that can take numeric values.\r\n */\r\nvar NumericSpline = /** @class */ (function (_super) {\r\n    __extends(NumericSpline, _super);\r\n    function NumericSpline() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    /**\r\n     * Smoothly interpolates between two keys on the spline.\r\n     */\r\n    NumericSpline.prototype.interpolate = function (a, b, t) {\r\n        return math_1.slerp(a, b, t);\r\n    };\r\n    return NumericSpline;\r\n}(spline_1.Spline));\r\nexports.NumericSpline = NumericSpline;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL2NvbXBvbmVudHMvbnVtZXJpY1NwbGluZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZ0JBQWdCLHNDQUFzQyxrQkFBa0I7QUFDdkYsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0EsQ0FBQztBQUNELDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckIsYUFBYSxtQkFBTyxDQUFDLDBFQUFpQjtBQUN0QyxlQUFlLG1CQUFPLENBQUMsd0VBQVU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELHFCQUFxQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi9jb21wb25lbnRzL251bWVyaWNTcGxpbmUuanM/ZTU5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxudmFyIF9fZXh0ZW5kcyA9ICh0aGlzICYmIHRoaXMuX19leHRlbmRzKSB8fCAoZnVuY3Rpb24gKCkge1xyXG4gICAgdmFyIGV4dGVuZFN0YXRpY3MgPSBmdW5jdGlvbiAoZCwgYikge1xyXG4gICAgICAgIGV4dGVuZFN0YXRpY3MgPSBPYmplY3Quc2V0UHJvdG90eXBlT2YgfHxcclxuICAgICAgICAgICAgKHsgX19wcm90b19fOiBbXSB9IGluc3RhbmNlb2YgQXJyYXkgJiYgZnVuY3Rpb24gKGQsIGIpIHsgZC5fX3Byb3RvX18gPSBiOyB9KSB8fFxyXG4gICAgICAgICAgICBmdW5jdGlvbiAoZCwgYikgeyBmb3IgKHZhciBwIGluIGIpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoYiwgcCkpIGRbcF0gPSBiW3BdOyB9O1xyXG4gICAgICAgIHJldHVybiBleHRlbmRTdGF0aWNzKGQsIGIpO1xyXG4gICAgfTtcclxuICAgIHJldHVybiBmdW5jdGlvbiAoZCwgYikge1xyXG4gICAgICAgIGlmICh0eXBlb2YgYiAhPT0gXCJmdW5jdGlvblwiICYmIGIgIT09IG51bGwpXHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDbGFzcyBleHRlbmRzIHZhbHVlIFwiICsgU3RyaW5nKGIpICsgXCIgaXMgbm90IGEgY29uc3RydWN0b3Igb3IgbnVsbFwiKTtcclxuICAgICAgICBleHRlbmRTdGF0aWNzKGQsIGIpO1xyXG4gICAgICAgIGZ1bmN0aW9uIF9fKCkgeyB0aGlzLmNvbnN0cnVjdG9yID0gZDsgfVxyXG4gICAgICAgIGQucHJvdG90eXBlID0gYiA9PT0gbnVsbCA/IE9iamVjdC5jcmVhdGUoYikgOiAoX18ucHJvdG90eXBlID0gYi5wcm90b3R5cGUsIG5ldyBfXygpKTtcclxuICAgIH07XHJcbn0pKCk7XHJcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcclxuZXhwb3J0cy5OdW1lcmljU3BsaW5lID0gdm9pZCAwO1xyXG52YXIgbWF0aF8xID0gcmVxdWlyZShcIi4uL3N5c3RlbXMvbWF0aFwiKTtcclxudmFyIHNwbGluZV8xID0gcmVxdWlyZShcIi4vc3BsaW5lXCIpO1xyXG4vKipcclxuICogUmVwcmVzZW50cyBhIHNwbGluZSB0aGF0IGNhbiB0YWtlIG51bWVyaWMgdmFsdWVzLlxyXG4gKi9cclxudmFyIE51bWVyaWNTcGxpbmUgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoX3N1cGVyKSB7XHJcbiAgICBfX2V4dGVuZHMoTnVtZXJpY1NwbGluZSwgX3N1cGVyKTtcclxuICAgIGZ1bmN0aW9uIE51bWVyaWNTcGxpbmUoKSB7XHJcbiAgICAgICAgcmV0dXJuIF9zdXBlciAhPT0gbnVsbCAmJiBfc3VwZXIuYXBwbHkodGhpcywgYXJndW1lbnRzKSB8fCB0aGlzO1xyXG4gICAgfVxyXG4gICAgLyoqXHJcbiAgICAgKiBTbW9vdGhseSBpbnRlcnBvbGF0ZXMgYmV0d2VlbiB0d28ga2V5cyBvbiB0aGUgc3BsaW5lLlxyXG4gICAgICovXHJcbiAgICBOdW1lcmljU3BsaW5lLnByb3RvdHlwZS5pbnRlcnBvbGF0ZSA9IGZ1bmN0aW9uIChhLCBiLCB0KSB7XHJcbiAgICAgICAgcmV0dXJuIG1hdGhfMS5zbGVycChhLCBiLCB0KTtcclxuICAgIH07XHJcbiAgICByZXR1cm4gTnVtZXJpY1NwbGluZTtcclxufShzcGxpbmVfMS5TcGxpbmUpKTtcclxuZXhwb3J0cy5OdW1lcmljU3BsaW5lID0gTnVtZXJpY1NwbGluZTtcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/numericSpline.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/rect.js":
/*!******************************************************!*\
  !*** ./node_modules/party-js/lib/components/rect.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Rect = void 0;\r\n/**\r\n * Represents a rectangle with an origin and size.\r\n */\r\nvar Rect = /** @class */ (function () {\r\n    function Rect(x, y, width, height) {\r\n        if (width === void 0) { width = 0; }\r\n        if (height === void 0) { height = 0; }\r\n        this.x = x;\r\n        this.y = y;\r\n        this.width = width;\r\n        this.height = height;\r\n    }\r\n    /**\r\n     * Returns a new document-space rectangle from the viewport's bounds.\r\n     */\r\n    Rect.fromScreen = function () {\r\n        return new Rect(window.scrollX, window.scrollY, window.innerWidth, window.innerHeight);\r\n    };\r\n    /**\r\n     * Returns a new document-space rectangle from the specified element.\r\n     */\r\n    Rect.fromElement = function (element) {\r\n        var r = element.getBoundingClientRect();\r\n        return new Rect(window.scrollX + r.x, window.scrollY + r.y, r.width, r.height);\r\n    };\r\n    Rect.zero = new Rect(0, 0);\r\n    return Rect;\r\n}());\r\nexports.Rect = Rect;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/rect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/spline.js":
/*!********************************************************!*\
  !*** ./node_modules/party-js/lib/components/spline.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Spline = void 0;\r\nvar math_1 = __webpack_require__(/*! ../systems/math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\n/**\r\n * Represents a spline that can be used to continueously evaluate a function\r\n * between keys. The base implementation is kept generic, so the functionality\r\n * can easily be implemented for similar constructs, such as gradients.\r\n */\r\nvar Spline = /** @class */ (function () {\r\n    /**\r\n     * Creates a new spline instance, using the specified keys.\r\n     * Note that you have to pass at least one key.\r\n     */\r\n    function Spline() {\r\n        var keys = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            keys[_i] = arguments[_i];\r\n        }\r\n        if (keys.length === 0) {\r\n            throw new Error(\"Splines require at least one key.\");\r\n        }\r\n        if (Array.isArray(keys[0])) {\r\n            throw new Error(\"You are trying to pass an array to the spline constructor, which is not supported. \" +\r\n                \"Try to spread the array into the constructor instead.\");\r\n        }\r\n        this.keys = keys;\r\n    }\r\n    /**\r\n     * Evaluates the spline at the given time.\r\n     */\r\n    Spline.prototype.evaluate = function (time) {\r\n        if (this.keys.length === 0) {\r\n            throw new Error(\"Attempt to evaluate a spline with no keys.\");\r\n        }\r\n        if (this.keys.length === 1) {\r\n            // The spline only contains one key, therefore is constant.\r\n            return this.keys[0].value;\r\n        }\r\n        // Sort the keys and figure out the first key above the passed time.\r\n        var ascendingKeys = this.keys.sort(function (a, b) { return a.time - b.time; });\r\n        var upperKeyIndex = ascendingKeys.findIndex(function (g) { return g.time > time; });\r\n        // If the found index is either 0 or -1, the specified time falls out\r\n        // of the range of the supplied keys. In that case, the value of the\r\n        // nearest applicant key is returned.\r\n        if (upperKeyIndex === 0) {\r\n            return ascendingKeys[0].value;\r\n        }\r\n        if (upperKeyIndex === -1) {\r\n            return ascendingKeys[ascendingKeys.length - 1].value;\r\n        }\r\n        // Otherwise, find the bounding keys, and extrapolate the time between\r\n        // the two. This is then used to interpolate between the two keys,\r\n        // using the provided implementation.\r\n        var lowerKey = ascendingKeys[upperKeyIndex - 1];\r\n        var upperKey = ascendingKeys[upperKeyIndex];\r\n        var containedTime = math_1.invlerp(lowerKey.time, upperKey.time, time);\r\n        return this.interpolate(lowerKey.value, upperKey.value, containedTime);\r\n    };\r\n    return Spline;\r\n}());\r\nexports.Spline = Spline;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/spline.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/components/vector.js":
/*!********************************************************!*\
  !*** ./node_modules/party-js/lib/components/vector.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Vector = void 0;\r\nvar math_1 = __webpack_require__(/*! ../systems/math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\n/**\r\n * Represents a structure used to process vectors.\r\n *\r\n * @remarks\r\n * Note that the operations in this class will **not** modify the original vector,\r\n * except for the property assignments. This is to ensure that vectors are not\r\n * unintentionally modified.\r\n *\r\n * @example\r\n * ```ts\r\n * const vectorA = new Vector(1, 3, 5);\r\n * const vectorB = new Vector(2, 3, 1);\r\n * const vectorC = vectorA.add(vectorB); // (3, 6, 6)\r\n * ```\r\n */\r\nvar Vector = /** @class */ (function () {\r\n    /**\r\n     * Creates a new vector with optional x-, y-, and z-components.\r\n     * Omitted components are defaulted to 0.\r\n     */\r\n    function Vector(x, y, z) {\r\n        if (x === void 0) { x = 0; }\r\n        if (y === void 0) { y = 0; }\r\n        if (z === void 0) { z = 0; }\r\n        this.values = new Float32Array(3);\r\n        this.xyz = [x, y, z];\r\n    }\r\n    Object.defineProperty(Vector.prototype, \"x\", {\r\n        /**\r\n         * Returns the x-component of the vector.\r\n         */\r\n        get: function () {\r\n            return this.values[0];\r\n        },\r\n        /**\r\n         * Modifies the x-component of the vector.\r\n         */\r\n        set: function (value) {\r\n            this.values[0] = value;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Vector.prototype, \"y\", {\r\n        /**\r\n         * Returns the y-component of the vector.\r\n         */\r\n        get: function () {\r\n            return this.values[1];\r\n        },\r\n        /**\r\n         * Modifies the y-component of the vector.\r\n         */\r\n        set: function (value) {\r\n            this.values[1] = value;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Vector.prototype, \"z\", {\r\n        /**\r\n         * Returns the z-component of the vector.\r\n         */\r\n        get: function () {\r\n            return this.values[2];\r\n        },\r\n        /**\r\n         * Modifies the z-component of the vector.\r\n         */\r\n        set: function (value) {\r\n            this.values[2] = value;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Vector.prototype, \"xyz\", {\r\n        /**\r\n         * Returns the xyz-components of the vector, bundled as a copied array.\r\n         */\r\n        get: function () {\r\n            return [this.x, this.y, this.z];\r\n        },\r\n        /**\r\n         * Simultaneously updates the xyz-components of the vector, by passing an array.\r\n         */\r\n        set: function (values) {\r\n            this.values[0] = values[0];\r\n            this.values[1] = values[1];\r\n            this.values[2] = values[2];\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    /**\r\n     * Returns the length of the vector.\r\n     */\r\n    Vector.prototype.magnitude = function () {\r\n        return Math.sqrt(this.sqrMagnitude());\r\n    };\r\n    /**\r\n     * Returns the squared length of the vector.\r\n     */\r\n    Vector.prototype.sqrMagnitude = function () {\r\n        return this.x * this.x + this.y * this.y + this.z * this.z;\r\n    };\r\n    /**\r\n     * Adds the two vectors together, component-wise.\r\n     */\r\n    Vector.prototype.add = function (vector) {\r\n        return new Vector(this.x + vector.x, this.y + vector.y, this.z + vector.z);\r\n    };\r\n    /**\r\n     * Subtracts the right vector from the left one, component-wise.\r\n     */\r\n    Vector.prototype.subtract = function (vector) {\r\n        return new Vector(this.x - vector.x, this.y - vector.y, this.z - vector.z);\r\n    };\r\n    /**\r\n     * Scales the lefthand vector by another vector or by a number.\r\n     */\r\n    Vector.prototype.scale = function (scalar) {\r\n        if (typeof scalar === \"number\") {\r\n            return new Vector(this.x * scalar, this.y * scalar, this.z * scalar);\r\n        }\r\n        else {\r\n            return new Vector(this.x * scalar.x, this.y * scalar.y, this.z * scalar.z);\r\n        }\r\n    };\r\n    /**\r\n     * Normalizes the vector to a length of 1. If the length was previously zero,\r\n     * then a zero-length vector will be returned.\r\n     */\r\n    Vector.prototype.normalized = function () {\r\n        var magnitude = this.magnitude();\r\n        if (magnitude !== 0) {\r\n            return this.scale(1 / magnitude);\r\n        }\r\n        return new (Vector.bind.apply(Vector, __spreadArray([void 0], this.xyz)))();\r\n    };\r\n    /**\r\n     * Returns the angle between two vectors, in degrees.\r\n     */\r\n    Vector.prototype.angle = function (vector) {\r\n        return (math_1.rad2deg *\r\n            Math.acos((this.x * vector.x + this.y * vector.y + this.z * vector.z) /\r\n                (this.magnitude() * vector.magnitude())));\r\n    };\r\n    /**\r\n     * Returns the cross-product of two vectors.\r\n     */\r\n    Vector.prototype.cross = function (vector) {\r\n        return new Vector(this.y * vector.z - this.z * vector.y, this.z * vector.x - this.x * vector.z, this.x * vector.y - this.y * vector.x);\r\n    };\r\n    /**\r\n     * returns the dot-product of two vectors.\r\n     */\r\n    Vector.prototype.dot = function (vector) {\r\n        return (this.magnitude() *\r\n            vector.magnitude() *\r\n            Math.cos(math_1.deg2rad * this.angle(vector)));\r\n    };\r\n    /**\r\n     * Returns a formatted representation of the vector.\r\n     */\r\n    Vector.prototype.toString = function () {\r\n        return \"Vector(\" + this.values.join(\", \") + \")\";\r\n    };\r\n    /**\r\n     * Creates a new vector from an angle, in degrees. Note that the z-component will be zero.\r\n     */\r\n    Vector.from2dAngle = function (angle) {\r\n        return new Vector(Math.cos(angle * math_1.deg2rad), Math.sin(angle * math_1.deg2rad));\r\n    };\r\n    /**\r\n     * Returns (0, 0, 0).\r\n     */\r\n    Vector.zero = new Vector(0, 0, 0);\r\n    /**\r\n     * Returns (1, 1, 1).\r\n     */\r\n    Vector.one = new Vector(1, 1, 1);\r\n    /**\r\n     * Returns (1, 0, 0).\r\n     */\r\n    Vector.right = new Vector(1, 0, 0);\r\n    /**\r\n     * Returns (0, 1, 0).\r\n     */\r\n    Vector.up = new Vector(0, 1, 0);\r\n    /**\r\n     * Returns (0, 0, 1).\r\n     */\r\n    Vector.forward = new Vector(0, 0, 1);\r\n    return Vector;\r\n}());\r\nexports.Vector = Vector;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/components/vector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/containers.js":
/*!*************************************************!*\
  !*** ./node_modules/party-js/lib/containers.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.particleContainer = exports.debugContainer = exports.rootContainer = void 0;\r\nvar settings_1 = __webpack_require__(/*! ./settings */ \"(ssr)/./node_modules/party-js/lib/settings.js\");\r\nvar util_1 = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/party-js/lib/util/index.js\");\r\n/**\r\n * The prefix to apply to the containers.\r\n */\r\nvar containerPrefix = \"party-js-\";\r\n/**\r\n * Checks if the specified container is 'active', meaning not undefined and attached to the DOM.\r\n */\r\nfunction isContainerActive(container) {\r\n    return container && container.isConnected;\r\n}\r\n/**\r\n * A generic factory method for creating a DOM container. Prefixes the specified name with the\r\n * container prefix, applies the styles and adds it under the parent.\r\n */\r\nfunction makeContainer(name, styles, parent) {\r\n    var container = document.createElement(\"div\");\r\n    container.id = containerPrefix + name;\r\n    Object.assign(container.style, styles);\r\n    return parent.appendChild(container);\r\n}\r\n/**\r\n * Represents the root container for DOM elements of the library.\r\n */\r\nexports.rootContainer = new util_1.Lazy(function () {\r\n    return makeContainer(\"container\", {\r\n        position: \"fixed\",\r\n        left: \"0\",\r\n        top: \"0\",\r\n        height: \"100vh\",\r\n        width: \"100vw\",\r\n        pointerEvents: \"none\",\r\n        userSelect: \"none\",\r\n        zIndex: settings_1.settings.zIndex.toString(),\r\n    }, document.body);\r\n}, isContainerActive);\r\n/**\r\n * Represents the debugging container of the library, only active if debugging is enabled.\r\n */\r\nexports.debugContainer = new util_1.Lazy(function () {\r\n    return makeContainer(\"debug\", {\r\n        position: \"absolute\",\r\n        top: \"0\",\r\n        left: \"0\",\r\n        margin: \"0.5em\",\r\n        padding: \"0.5em 1em\",\r\n        border: \"2px solid rgb(0, 0, 0, 0.2)\",\r\n        background: \"rgb(0, 0, 0, 0.1)\",\r\n        color: \"#555\",\r\n        fontFamily: \"monospace\",\r\n    }, exports.rootContainer.current);\r\n}, isContainerActive);\r\n/**\r\n * Represents the particle container of the library.\r\n * This is where the particle DOM elements get rendered into.\r\n */\r\nexports.particleContainer = new util_1.Lazy(function () {\r\n    return makeContainer(\"particles\", {\r\n        width: \"100%\",\r\n        height: \"100%\",\r\n        overflow: \"hidden\",\r\n        perspective: \"1200px\",\r\n    }, exports.rootContainer.current);\r\n}, isContainerActive);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/containers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/debug.js":
/*!********************************************!*\
  !*** ./node_modules/party-js/lib/debug.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Debug = void 0;\r\nvar containers_1 = __webpack_require__(/*! ./containers */ \"(ssr)/./node_modules/party-js/lib/containers.js\");\r\nvar settings_1 = __webpack_require__(/*! ./settings */ \"(ssr)/./node_modules/party-js/lib/settings.js\");\r\n/**\r\n * Represents a utility module to view debug information inside the DOM.\r\n * This is disabled by default and needs to manually be enabled by setting\r\n * the '.enabled' field to true.\r\n *\r\n * While disabled, the utility will not fetch stats and update itself.\r\n */\r\nvar Debug = /** @class */ (function () {\r\n    /**\r\n     * Registers a new debug utility that is attached to the given scene.\r\n     *\r\n     * @param scene The scene to attach to.\r\n     */\r\n    function Debug(scene) {\r\n        this.scene = scene;\r\n        /**\r\n         * The rate at which the debug interface should refresh itself (per second).\r\n         */\r\n        this.refreshRate = 8;\r\n        /**\r\n         * The timer counting down to refreshes.\r\n         */\r\n        this.refreshTimer = 1 / this.refreshRate;\r\n    }\r\n    /**\r\n     * Processes a tick event in the interface. This checks if enough has passed to\r\n     * trigger a refresh, and if so, fetches the debug information and updates the DOM.\r\n     *\r\n     * @param delta The time that has elapsed since the last tick.\r\n     */\r\n    Debug.prototype.tick = function (delta) {\r\n        var container = containers_1.debugContainer.current;\r\n        // If the current display style does not match the style inferred from the\r\n        // enabled-state, update it.\r\n        var displayStyle = settings_1.settings.debug ? \"block\" : \"none\";\r\n        if (container.style.display !== displayStyle) {\r\n            container.style.display = displayStyle;\r\n        }\r\n        if (!settings_1.settings.debug) {\r\n            // If the interface is not enabled, don't fetch or update any infos.\r\n            return;\r\n        }\r\n        this.refreshTimer += delta;\r\n        if (this.refreshTimer > 1 / this.refreshRate) {\r\n            this.refreshTimer = 0;\r\n            // Update the container with the fetched information joined on line breaks.\r\n            container.innerHTML = this.getDebugInformation(delta).join(\"<br>\");\r\n        }\r\n    };\r\n    /**\r\n     * Fetches the debug information from the specified delta and the linked scene.\r\n     *\r\n     * @returns An array of debugging information, formatted as HTML.\r\n     */\r\n    Debug.prototype.getDebugInformation = function (delta) {\r\n        // Count emitters and particles.\r\n        var emitters = this.scene.emitters.length;\r\n        var particles = this.scene.emitters.reduce(function (acc, cur) { return acc + cur.particles.length; }, 0);\r\n        var infos = [\r\n            \"<b>party.js Debug</b>\",\r\n            \"--------------\",\r\n            \"FPS: \" + Math.round(1 / delta),\r\n            \"Emitters: \" + emitters,\r\n            \"Particles: \" + particles,\r\n        ];\r\n        // Emitter informations are formatted using their index, internal timer\r\n        // and total particle count.\r\n        var emitterInfos = this.scene.emitters.map(function (emitter) {\r\n            return [\r\n                // Show the current loop and the total loops.\r\n                \"\\u2B6F: \" + (emitter[\"currentLoop\"] + 1) + \"/\" + (emitter.options.loops >= 0 ? emitter.options.loops : \"∞\"),\r\n                // Show the amount of particle contained.\r\n                \"\\u03A3p: \" + emitter.particles.length,\r\n                // Show the internal timer.\r\n                !emitter.isExpired\r\n                    ? \"\\u03A3t: \" + emitter[\"durationTimer\"].toFixed(3) + \"s\"\r\n                    : \"<i>expired</i>\",\r\n            ].join(\", \");\r\n        });\r\n        infos.push.apply(infos, __spreadArray([\"--------------\"], emitterInfos));\r\n        return infos;\r\n    };\r\n    return Debug;\r\n}());\r\nexports.Debug = Debug;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL2RlYnVnLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSxxREFBcUQsUUFBUTtBQUM3RDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYTtBQUNiLG1CQUFtQixtQkFBTyxDQUFDLHFFQUFjO0FBQ3pDLGlCQUFpQixtQkFBTyxDQUFDLGlFQUFZO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlFQUF5RSxvQ0FBb0M7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL2RlYnVnLmpzP2IwMTgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbnZhciBfX3NwcmVhZEFycmF5ID0gKHRoaXMgJiYgdGhpcy5fX3NwcmVhZEFycmF5KSB8fCBmdW5jdGlvbiAodG8sIGZyb20pIHtcclxuICAgIGZvciAodmFyIGkgPSAwLCBpbCA9IGZyb20ubGVuZ3RoLCBqID0gdG8ubGVuZ3RoOyBpIDwgaWw7IGkrKywgaisrKVxyXG4gICAgICAgIHRvW2pdID0gZnJvbVtpXTtcclxuICAgIHJldHVybiB0bztcclxufTtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLkRlYnVnID0gdm9pZCAwO1xyXG52YXIgY29udGFpbmVyc18xID0gcmVxdWlyZShcIi4vY29udGFpbmVyc1wiKTtcclxudmFyIHNldHRpbmdzXzEgPSByZXF1aXJlKFwiLi9zZXR0aW5nc1wiKTtcclxuLyoqXHJcbiAqIFJlcHJlc2VudHMgYSB1dGlsaXR5IG1vZHVsZSB0byB2aWV3IGRlYnVnIGluZm9ybWF0aW9uIGluc2lkZSB0aGUgRE9NLlxyXG4gKiBUaGlzIGlzIGRpc2FibGVkIGJ5IGRlZmF1bHQgYW5kIG5lZWRzIHRvIG1hbnVhbGx5IGJlIGVuYWJsZWQgYnkgc2V0dGluZ1xyXG4gKiB0aGUgJy5lbmFibGVkJyBmaWVsZCB0byB0cnVlLlxyXG4gKlxyXG4gKiBXaGlsZSBkaXNhYmxlZCwgdGhlIHV0aWxpdHkgd2lsbCBub3QgZmV0Y2ggc3RhdHMgYW5kIHVwZGF0ZSBpdHNlbGYuXHJcbiAqL1xyXG52YXIgRGVidWcgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XHJcbiAgICAvKipcclxuICAgICAqIFJlZ2lzdGVycyBhIG5ldyBkZWJ1ZyB1dGlsaXR5IHRoYXQgaXMgYXR0YWNoZWQgdG8gdGhlIGdpdmVuIHNjZW5lLlxyXG4gICAgICpcclxuICAgICAqIEBwYXJhbSBzY2VuZSBUaGUgc2NlbmUgdG8gYXR0YWNoIHRvLlxyXG4gICAgICovXHJcbiAgICBmdW5jdGlvbiBEZWJ1ZyhzY2VuZSkge1xyXG4gICAgICAgIHRoaXMuc2NlbmUgPSBzY2VuZTtcclxuICAgICAgICAvKipcclxuICAgICAgICAgKiBUaGUgcmF0ZSBhdCB3aGljaCB0aGUgZGVidWcgaW50ZXJmYWNlIHNob3VsZCByZWZyZXNoIGl0c2VsZiAocGVyIHNlY29uZCkuXHJcbiAgICAgICAgICovXHJcbiAgICAgICAgdGhpcy5yZWZyZXNoUmF0ZSA9IDg7XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogVGhlIHRpbWVyIGNvdW50aW5nIGRvd24gdG8gcmVmcmVzaGVzLlxyXG4gICAgICAgICAqL1xyXG4gICAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gMSAvIHRoaXMucmVmcmVzaFJhdGU7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFByb2Nlc3NlcyBhIHRpY2sgZXZlbnQgaW4gdGhlIGludGVyZmFjZS4gVGhpcyBjaGVja3MgaWYgZW5vdWdoIGhhcyBwYXNzZWQgdG9cclxuICAgICAqIHRyaWdnZXIgYSByZWZyZXNoLCBhbmQgaWYgc28sIGZldGNoZXMgdGhlIGRlYnVnIGluZm9ybWF0aW9uIGFuZCB1cGRhdGVzIHRoZSBET00uXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIGRlbHRhIFRoZSB0aW1lIHRoYXQgaGFzIGVsYXBzZWQgc2luY2UgdGhlIGxhc3QgdGljay5cclxuICAgICAqL1xyXG4gICAgRGVidWcucHJvdG90eXBlLnRpY2sgPSBmdW5jdGlvbiAoZGVsdGEpIHtcclxuICAgICAgICB2YXIgY29udGFpbmVyID0gY29udGFpbmVyc18xLmRlYnVnQ29udGFpbmVyLmN1cnJlbnQ7XHJcbiAgICAgICAgLy8gSWYgdGhlIGN1cnJlbnQgZGlzcGxheSBzdHlsZSBkb2VzIG5vdCBtYXRjaCB0aGUgc3R5bGUgaW5mZXJyZWQgZnJvbSB0aGVcclxuICAgICAgICAvLyBlbmFibGVkLXN0YXRlLCB1cGRhdGUgaXQuXHJcbiAgICAgICAgdmFyIGRpc3BsYXlTdHlsZSA9IHNldHRpbmdzXzEuc2V0dGluZ3MuZGVidWcgPyBcImJsb2NrXCIgOiBcIm5vbmVcIjtcclxuICAgICAgICBpZiAoY29udGFpbmVyLnN0eWxlLmRpc3BsYXkgIT09IGRpc3BsYXlTdHlsZSkge1xyXG4gICAgICAgICAgICBjb250YWluZXIuc3R5bGUuZGlzcGxheSA9IGRpc3BsYXlTdHlsZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKCFzZXR0aW5nc18xLnNldHRpbmdzLmRlYnVnKSB7XHJcbiAgICAgICAgICAgIC8vIElmIHRoZSBpbnRlcmZhY2UgaXMgbm90IGVuYWJsZWQsIGRvbid0IGZldGNoIG9yIHVwZGF0ZSBhbnkgaW5mb3MuXHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5yZWZyZXNoVGltZXIgKz0gZGVsdGE7XHJcbiAgICAgICAgaWYgKHRoaXMucmVmcmVzaFRpbWVyID4gMSAvIHRoaXMucmVmcmVzaFJhdGUpIHtcclxuICAgICAgICAgICAgdGhpcy5yZWZyZXNoVGltZXIgPSAwO1xyXG4gICAgICAgICAgICAvLyBVcGRhdGUgdGhlIGNvbnRhaW5lciB3aXRoIHRoZSBmZXRjaGVkIGluZm9ybWF0aW9uIGpvaW5lZCBvbiBsaW5lIGJyZWFrcy5cclxuICAgICAgICAgICAgY29udGFpbmVyLmlubmVySFRNTCA9IHRoaXMuZ2V0RGVidWdJbmZvcm1hdGlvbihkZWx0YSkuam9pbihcIjxicj5cIik7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogRmV0Y2hlcyB0aGUgZGVidWcgaW5mb3JtYXRpb24gZnJvbSB0aGUgc3BlY2lmaWVkIGRlbHRhIGFuZCB0aGUgbGlua2VkIHNjZW5lLlxyXG4gICAgICpcclxuICAgICAqIEByZXR1cm5zIEFuIGFycmF5IG9mIGRlYnVnZ2luZyBpbmZvcm1hdGlvbiwgZm9ybWF0dGVkIGFzIEhUTUwuXHJcbiAgICAgKi9cclxuICAgIERlYnVnLnByb3RvdHlwZS5nZXREZWJ1Z0luZm9ybWF0aW9uID0gZnVuY3Rpb24gKGRlbHRhKSB7XHJcbiAgICAgICAgLy8gQ291bnQgZW1pdHRlcnMgYW5kIHBhcnRpY2xlcy5cclxuICAgICAgICB2YXIgZW1pdHRlcnMgPSB0aGlzLnNjZW5lLmVtaXR0ZXJzLmxlbmd0aDtcclxuICAgICAgICB2YXIgcGFydGljbGVzID0gdGhpcy5zY2VuZS5lbWl0dGVycy5yZWR1Y2UoZnVuY3Rpb24gKGFjYywgY3VyKSB7IHJldHVybiBhY2MgKyBjdXIucGFydGljbGVzLmxlbmd0aDsgfSwgMCk7XHJcbiAgICAgICAgdmFyIGluZm9zID0gW1xyXG4gICAgICAgICAgICBcIjxiPnBhcnR5LmpzIERlYnVnPC9iPlwiLFxyXG4gICAgICAgICAgICBcIi0tLS0tLS0tLS0tLS0tXCIsXHJcbiAgICAgICAgICAgIFwiRlBTOiBcIiArIE1hdGgucm91bmQoMSAvIGRlbHRhKSxcclxuICAgICAgICAgICAgXCJFbWl0dGVyczogXCIgKyBlbWl0dGVycyxcclxuICAgICAgICAgICAgXCJQYXJ0aWNsZXM6IFwiICsgcGFydGljbGVzLFxyXG4gICAgICAgIF07XHJcbiAgICAgICAgLy8gRW1pdHRlciBpbmZvcm1hdGlvbnMgYXJlIGZvcm1hdHRlZCB1c2luZyB0aGVpciBpbmRleCwgaW50ZXJuYWwgdGltZXJcclxuICAgICAgICAvLyBhbmQgdG90YWwgcGFydGljbGUgY291bnQuXHJcbiAgICAgICAgdmFyIGVtaXR0ZXJJbmZvcyA9IHRoaXMuc2NlbmUuZW1pdHRlcnMubWFwKGZ1bmN0aW9uIChlbWl0dGVyKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBbXHJcbiAgICAgICAgICAgICAgICAvLyBTaG93IHRoZSBjdXJyZW50IGxvb3AgYW5kIHRoZSB0b3RhbCBsb29wcy5cclxuICAgICAgICAgICAgICAgIFwiXFx1MkI2RjogXCIgKyAoZW1pdHRlcltcImN1cnJlbnRMb29wXCJdICsgMSkgKyBcIi9cIiArIChlbWl0dGVyLm9wdGlvbnMubG9vcHMgPj0gMCA/IGVtaXR0ZXIub3B0aW9ucy5sb29wcyA6IFwi4oieXCIpLFxyXG4gICAgICAgICAgICAgICAgLy8gU2hvdyB0aGUgYW1vdW50IG9mIHBhcnRpY2xlIGNvbnRhaW5lZC5cclxuICAgICAgICAgICAgICAgIFwiXFx1MDNBM3A6IFwiICsgZW1pdHRlci5wYXJ0aWNsZXMubGVuZ3RoLFxyXG4gICAgICAgICAgICAgICAgLy8gU2hvdyB0aGUgaW50ZXJuYWwgdGltZXIuXHJcbiAgICAgICAgICAgICAgICAhZW1pdHRlci5pc0V4cGlyZWRcclxuICAgICAgICAgICAgICAgICAgICA/IFwiXFx1MDNBM3Q6IFwiICsgZW1pdHRlcltcImR1cmF0aW9uVGltZXJcIl0udG9GaXhlZCgzKSArIFwic1wiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcIjxpPmV4cGlyZWQ8L2k+XCIsXHJcbiAgICAgICAgICAgIF0uam9pbihcIiwgXCIpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGluZm9zLnB1c2guYXBwbHkoaW5mb3MsIF9fc3ByZWFkQXJyYXkoW1wiLS0tLS0tLS0tLS0tLS1cIl0sIGVtaXR0ZXJJbmZvcykpO1xyXG4gICAgICAgIHJldHVybiBpbmZvcztcclxuICAgIH07XHJcbiAgICByZXR1cm4gRGVidWc7XHJcbn0oKSk7XHJcbmV4cG9ydHMuRGVidWcgPSBEZWJ1ZztcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/debug.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/party-js/lib/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports[\"default\"] = exports.forceInit = exports.util = exports.math = exports.random = exports.sources = exports.variation = exports.Emitter = exports.Particle = exports.settings = exports.scene = void 0;\r\nvar scene_1 = __webpack_require__(/*! ./scene */ \"(ssr)/./node_modules/party-js/lib/scene.js\");\r\nvar util_1 = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/party-js/lib/util/index.js\");\r\n__exportStar(__webpack_require__(/*! ./components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./templates */ \"(ssr)/./node_modules/party-js/lib/templates/index.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./systems/shapes */ \"(ssr)/./node_modules/party-js/lib/systems/shapes.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./systems/modules */ \"(ssr)/./node_modules/party-js/lib/systems/modules.js\"), exports);\r\n// Create the lazy-initializing scene.\r\nexports.scene = new util_1.Lazy(function () {\r\n    // The library requires the use of the DOM, hence it cannot run in non-browser environments.\r\n    if (typeof document === \"undefined\" || typeof window === \"undefined\") {\r\n        throw new Error(\"It seems like you are trying to run party.js in a non-browser-like environment, which is not supported.\");\r\n    }\r\n    return new scene_1.Scene();\r\n});\r\nvar settings_1 = __webpack_require__(/*! ./settings */ \"(ssr)/./node_modules/party-js/lib/settings.js\");\r\nObject.defineProperty(exports, \"settings\", ({ enumerable: true, get: function () { return settings_1.settings; } }));\r\nvar particle_1 = __webpack_require__(/*! ./particles/particle */ \"(ssr)/./node_modules/party-js/lib/particles/particle.js\");\r\nObject.defineProperty(exports, \"Particle\", ({ enumerable: true, get: function () { return particle_1.Particle; } }));\r\nvar emitter_1 = __webpack_require__(/*! ./particles/emitter */ \"(ssr)/./node_modules/party-js/lib/particles/emitter.js\");\r\nObject.defineProperty(exports, \"Emitter\", ({ enumerable: true, get: function () { return emitter_1.Emitter; } }));\r\nexports.variation = __webpack_require__(/*! ./systems/variation */ \"(ssr)/./node_modules/party-js/lib/systems/variation.js\");\r\nexports.sources = __webpack_require__(/*! ./systems/sources */ \"(ssr)/./node_modules/party-js/lib/systems/sources.js\");\r\nexports.random = __webpack_require__(/*! ./systems/random */ \"(ssr)/./node_modules/party-js/lib/systems/random.js\");\r\nexports.math = __webpack_require__(/*! ./systems/math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\nexports.util = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/party-js/lib/util/index.js\");\r\n/**\r\n * Forces the initialization of the otherwise lazy scene.\r\n */\r\nfunction forceInit() {\r\n    exports.scene.current;\r\n}\r\nexports.forceInit = forceInit;\r\nexports[\"default\"] = __webpack_require__(/*! ./ */ \"(ssr)/./node_modules/party-js/lib/index.js\");\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/emitter.js":
/*!********************************************************!*\
  !*** ./node_modules/party-js/lib/particles/emitter.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Emitter = void 0;\r\nvar vector_1 = __webpack_require__(/*! ../components/vector */ \"(ssr)/./node_modules/party-js/lib/components/vector.js\");\r\nvar settings_1 = __webpack_require__(/*! ../settings */ \"(ssr)/./node_modules/party-js/lib/settings.js\");\r\nvar variation_1 = __webpack_require__(/*! ../systems/variation */ \"(ssr)/./node_modules/party-js/lib/systems/variation.js\");\r\nvar config_1 = __webpack_require__(/*! ../util/config */ \"(ssr)/./node_modules/party-js/lib/util/config.js\");\r\nvar options_1 = __webpack_require__(/*! ./options */ \"(ssr)/./node_modules/party-js/lib/particles/options/index.js\");\r\nvar particle_1 = __webpack_require__(/*! ./particle */ \"(ssr)/./node_modules/party-js/lib/particles/particle.js\");\r\n/**\r\n * Represents an emitter that is responsible for spawning and updating particles.\r\n *\r\n * Particles themselves are just data-holders, with the system acting upon them and\r\n * modifying them. The modifications are done mainly via modules, that use the\r\n * particle's data together with some function to apply temporal transitions.\r\n *\r\n * @see Particle\r\n * @see ParticleModifierModule\r\n */\r\nvar Emitter = /** @class */ (function () {\r\n    /**\r\n     * Creates a new emitter, using default options.\r\n     */\r\n    function Emitter(options) {\r\n        /**\r\n         * The particles currently contained within the system.\r\n         */\r\n        this.particles = [];\r\n        this.currentLoop = 0; // The current loop index.\r\n        this.durationTimer = 0; // Measures the current runtime duration, to allow loops to reset.\r\n        this.emissionTimer = 0; // Measures the current emission timer, to allow spawning particles in intervals.\r\n        this.attemptedBurstIndices = []; // The indices of the particle bursts that were attempted this loop.\r\n        this.options = config_1.overrideDefaults(options_1.getDefaultEmitterOptions(), options === null || options === void 0 ? void 0 : options.emitterOptions);\r\n        this.emission = config_1.overrideDefaults(options_1.getDefaultEmissionOptions(), options === null || options === void 0 ? void 0 : options.emissionOptions);\r\n        this.renderer = config_1.overrideDefaults(options_1.getDefaultRendererOptions(), options === null || options === void 0 ? void 0 : options.rendererOptions);\r\n    }\r\n    Object.defineProperty(Emitter.prototype, \"isExpired\", {\r\n        /**\r\n         * Checks if the emitter is already expired and can be removed.\r\n         * Expired emitters do not emit new particles.\r\n         */\r\n        get: function () {\r\n            return (this.options.loops >= 0 && this.currentLoop >= this.options.loops);\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Emitter.prototype, \"canRemove\", {\r\n        /**\r\n         * Checks if the emitter can safely be removed.\r\n         * This is true if no more particles are active.\r\n         */\r\n        get: function () {\r\n            return this.particles.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    /**\r\n     * Clears all particles inside the emitter.\r\n     *\r\n     * @returns The number of cleared particles.\r\n     */\r\n    Emitter.prototype.clearParticles = function () {\r\n        return this.particles.splice(0).length;\r\n    };\r\n    /**\r\n     * Processes a tick of the emitter, using the elapsed time.\r\n     *\r\n     * @remarks\r\n     * This handles a few things, namely:\r\n     * - Incrementing the duration timer and potentially incrementing the loop.\r\n     * - Handling particle bursts & emissions.\r\n     * - Despawning particles conditionally.\r\n     *\r\n     * @param delta The time, in seconds, passed since the last tick.\r\n     */\r\n    Emitter.prototype.tick = function (delta) {\r\n        if (!this.isExpired) {\r\n            this.durationTimer += delta;\r\n            if (this.durationTimer >= this.options.duration) {\r\n                this.currentLoop++;\r\n                // To start a new loop, the duration timer and attempted bursts are reset.\r\n                this.durationTimer = 0;\r\n                this.attemptedBurstIndices = [];\r\n            }\r\n            // We need to check the expiry again, in case the added loop or duration changed something.\r\n            if (!this.isExpired) {\r\n                // Iterate over the bursts, attempting to execute them if the time is ready.\r\n                var burstIndex = 0;\r\n                for (var _i = 0, _a = this.emission.bursts; _i < _a.length; _i++) {\r\n                    var burst = _a[_i];\r\n                    if (burst.time <= this.durationTimer) {\r\n                        // Has the burst already been attempted? If not ...\r\n                        if (!this.attemptedBurstIndices.includes(burstIndex)) {\r\n                            // Perform the burst, emitting a variable amount of particles.\r\n                            var count = variation_1.evaluateVariation(burst.count);\r\n                            for (var i = 0; i < count; i++) {\r\n                                this.emitParticle();\r\n                            }\r\n                            // Mark the burst as attempted.\r\n                            this.attemptedBurstIndices.push(burstIndex);\r\n                        }\r\n                    }\r\n                    burstIndex++;\r\n                }\r\n                // Handle the 'emission over time'. By using a while-loop instead of a simple\r\n                // if-condition, we take high deltas into account, and ensure that the correct\r\n                // number of particles will consistently be emitted.\r\n                this.emissionTimer += delta;\r\n                var delay = 1 / this.emission.rate;\r\n                while (this.emissionTimer > delay) {\r\n                    this.emissionTimer -= delay;\r\n                    this.emitParticle();\r\n                }\r\n            }\r\n        }\r\n        var _loop_1 = function (i) {\r\n            var particle = this_1.particles[i];\r\n            this_1.tickParticle(particle, delta);\r\n            // Particles should be despawned (i.e. removed from the collection) if any of\r\n            // the despawning rules apply to them.\r\n            if (this_1.options.despawningRules.some(function (rule) { return rule(particle); })) {\r\n                this_1.particles.splice(i, 1);\r\n            }\r\n        };\r\n        var this_1 = this;\r\n        for (var i = this.particles.length - 1; i >= 0; i--) {\r\n            _loop_1(i);\r\n        }\r\n    };\r\n    /**\r\n     * Performs an internal tick for the particle.\r\n     *\r\n     * @remarks\r\n     * This method controls the particle's lifetime, location and velocity, according\r\n     * to the elapsed delta and the configuration. Additionally, each of the emitter's\r\n     * modules is applied to the particle.\r\n     *\r\n     * @param particle The particle to apply the tick for.\r\n     * @param delta The time, in seconds, passed since the last tick.\r\n     */\r\n    Emitter.prototype.tickParticle = function (particle, delta) {\r\n        particle.lifetime -= delta;\r\n        if (this.options.useGravity) {\r\n            // Apply gravitational acceleration to the particle.\r\n            particle.velocity = particle.velocity.add(vector_1.Vector.up.scale(settings_1.settings.gravity * delta));\r\n        }\r\n        // Apply the particle's velocity to its location.\r\n        particle.location = particle.location.add(particle.velocity.scale(delta));\r\n        // Apply the modules to the particle.\r\n        for (var _i = 0, _a = this.options.modules; _i < _a.length; _i++) {\r\n            var moduleFunction = _a[_i];\r\n            moduleFunction(particle);\r\n        }\r\n    };\r\n    /**\r\n     * Emits a particle using the registered settings.\r\n     * Also may despawn a particle if the maximum number of particles is exceeded.\r\n     */\r\n    Emitter.prototype.emitParticle = function () {\r\n        var particle = new particle_1.Particle({\r\n            location: this.emission.sourceSampler(),\r\n            lifetime: variation_1.evaluateVariation(this.emission.initialLifetime),\r\n            velocity: vector_1.Vector.from2dAngle(variation_1.evaluateVariation(this.emission.angle)).scale(variation_1.evaluateVariation(this.emission.initialSpeed)),\r\n            size: variation_1.evaluateVariation(this.emission.initialSize),\r\n            rotation: variation_1.evaluateVariation(this.emission.initialRotation),\r\n            color: variation_1.evaluateVariation(this.emission.initialColor),\r\n        });\r\n        this.particles.push(particle);\r\n        // Ensure that no more particles than 'maxParticles' can exist.\r\n        if (this.particles.length > this.options.maxParticles) {\r\n            this.particles.shift();\r\n        }\r\n        return particle;\r\n    };\r\n    return Emitter;\r\n}());\r\nexports.Emitter = Emitter;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9lbWl0dGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZixlQUFlLG1CQUFPLENBQUMsb0ZBQXNCO0FBQzdDLGlCQUFpQixtQkFBTyxDQUFDLGtFQUFhO0FBQ3RDLGtCQUFrQixtQkFBTyxDQUFDLG9GQUFzQjtBQUNoRCxlQUFlLG1CQUFPLENBQUMsd0VBQWdCO0FBQ3ZDLGdCQUFnQixtQkFBTyxDQUFDLCtFQUFXO0FBQ25DLGlCQUFpQixtQkFBTyxDQUFDLDJFQUFZO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCLGdDQUFnQztBQUNoQyxnQ0FBZ0M7QUFDaEMseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsZ0JBQWdCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxXQUFXO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNFQUFzRSx3QkFBd0I7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsUUFBUTtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGdCQUFnQjtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGVBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9wYXJ0eS1qcy9saWIvcGFydGljbGVzL2VtaXR0ZXIuanM/NzE1NyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLkVtaXR0ZXIgPSB2b2lkIDA7XHJcbnZhciB2ZWN0b3JfMSA9IHJlcXVpcmUoXCIuLi9jb21wb25lbnRzL3ZlY3RvclwiKTtcclxudmFyIHNldHRpbmdzXzEgPSByZXF1aXJlKFwiLi4vc2V0dGluZ3NcIik7XHJcbnZhciB2YXJpYXRpb25fMSA9IHJlcXVpcmUoXCIuLi9zeXN0ZW1zL3ZhcmlhdGlvblwiKTtcclxudmFyIGNvbmZpZ18xID0gcmVxdWlyZShcIi4uL3V0aWwvY29uZmlnXCIpO1xyXG52YXIgb3B0aW9uc18xID0gcmVxdWlyZShcIi4vb3B0aW9uc1wiKTtcclxudmFyIHBhcnRpY2xlXzEgPSByZXF1aXJlKFwiLi9wYXJ0aWNsZVwiKTtcclxuLyoqXHJcbiAqIFJlcHJlc2VudHMgYW4gZW1pdHRlciB0aGF0IGlzIHJlc3BvbnNpYmxlIGZvciBzcGF3bmluZyBhbmQgdXBkYXRpbmcgcGFydGljbGVzLlxyXG4gKlxyXG4gKiBQYXJ0aWNsZXMgdGhlbXNlbHZlcyBhcmUganVzdCBkYXRhLWhvbGRlcnMsIHdpdGggdGhlIHN5c3RlbSBhY3RpbmcgdXBvbiB0aGVtIGFuZFxyXG4gKiBtb2RpZnlpbmcgdGhlbS4gVGhlIG1vZGlmaWNhdGlvbnMgYXJlIGRvbmUgbWFpbmx5IHZpYSBtb2R1bGVzLCB0aGF0IHVzZSB0aGVcclxuICogcGFydGljbGUncyBkYXRhIHRvZ2V0aGVyIHdpdGggc29tZSBmdW5jdGlvbiB0byBhcHBseSB0ZW1wb3JhbCB0cmFuc2l0aW9ucy5cclxuICpcclxuICogQHNlZSBQYXJ0aWNsZVxyXG4gKiBAc2VlIFBhcnRpY2xlTW9kaWZpZXJNb2R1bGVcclxuICovXHJcbnZhciBFbWl0dGVyID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xyXG4gICAgLyoqXHJcbiAgICAgKiBDcmVhdGVzIGEgbmV3IGVtaXR0ZXIsIHVzaW5nIGRlZmF1bHQgb3B0aW9ucy5cclxuICAgICAqL1xyXG4gICAgZnVuY3Rpb24gRW1pdHRlcihvcHRpb25zKSB7XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogVGhlIHBhcnRpY2xlcyBjdXJyZW50bHkgY29udGFpbmVkIHdpdGhpbiB0aGUgc3lzdGVtLlxyXG4gICAgICAgICAqL1xyXG4gICAgICAgIHRoaXMucGFydGljbGVzID0gW107XHJcbiAgICAgICAgdGhpcy5jdXJyZW50TG9vcCA9IDA7IC8vIFRoZSBjdXJyZW50IGxvb3AgaW5kZXguXHJcbiAgICAgICAgdGhpcy5kdXJhdGlvblRpbWVyID0gMDsgLy8gTWVhc3VyZXMgdGhlIGN1cnJlbnQgcnVudGltZSBkdXJhdGlvbiwgdG8gYWxsb3cgbG9vcHMgdG8gcmVzZXQuXHJcbiAgICAgICAgdGhpcy5lbWlzc2lvblRpbWVyID0gMDsgLy8gTWVhc3VyZXMgdGhlIGN1cnJlbnQgZW1pc3Npb24gdGltZXIsIHRvIGFsbG93IHNwYXduaW5nIHBhcnRpY2xlcyBpbiBpbnRlcnZhbHMuXHJcbiAgICAgICAgdGhpcy5hdHRlbXB0ZWRCdXJzdEluZGljZXMgPSBbXTsgLy8gVGhlIGluZGljZXMgb2YgdGhlIHBhcnRpY2xlIGJ1cnN0cyB0aGF0IHdlcmUgYXR0ZW1wdGVkIHRoaXMgbG9vcC5cclxuICAgICAgICB0aGlzLm9wdGlvbnMgPSBjb25maWdfMS5vdmVycmlkZURlZmF1bHRzKG9wdGlvbnNfMS5nZXREZWZhdWx0RW1pdHRlck9wdGlvbnMoKSwgb3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLmVtaXR0ZXJPcHRpb25zKTtcclxuICAgICAgICB0aGlzLmVtaXNzaW9uID0gY29uZmlnXzEub3ZlcnJpZGVEZWZhdWx0cyhvcHRpb25zXzEuZ2V0RGVmYXVsdEVtaXNzaW9uT3B0aW9ucygpLCBvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMuZW1pc3Npb25PcHRpb25zKTtcclxuICAgICAgICB0aGlzLnJlbmRlcmVyID0gY29uZmlnXzEub3ZlcnJpZGVEZWZhdWx0cyhvcHRpb25zXzEuZ2V0RGVmYXVsdFJlbmRlcmVyT3B0aW9ucygpLCBvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMucmVuZGVyZXJPcHRpb25zKTtcclxuICAgIH1cclxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShFbWl0dGVyLnByb3RvdHlwZSwgXCJpc0V4cGlyZWRcIiwge1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIENoZWNrcyBpZiB0aGUgZW1pdHRlciBpcyBhbHJlYWR5IGV4cGlyZWQgYW5kIGNhbiBiZSByZW1vdmVkLlxyXG4gICAgICAgICAqIEV4cGlyZWQgZW1pdHRlcnMgZG8gbm90IGVtaXQgbmV3IHBhcnRpY2xlcy5cclxuICAgICAgICAgKi9cclxuICAgICAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAgICAgcmV0dXJuICh0aGlzLm9wdGlvbnMubG9vcHMgPj0gMCAmJiB0aGlzLmN1cnJlbnRMb29wID49IHRoaXMub3B0aW9ucy5sb29wcyk7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcclxuICAgICAgICBjb25maWd1cmFibGU6IHRydWVcclxuICAgIH0pO1xyXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KEVtaXR0ZXIucHJvdG90eXBlLCBcImNhblJlbW92ZVwiLCB7XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogQ2hlY2tzIGlmIHRoZSBlbWl0dGVyIGNhbiBzYWZlbHkgYmUgcmVtb3ZlZC5cclxuICAgICAgICAgKiBUaGlzIGlzIHRydWUgaWYgbm8gbW9yZSBwYXJ0aWNsZXMgYXJlIGFjdGl2ZS5cclxuICAgICAgICAgKi9cclxuICAgICAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHRoaXMucGFydGljbGVzLmxlbmd0aCA9PT0gMDtcclxuICAgICAgICB9LFxyXG4gICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxyXG4gICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxyXG4gICAgfSk7XHJcbiAgICAvKipcclxuICAgICAqIENsZWFycyBhbGwgcGFydGljbGVzIGluc2lkZSB0aGUgZW1pdHRlci5cclxuICAgICAqXHJcbiAgICAgKiBAcmV0dXJucyBUaGUgbnVtYmVyIG9mIGNsZWFyZWQgcGFydGljbGVzLlxyXG4gICAgICovXHJcbiAgICBFbWl0dGVyLnByb3RvdHlwZS5jbGVhclBhcnRpY2xlcyA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5wYXJ0aWNsZXMuc3BsaWNlKDApLmxlbmd0aDtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIFByb2Nlc3NlcyBhIHRpY2sgb2YgdGhlIGVtaXR0ZXIsIHVzaW5nIHRoZSBlbGFwc2VkIHRpbWUuXHJcbiAgICAgKlxyXG4gICAgICogQHJlbWFya3NcclxuICAgICAqIFRoaXMgaGFuZGxlcyBhIGZldyB0aGluZ3MsIG5hbWVseTpcclxuICAgICAqIC0gSW5jcmVtZW50aW5nIHRoZSBkdXJhdGlvbiB0aW1lciBhbmQgcG90ZW50aWFsbHkgaW5jcmVtZW50aW5nIHRoZSBsb29wLlxyXG4gICAgICogLSBIYW5kbGluZyBwYXJ0aWNsZSBidXJzdHMgJiBlbWlzc2lvbnMuXHJcbiAgICAgKiAtIERlc3Bhd25pbmcgcGFydGljbGVzIGNvbmRpdGlvbmFsbHkuXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIGRlbHRhIFRoZSB0aW1lLCBpbiBzZWNvbmRzLCBwYXNzZWQgc2luY2UgdGhlIGxhc3QgdGljay5cclxuICAgICAqL1xyXG4gICAgRW1pdHRlci5wcm90b3R5cGUudGljayA9IGZ1bmN0aW9uIChkZWx0YSkge1xyXG4gICAgICAgIGlmICghdGhpcy5pc0V4cGlyZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5kdXJhdGlvblRpbWVyICs9IGRlbHRhO1xyXG4gICAgICAgICAgICBpZiAodGhpcy5kdXJhdGlvblRpbWVyID49IHRoaXMub3B0aW9ucy5kdXJhdGlvbikge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50TG9vcCsrO1xyXG4gICAgICAgICAgICAgICAgLy8gVG8gc3RhcnQgYSBuZXcgbG9vcCwgdGhlIGR1cmF0aW9uIHRpbWVyIGFuZCBhdHRlbXB0ZWQgYnVyc3RzIGFyZSByZXNldC5cclxuICAgICAgICAgICAgICAgIHRoaXMuZHVyYXRpb25UaW1lciA9IDA7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmF0dGVtcHRlZEJ1cnN0SW5kaWNlcyA9IFtdO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIFdlIG5lZWQgdG8gY2hlY2sgdGhlIGV4cGlyeSBhZ2FpbiwgaW4gY2FzZSB0aGUgYWRkZWQgbG9vcCBvciBkdXJhdGlvbiBjaGFuZ2VkIHNvbWV0aGluZy5cclxuICAgICAgICAgICAgaWYgKCF0aGlzLmlzRXhwaXJlZCkge1xyXG4gICAgICAgICAgICAgICAgLy8gSXRlcmF0ZSBvdmVyIHRoZSBidXJzdHMsIGF0dGVtcHRpbmcgdG8gZXhlY3V0ZSB0aGVtIGlmIHRoZSB0aW1lIGlzIHJlYWR5LlxyXG4gICAgICAgICAgICAgICAgdmFyIGJ1cnN0SW5kZXggPSAwO1xyXG4gICAgICAgICAgICAgICAgZm9yICh2YXIgX2kgPSAwLCBfYSA9IHRoaXMuZW1pc3Npb24uYnVyc3RzOyBfaSA8IF9hLmxlbmd0aDsgX2krKykge1xyXG4gICAgICAgICAgICAgICAgICAgIHZhciBidXJzdCA9IF9hW19pXTtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoYnVyc3QudGltZSA8PSB0aGlzLmR1cmF0aW9uVGltZXIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gSGFzIHRoZSBidXJzdCBhbHJlYWR5IGJlZW4gYXR0ZW1wdGVkPyBJZiBub3QgLi4uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5hdHRlbXB0ZWRCdXJzdEluZGljZXMuaW5jbHVkZXMoYnVyc3RJbmRleCkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFBlcmZvcm0gdGhlIGJ1cnN0LCBlbWl0dGluZyBhIHZhcmlhYmxlIGFtb3VudCBvZiBwYXJ0aWNsZXMuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXIgY291bnQgPSB2YXJpYXRpb25fMS5ldmFsdWF0ZVZhcmlhdGlvbihidXJzdC5jb3VudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGNvdW50OyBpKyspIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXRQYXJ0aWNsZSgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gTWFyayB0aGUgYnVyc3QgYXMgYXR0ZW1wdGVkLlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5hdHRlbXB0ZWRCdXJzdEluZGljZXMucHVzaChidXJzdEluZGV4KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBidXJzdEluZGV4Kys7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgdGhlICdlbWlzc2lvbiBvdmVyIHRpbWUnLiBCeSB1c2luZyBhIHdoaWxlLWxvb3AgaW5zdGVhZCBvZiBhIHNpbXBsZVxyXG4gICAgICAgICAgICAgICAgLy8gaWYtY29uZGl0aW9uLCB3ZSB0YWtlIGhpZ2ggZGVsdGFzIGludG8gYWNjb3VudCwgYW5kIGVuc3VyZSB0aGF0IHRoZSBjb3JyZWN0XHJcbiAgICAgICAgICAgICAgICAvLyBudW1iZXIgb2YgcGFydGljbGVzIHdpbGwgY29uc2lzdGVudGx5IGJlIGVtaXR0ZWQuXHJcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXNzaW9uVGltZXIgKz0gZGVsdGE7XHJcbiAgICAgICAgICAgICAgICB2YXIgZGVsYXkgPSAxIC8gdGhpcy5lbWlzc2lvbi5yYXRlO1xyXG4gICAgICAgICAgICAgICAgd2hpbGUgKHRoaXMuZW1pc3Npb25UaW1lciA+IGRlbGF5KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWlzc2lvblRpbWVyIC09IGRlbGF5O1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdFBhcnRpY2xlKCk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgdmFyIF9sb29wXzEgPSBmdW5jdGlvbiAoaSkge1xyXG4gICAgICAgICAgICB2YXIgcGFydGljbGUgPSB0aGlzXzEucGFydGljbGVzW2ldO1xyXG4gICAgICAgICAgICB0aGlzXzEudGlja1BhcnRpY2xlKHBhcnRpY2xlLCBkZWx0YSk7XHJcbiAgICAgICAgICAgIC8vIFBhcnRpY2xlcyBzaG91bGQgYmUgZGVzcGF3bmVkIChpLmUuIHJlbW92ZWQgZnJvbSB0aGUgY29sbGVjdGlvbikgaWYgYW55IG9mXHJcbiAgICAgICAgICAgIC8vIHRoZSBkZXNwYXduaW5nIHJ1bGVzIGFwcGx5IHRvIHRoZW0uXHJcbiAgICAgICAgICAgIGlmICh0aGlzXzEub3B0aW9ucy5kZXNwYXduaW5nUnVsZXMuc29tZShmdW5jdGlvbiAocnVsZSkgeyByZXR1cm4gcnVsZShwYXJ0aWNsZSk7IH0pKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzXzEucGFydGljbGVzLnNwbGljZShpLCAxKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH07XHJcbiAgICAgICAgdmFyIHRoaXNfMSA9IHRoaXM7XHJcbiAgICAgICAgZm9yICh2YXIgaSA9IHRoaXMucGFydGljbGVzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XHJcbiAgICAgICAgICAgIF9sb29wXzEoaSk7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogUGVyZm9ybXMgYW4gaW50ZXJuYWwgdGljayBmb3IgdGhlIHBhcnRpY2xlLlxyXG4gICAgICpcclxuICAgICAqIEByZW1hcmtzXHJcbiAgICAgKiBUaGlzIG1ldGhvZCBjb250cm9scyB0aGUgcGFydGljbGUncyBsaWZldGltZSwgbG9jYXRpb24gYW5kIHZlbG9jaXR5LCBhY2NvcmRpbmdcclxuICAgICAqIHRvIHRoZSBlbGFwc2VkIGRlbHRhIGFuZCB0aGUgY29uZmlndXJhdGlvbi4gQWRkaXRpb25hbGx5LCBlYWNoIG9mIHRoZSBlbWl0dGVyJ3NcclxuICAgICAqIG1vZHVsZXMgaXMgYXBwbGllZCB0byB0aGUgcGFydGljbGUuXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIHBhcnRpY2xlIFRoZSBwYXJ0aWNsZSB0byBhcHBseSB0aGUgdGljayBmb3IuXHJcbiAgICAgKiBAcGFyYW0gZGVsdGEgVGhlIHRpbWUsIGluIHNlY29uZHMsIHBhc3NlZCBzaW5jZSB0aGUgbGFzdCB0aWNrLlxyXG4gICAgICovXHJcbiAgICBFbWl0dGVyLnByb3RvdHlwZS50aWNrUGFydGljbGUgPSBmdW5jdGlvbiAocGFydGljbGUsIGRlbHRhKSB7XHJcbiAgICAgICAgcGFydGljbGUubGlmZXRpbWUgLT0gZGVsdGE7XHJcbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy51c2VHcmF2aXR5KSB7XHJcbiAgICAgICAgICAgIC8vIEFwcGx5IGdyYXZpdGF0aW9uYWwgYWNjZWxlcmF0aW9uIHRvIHRoZSBwYXJ0aWNsZS5cclxuICAgICAgICAgICAgcGFydGljbGUudmVsb2NpdHkgPSBwYXJ0aWNsZS52ZWxvY2l0eS5hZGQodmVjdG9yXzEuVmVjdG9yLnVwLnNjYWxlKHNldHRpbmdzXzEuc2V0dGluZ3MuZ3Jhdml0eSAqIGRlbHRhKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIEFwcGx5IHRoZSBwYXJ0aWNsZSdzIHZlbG9jaXR5IHRvIGl0cyBsb2NhdGlvbi5cclxuICAgICAgICBwYXJ0aWNsZS5sb2NhdGlvbiA9IHBhcnRpY2xlLmxvY2F0aW9uLmFkZChwYXJ0aWNsZS52ZWxvY2l0eS5zY2FsZShkZWx0YSkpO1xyXG4gICAgICAgIC8vIEFwcGx5IHRoZSBtb2R1bGVzIHRvIHRoZSBwYXJ0aWNsZS5cclxuICAgICAgICBmb3IgKHZhciBfaSA9IDAsIF9hID0gdGhpcy5vcHRpb25zLm1vZHVsZXM7IF9pIDwgX2EubGVuZ3RoOyBfaSsrKSB7XHJcbiAgICAgICAgICAgIHZhciBtb2R1bGVGdW5jdGlvbiA9IF9hW19pXTtcclxuICAgICAgICAgICAgbW9kdWxlRnVuY3Rpb24ocGFydGljbGUpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIEVtaXRzIGEgcGFydGljbGUgdXNpbmcgdGhlIHJlZ2lzdGVyZWQgc2V0dGluZ3MuXHJcbiAgICAgKiBBbHNvIG1heSBkZXNwYXduIGEgcGFydGljbGUgaWYgdGhlIG1heGltdW0gbnVtYmVyIG9mIHBhcnRpY2xlcyBpcyBleGNlZWRlZC5cclxuICAgICAqL1xyXG4gICAgRW1pdHRlci5wcm90b3R5cGUuZW1pdFBhcnRpY2xlID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHZhciBwYXJ0aWNsZSA9IG5ldyBwYXJ0aWNsZV8xLlBhcnRpY2xlKHtcclxuICAgICAgICAgICAgbG9jYXRpb246IHRoaXMuZW1pc3Npb24uc291cmNlU2FtcGxlcigpLFxyXG4gICAgICAgICAgICBsaWZldGltZTogdmFyaWF0aW9uXzEuZXZhbHVhdGVWYXJpYXRpb24odGhpcy5lbWlzc2lvbi5pbml0aWFsTGlmZXRpbWUpLFxyXG4gICAgICAgICAgICB2ZWxvY2l0eTogdmVjdG9yXzEuVmVjdG9yLmZyb20yZEFuZ2xlKHZhcmlhdGlvbl8xLmV2YWx1YXRlVmFyaWF0aW9uKHRoaXMuZW1pc3Npb24uYW5nbGUpKS5zY2FsZSh2YXJpYXRpb25fMS5ldmFsdWF0ZVZhcmlhdGlvbih0aGlzLmVtaXNzaW9uLmluaXRpYWxTcGVlZCkpLFxyXG4gICAgICAgICAgICBzaXplOiB2YXJpYXRpb25fMS5ldmFsdWF0ZVZhcmlhdGlvbih0aGlzLmVtaXNzaW9uLmluaXRpYWxTaXplKSxcclxuICAgICAgICAgICAgcm90YXRpb246IHZhcmlhdGlvbl8xLmV2YWx1YXRlVmFyaWF0aW9uKHRoaXMuZW1pc3Npb24uaW5pdGlhbFJvdGF0aW9uKSxcclxuICAgICAgICAgICAgY29sb3I6IHZhcmlhdGlvbl8xLmV2YWx1YXRlVmFyaWF0aW9uKHRoaXMuZW1pc3Npb24uaW5pdGlhbENvbG9yKSxcclxuICAgICAgICB9KTtcclxuICAgICAgICB0aGlzLnBhcnRpY2xlcy5wdXNoKHBhcnRpY2xlKTtcclxuICAgICAgICAvLyBFbnN1cmUgdGhhdCBubyBtb3JlIHBhcnRpY2xlcyB0aGFuICdtYXhQYXJ0aWNsZXMnIGNhbiBleGlzdC5cclxuICAgICAgICBpZiAodGhpcy5wYXJ0aWNsZXMubGVuZ3RoID4gdGhpcy5vcHRpb25zLm1heFBhcnRpY2xlcykge1xyXG4gICAgICAgICAgICB0aGlzLnBhcnRpY2xlcy5zaGlmdCgpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gcGFydGljbGU7XHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIEVtaXR0ZXI7XHJcbn0oKSk7XHJcbmV4cG9ydHMuRW1pdHRlciA9IEVtaXR0ZXI7XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/emitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/options/emissionOptions.js":
/*!************************************************************************!*\
  !*** ./node_modules/party-js/lib/particles/options/emissionOptions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.getDefaultEmissionOptions = void 0;\r\nvar components_1 = __webpack_require__(/*! ../../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar sources_1 = __webpack_require__(/*! ../../systems/sources */ \"(ssr)/./node_modules/party-js/lib/systems/sources.js\");\r\n/**\r\n * Returns the default set of emission options.\r\n */\r\nfunction getDefaultEmissionOptions() {\r\n    return {\r\n        rate: 10,\r\n        angle: 0,\r\n        bursts: [],\r\n        sourceSampler: sources_1.rectSource(components_1.Rect.zero),\r\n        initialLifetime: 5,\r\n        initialSpeed: 5,\r\n        initialSize: 1,\r\n        initialRotation: components_1.Vector.zero,\r\n        initialColor: components_1.Color.white,\r\n    };\r\n}\r\nexports.getDefaultEmissionOptions = getDefaultEmissionOptions;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9vcHRpb25zL2VtaXNzaW9uT3B0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQ0FBaUM7QUFDakMsbUJBQW1CLG1CQUFPLENBQUMsK0VBQWtCO0FBQzdDLGdCQUFnQixtQkFBTyxDQUFDLG1GQUF1QjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi9wYXJ0aWNsZXMvb3B0aW9ucy9lbWlzc2lvbk9wdGlvbnMuanM/ZGYyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLmdldERlZmF1bHRFbWlzc2lvbk9wdGlvbnMgPSB2b2lkIDA7XHJcbnZhciBjb21wb25lbnRzXzEgPSByZXF1aXJlKFwiLi4vLi4vY29tcG9uZW50c1wiKTtcclxudmFyIHNvdXJjZXNfMSA9IHJlcXVpcmUoXCIuLi8uLi9zeXN0ZW1zL3NvdXJjZXNcIik7XHJcbi8qKlxyXG4gKiBSZXR1cm5zIHRoZSBkZWZhdWx0IHNldCBvZiBlbWlzc2lvbiBvcHRpb25zLlxyXG4gKi9cclxuZnVuY3Rpb24gZ2V0RGVmYXVsdEVtaXNzaW9uT3B0aW9ucygpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgICAgcmF0ZTogMTAsXHJcbiAgICAgICAgYW5nbGU6IDAsXHJcbiAgICAgICAgYnVyc3RzOiBbXSxcclxuICAgICAgICBzb3VyY2VTYW1wbGVyOiBzb3VyY2VzXzEucmVjdFNvdXJjZShjb21wb25lbnRzXzEuUmVjdC56ZXJvKSxcclxuICAgICAgICBpbml0aWFsTGlmZXRpbWU6IDUsXHJcbiAgICAgICAgaW5pdGlhbFNwZWVkOiA1LFxyXG4gICAgICAgIGluaXRpYWxTaXplOiAxLFxyXG4gICAgICAgIGluaXRpYWxSb3RhdGlvbjogY29tcG9uZW50c18xLlZlY3Rvci56ZXJvLFxyXG4gICAgICAgIGluaXRpYWxDb2xvcjogY29tcG9uZW50c18xLkNvbG9yLndoaXRlLFxyXG4gICAgfTtcclxufVxyXG5leHBvcnRzLmdldERlZmF1bHRFbWlzc2lvbk9wdGlvbnMgPSBnZXREZWZhdWx0RW1pc3Npb25PcHRpb25zO1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/options/emissionOptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/options/emitterOptions.js":
/*!***********************************************************************!*\
  !*** ./node_modules/party-js/lib/particles/options/emitterOptions.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.getDefaultEmitterOptions = void 0;\r\nvar rules_1 = __webpack_require__(/*! ../../util/rules */ \"(ssr)/./node_modules/party-js/lib/util/rules.js\");\r\n/**\r\n * Returns the default set of emitter options.\r\n */\r\nfunction getDefaultEmitterOptions() {\r\n    return {\r\n        duration: 5,\r\n        loops: 1,\r\n        useGravity: true,\r\n        maxParticles: 300,\r\n        despawningRules: [rules_1.despawningRules.lifetime, rules_1.despawningRules.bounds],\r\n        modules: [],\r\n    };\r\n}\r\nexports.getDefaultEmitterOptions = getDefaultEmitterOptions;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9vcHRpb25zL2VtaXR0ZXJPcHRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdDQUFnQztBQUNoQyxjQUFjLG1CQUFPLENBQUMseUVBQWtCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9vcHRpb25zL2VtaXR0ZXJPcHRpb25zLmpzPzRjNWYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcclxuZXhwb3J0cy5nZXREZWZhdWx0RW1pdHRlck9wdGlvbnMgPSB2b2lkIDA7XHJcbnZhciBydWxlc18xID0gcmVxdWlyZShcIi4uLy4uL3V0aWwvcnVsZXNcIik7XHJcbi8qKlxyXG4gKiBSZXR1cm5zIHRoZSBkZWZhdWx0IHNldCBvZiBlbWl0dGVyIG9wdGlvbnMuXHJcbiAqL1xyXG5mdW5jdGlvbiBnZXREZWZhdWx0RW1pdHRlck9wdGlvbnMoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAgIGR1cmF0aW9uOiA1LFxyXG4gICAgICAgIGxvb3BzOiAxLFxyXG4gICAgICAgIHVzZUdyYXZpdHk6IHRydWUsXHJcbiAgICAgICAgbWF4UGFydGljbGVzOiAzMDAsXHJcbiAgICAgICAgZGVzcGF3bmluZ1J1bGVzOiBbcnVsZXNfMS5kZXNwYXduaW5nUnVsZXMubGlmZXRpbWUsIHJ1bGVzXzEuZGVzcGF3bmluZ1J1bGVzLmJvdW5kc10sXHJcbiAgICAgICAgbW9kdWxlczogW10sXHJcbiAgICB9O1xyXG59XHJcbmV4cG9ydHMuZ2V0RGVmYXVsdEVtaXR0ZXJPcHRpb25zID0gZ2V0RGVmYXVsdEVtaXR0ZXJPcHRpb25zO1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/options/emitterOptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/options/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/party-js/lib/particles/options/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\n__exportStar(__webpack_require__(/*! ./emitterOptions */ \"(ssr)/./node_modules/party-js/lib/particles/options/emitterOptions.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./emissionOptions */ \"(ssr)/./node_modules/party-js/lib/particles/options/emissionOptions.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./renderOptions */ \"(ssr)/./node_modules/party-js/lib/particles/options/renderOptions.js\"), exports);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9vcHRpb25zL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsK0ZBQWtCO0FBQ3ZDLGFBQWEsbUJBQU8sQ0FBQyxpR0FBbUI7QUFDeEMsYUFBYSxtQkFBTyxDQUFDLDZGQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi9wYXJ0aWNsZXMvb3B0aW9ucy9pbmRleC5qcz9iNzcwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xyXG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XHJcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xyXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH0pO1xyXG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xyXG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcclxuICAgIG9bazJdID0gbVtrXTtcclxufSkpO1xyXG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcclxuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcclxufTtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZW1pdHRlck9wdGlvbnNcIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZW1pc3Npb25PcHRpb25zXCIpLCBleHBvcnRzKTtcclxuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3JlbmRlck9wdGlvbnNcIiksIGV4cG9ydHMpO1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/options/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/options/renderOptions.js":
/*!**********************************************************************!*\
  !*** ./node_modules/party-js/lib/particles/options/renderOptions.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.getDefaultRendererOptions = void 0;\r\n/**\r\n * Returns the default set of renderer options.\r\n */\r\nfunction getDefaultRendererOptions() {\r\n    return {\r\n        shapeFactory: \"square\",\r\n        applyColor: defaultApplyColor,\r\n        applyOpacity: defaultApplyOpacity,\r\n        applyLighting: defaultApplyLighting,\r\n        applyTransform: defaultApplyTransform,\r\n    };\r\n}\r\nexports.getDefaultRendererOptions = getDefaultRendererOptions;\r\n/**\r\n * Applies the specified color to the element.\r\n *\r\n * @remarks\r\n * This function is aware of the element's node type:\r\n * - `div` elements have their `background` set.\r\n * - `svg` elements have their `fill` and `color` set.\r\n * - Other elements have their `color` set.\r\n */\r\nfunction defaultApplyColor(color, element) {\r\n    var hex = color.toHex();\r\n    // Note that by default, HTML node names are uppercase.\r\n    switch (element.nodeName.toLowerCase()) {\r\n        case \"div\":\r\n            element.style.background = hex;\r\n            break;\r\n        case \"svg\":\r\n            element.style.fill = element.style.color = hex;\r\n            break;\r\n        default:\r\n            element.style.color = hex;\r\n            break;\r\n    }\r\n}\r\n/**\r\n * Applies the specified opacity to the element.\r\n */\r\nfunction defaultApplyOpacity(opacity, element) {\r\n    element.style.opacity = opacity.toString();\r\n}\r\n/**\r\n * Applies the specified lighting to the element as a brightness filter.\r\n *\r\n * @remarks\r\n * This function assumes an ambient light with intensity 0.5, and that the\r\n * particle should be lit from both sides. The brightness filter can exceed 1,\r\n * to give the particles a \"glossy\" feel.\r\n */\r\nfunction defaultApplyLighting(lighting, element) {\r\n    element.style.filter = \"brightness(\" + (0.5 + Math.abs(lighting)) + \")\";\r\n}\r\n/**\r\n * Applies the specified transform to the element as a 3D CSS transform.\r\n * Also takes into account the current window scroll, to make sure that particles are\r\n * rendered inside of the fixed container.\r\n */\r\nfunction defaultApplyTransform(particle, element) {\r\n    element.style.transform =\r\n        // Make sure to take window scrolling into account.\r\n        \"translateX(\" + (particle.location.x - window.scrollX).toFixed(3) + \"px) \" +\r\n            (\"translateY(\" + (particle.location.y - window.scrollY).toFixed(3) + \"px) \") +\r\n            (\"translateZ(\" + particle.location.z.toFixed(3) + \"px) \") +\r\n            (\"rotateX(\" + particle.rotation.x.toFixed(3) + \"deg) \") +\r\n            (\"rotateY(\" + particle.rotation.y.toFixed(3) + \"deg) \") +\r\n            (\"rotateZ(\" + particle.rotation.z.toFixed(3) + \"deg) \") +\r\n            (\"scale(\" + particle.size.toFixed(3) + \")\");\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/options/renderOptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/particle.js":
/*!*********************************************************!*\
  !*** ./node_modules/party-js/lib/particles/particle.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Particle = void 0;\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar config_1 = __webpack_require__(/*! ../util/config */ \"(ssr)/./node_modules/party-js/lib/util/config.js\");\r\n/**\r\n * Represents an emitted particle.\r\n */\r\nvar Particle = /** @class */ (function () {\r\n    /**\r\n     * Creates a new particle instance through the specified options.\r\n     */\r\n    function Particle(options) {\r\n        var populatedOptions = config_1.overrideDefaults({\r\n            lifetime: 0,\r\n            size: 1,\r\n            location: components_1.Vector.zero,\r\n            rotation: components_1.Vector.zero,\r\n            velocity: components_1.Vector.zero,\r\n            color: components_1.Color.white,\r\n            opacity: 1,\r\n        }, options);\r\n        // Generate a symbolic ID.\r\n        this.id = Symbol();\r\n        // Assign various properties, together with some initials for later reference.\r\n        this.size = this.initialSize = populatedOptions.size;\r\n        this.lifetime = this.initialLifetime = populatedOptions.lifetime;\r\n        this.rotation = this.initialRotation = populatedOptions.rotation;\r\n        this.location = populatedOptions.location;\r\n        this.velocity = populatedOptions.velocity;\r\n        this.color = populatedOptions.color;\r\n        this.opacity = populatedOptions.opacity;\r\n    }\r\n    return Particle;\r\n}());\r\nexports.Particle = Particle;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9wYXJ0aWNsZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0I7QUFDaEIsbUJBQW1CLG1CQUFPLENBQUMsNEVBQWU7QUFDMUMsZUFBZSxtQkFBTyxDQUFDLHdFQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsZ0JBQWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3BhcnRpY2xlcy9wYXJ0aWNsZS5qcz8yZjRmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xyXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XHJcbmV4cG9ydHMuUGFydGljbGUgPSB2b2lkIDA7XHJcbnZhciBjb21wb25lbnRzXzEgPSByZXF1aXJlKFwiLi4vY29tcG9uZW50c1wiKTtcclxudmFyIGNvbmZpZ18xID0gcmVxdWlyZShcIi4uL3V0aWwvY29uZmlnXCIpO1xyXG4vKipcclxuICogUmVwcmVzZW50cyBhbiBlbWl0dGVkIHBhcnRpY2xlLlxyXG4gKi9cclxudmFyIFBhcnRpY2xlID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xyXG4gICAgLyoqXHJcbiAgICAgKiBDcmVhdGVzIGEgbmV3IHBhcnRpY2xlIGluc3RhbmNlIHRocm91Z2ggdGhlIHNwZWNpZmllZCBvcHRpb25zLlxyXG4gICAgICovXHJcbiAgICBmdW5jdGlvbiBQYXJ0aWNsZShvcHRpb25zKSB7XHJcbiAgICAgICAgdmFyIHBvcHVsYXRlZE9wdGlvbnMgPSBjb25maWdfMS5vdmVycmlkZURlZmF1bHRzKHtcclxuICAgICAgICAgICAgbGlmZXRpbWU6IDAsXHJcbiAgICAgICAgICAgIHNpemU6IDEsXHJcbiAgICAgICAgICAgIGxvY2F0aW9uOiBjb21wb25lbnRzXzEuVmVjdG9yLnplcm8sXHJcbiAgICAgICAgICAgIHJvdGF0aW9uOiBjb21wb25lbnRzXzEuVmVjdG9yLnplcm8sXHJcbiAgICAgICAgICAgIHZlbG9jaXR5OiBjb21wb25lbnRzXzEuVmVjdG9yLnplcm8sXHJcbiAgICAgICAgICAgIGNvbG9yOiBjb21wb25lbnRzXzEuQ29sb3Iud2hpdGUsXHJcbiAgICAgICAgICAgIG9wYWNpdHk6IDEsXHJcbiAgICAgICAgfSwgb3B0aW9ucyk7XHJcbiAgICAgICAgLy8gR2VuZXJhdGUgYSBzeW1ib2xpYyBJRC5cclxuICAgICAgICB0aGlzLmlkID0gU3ltYm9sKCk7XHJcbiAgICAgICAgLy8gQXNzaWduIHZhcmlvdXMgcHJvcGVydGllcywgdG9nZXRoZXIgd2l0aCBzb21lIGluaXRpYWxzIGZvciBsYXRlciByZWZlcmVuY2UuXHJcbiAgICAgICAgdGhpcy5zaXplID0gdGhpcy5pbml0aWFsU2l6ZSA9IHBvcHVsYXRlZE9wdGlvbnMuc2l6ZTtcclxuICAgICAgICB0aGlzLmxpZmV0aW1lID0gdGhpcy5pbml0aWFsTGlmZXRpbWUgPSBwb3B1bGF0ZWRPcHRpb25zLmxpZmV0aW1lO1xyXG4gICAgICAgIHRoaXMucm90YXRpb24gPSB0aGlzLmluaXRpYWxSb3RhdGlvbiA9IHBvcHVsYXRlZE9wdGlvbnMucm90YXRpb247XHJcbiAgICAgICAgdGhpcy5sb2NhdGlvbiA9IHBvcHVsYXRlZE9wdGlvbnMubG9jYXRpb247XHJcbiAgICAgICAgdGhpcy52ZWxvY2l0eSA9IHBvcHVsYXRlZE9wdGlvbnMudmVsb2NpdHk7XHJcbiAgICAgICAgdGhpcy5jb2xvciA9IHBvcHVsYXRlZE9wdGlvbnMuY29sb3I7XHJcbiAgICAgICAgdGhpcy5vcGFjaXR5ID0gcG9wdWxhdGVkT3B0aW9ucy5vcGFjaXR5O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIFBhcnRpY2xlO1xyXG59KCkpO1xyXG5leHBvcnRzLlBhcnRpY2xlID0gUGFydGljbGU7XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/particle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/particles/renderer.js":
/*!*********************************************************!*\
  !*** ./node_modules/party-js/lib/particles/renderer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Renderer = void 0;\r\nvar __1 = __webpack_require__(/*! .. */ \"(ssr)/./node_modules/party-js/lib/index.js\");\r\nvar vector_1 = __webpack_require__(/*! ../components/vector */ \"(ssr)/./node_modules/party-js/lib/components/vector.js\");\r\nvar containers_1 = __webpack_require__(/*! ../containers */ \"(ssr)/./node_modules/party-js/lib/containers.js\");\r\nvar shapes_1 = __webpack_require__(/*! ../systems/shapes */ \"(ssr)/./node_modules/party-js/lib/systems/shapes.js\");\r\nvar util_1 = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/party-js/lib/util/index.js\");\r\n/**\r\n * Represents a renderer used to draw particles to the DOM via HTML\r\n * elements. Additionally, it is responsible for purging the elements\r\n * of destroyed particles from the DOM.\r\n */\r\nvar Renderer = /** @class */ (function () {\r\n    function Renderer() {\r\n        /**\r\n         * The lookup of elements currently handled by the renderer, with the\r\n         * particle ID as key and a HTMLElement as the value.\r\n         */\r\n        this.elements = new Map();\r\n        /**\r\n         * The normalized direction the light comes from.\r\n         */\r\n        this.light = new vector_1.Vector(0, 0, 1);\r\n        /**\r\n         * Whether or not the renderer should actually draw particles.\r\n         */\r\n        this.enabled = true;\r\n        // Respect that users might prefer reduced motion.\r\n        // See: https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion\r\n        this.enabled =\r\n            !__1.settings.respectReducedMotion ||\r\n                !window.matchMedia(\"(prefers-reduced-motion)\").matches;\r\n    }\r\n    /**\r\n     * Begins a new render block. During the rendering phase, a list of rendered particles\r\n     * is tracked, so that stale particles can be removed later.\r\n     */\r\n    Renderer.prototype.begin = function () {\r\n        this.renderedParticles = [];\r\n    };\r\n    /**\r\n     * Terminates an existing render block. This checks which particles were rendered\r\n     * during the block and purges all unused HTMLElements from the DOM.\r\n     *\r\n     * @returns The amount of particles that were rendered.\r\n     */\r\n    Renderer.prototype.end = function () {\r\n        var it = this.elements.keys();\r\n        var result = it.next();\r\n        while (!result.done) {\r\n            var id = result.value;\r\n            if (!this.renderedParticles.includes(id)) {\r\n                this.elements.get(id).remove();\r\n                this.elements.delete(id);\r\n            }\r\n            result = it.next();\r\n        }\r\n        return this.renderedParticles.length;\r\n    };\r\n    /**\r\n     * Renders an individual particle to the DOM. If the particle is rendered for the first\r\n     * time, a HTMLElement will be created using the emitter's render settings.\r\n     *\r\n     * @param particle The particle to be rendered.\r\n     * @param emitter The system containing the particle.\r\n     */\r\n    Renderer.prototype.renderParticle = function (particle, emitter) {\r\n        if (!this.enabled)\r\n            return;\r\n        var options = emitter.renderer;\r\n        // Ensure that an element for the particle exists.\r\n        var element = this.elements.has(particle.id)\r\n            ? this.elements.get(particle.id)\r\n            : this.createParticleElement(particle, options);\r\n        if (options.applyColor) {\r\n            // If the options offer a coloring method, apply it.\r\n            options.applyColor(particle.color, element);\r\n        }\r\n        if (options.applyOpacity) {\r\n            // If the options offer an opacity modifying method, apply it.\r\n            options.applyOpacity(particle.opacity, element);\r\n        }\r\n        if (options.applyLighting) {\r\n            // If the options offer a lighting method, apply it.\r\n            // Lighting is calculated as a combination of the particle's normal\r\n            // direction and the lighting direction.\r\n            var normal = util_1.rotationToNormal(particle.rotation);\r\n            var lightingCoefficient = normal.dot(this.light);\r\n            options.applyLighting(lightingCoefficient, element);\r\n        }\r\n        if (options.applyTransform) {\r\n            // If the options offer a transformation method, apply it.\r\n            // This ensures the particle is rendered at the correct position with the correct rotation.\r\n            options.applyTransform(particle, element);\r\n        }\r\n        // Mark the particle as rendered.\r\n        this.renderedParticles.push(particle.id);\r\n    };\r\n    /**\r\n     * Creates the HTMLElement for a particle that does not have one already.\r\n     */\r\n    Renderer.prototype.createParticleElement = function (particle, options) {\r\n        // Resolve the element returned from the factory.\r\n        var resolved = shapes_1.resolveShapeFactory(options.shapeFactory);\r\n        // Clone the node to ensure we do not break existing elements.\r\n        var element = resolved.cloneNode(true);\r\n        // Ensure that the elements can be \"stacked\" ontop of eachother.\r\n        element.style.position = \"absolute\";\r\n        // Register the new element in the map, while appending the new element to the DOM.\r\n        this.elements.set(particle.id, containers_1.particleContainer.current.appendChild(element));\r\n        return element;\r\n    };\r\n    return Renderer;\r\n}());\r\nexports.Renderer = Renderer;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/particles/renderer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/scene.js":
/*!********************************************!*\
  !*** ./node_modules/party-js/lib/scene.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Scene = void 0;\r\nvar debug_1 = __webpack_require__(/*! ./debug */ \"(ssr)/./node_modules/party-js/lib/debug.js\");\r\nvar emitter_1 = __webpack_require__(/*! ./particles/emitter */ \"(ssr)/./node_modules/party-js/lib/particles/emitter.js\");\r\nvar renderer_1 = __webpack_require__(/*! ./particles/renderer */ \"(ssr)/./node_modules/party-js/lib/particles/renderer.js\");\r\n/**\r\n * Represents a scene that contains emitters and their particles.\r\n *\r\n * Scenes are responsible for spawning and updating emitters, and\r\n * removing them once they are done.\r\n *\r\n * Scenes are not explicitely present in the DOM as an element, only\r\n * the contained particles are.\r\n */\r\nvar Scene = /** @class */ (function () {\r\n    /**\r\n     * Initializes a new scene and starts the ticking job.\r\n     */\r\n    function Scene() {\r\n        /**\r\n         * The emitters currently present in the scene.\r\n         */\r\n        this.emitters = [];\r\n        /**\r\n         * The debug instance associated with the scene.\r\n         */\r\n        this.debug = new debug_1.Debug(this);\r\n        /**\r\n         * The renderer associated with the scene.\r\n         */\r\n        this.renderer = new renderer_1.Renderer();\r\n        /**\r\n         * The ID of the currently scheduled tick.\r\n         */\r\n        this.scheduledTickId = undefined;\r\n        /**\r\n         * The timestamp of the last tick, used to calculate deltas.\r\n         *\r\n         * @initialValue `performance.now()` (time origin)\r\n         * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMHighResTimeStamp\r\n         */\r\n        this.lastTickTimestamp = performance.now();\r\n        // Ensure the scene context is preserved on the tick.\r\n        this.tick = this.tick.bind(this);\r\n        this.scheduleTick();\r\n    }\r\n    /**\r\n     * Creates and returns a new, default emitter object.\r\n     */\r\n    Scene.prototype.createEmitter = function (options) {\r\n        var emitter = new emitter_1.Emitter(options);\r\n        this.emitters.push(emitter);\r\n        return emitter;\r\n    };\r\n    /**\r\n     * Clears all emitters from the scene.\r\n     *\r\n     * @returns The number of cleared emitters.\r\n     */\r\n    Scene.prototype.clearEmitters = function () {\r\n        return this.emitters.splice(0).length;\r\n    };\r\n    /**\r\n     * Clears the particles from all emitters in the scene.\r\n     * Note that this does not remove the actual emitter objects though.\r\n     *\r\n     * @returns The number of cleared particles.\r\n     */\r\n    Scene.prototype.clearParticles = function () {\r\n        return this.emitters.reduce(function (sum, emitter) { return sum + emitter.clearParticles(); }, 0);\r\n    };\r\n    /**\r\n     * Schedules a tick in the scene.\r\n     */\r\n    Scene.prototype.scheduleTick = function () {\r\n        this.scheduledTickId = window.requestAnimationFrame(this.tick);\r\n    };\r\n    /**\r\n     * Cancels a pending tick operation.\r\n     */\r\n    Scene.prototype.cancelTick = function () {\r\n        window.cancelAnimationFrame(this.scheduledTickId);\r\n    };\r\n    /**\r\n     * Processes a tick cycle, updating all emitters contained in the scene.\r\n     * This is handled as a JS animation frame event, hence the passed timestamp.\r\n     *\r\n     * @remarks\r\n     * The emitter ticking and particle rendering is run using try-catch blocks,\r\n     * to ensure that we can recover from potential errors.\r\n     *\r\n     * @param timestamp The current timestamp of the animation frame.\r\n     */\r\n    Scene.prototype.tick = function (timestamp) {\r\n        // Calculate the elapsed delta and convert it to seconds.\r\n        var delta = (timestamp - this.lastTickTimestamp) / 1000;\r\n        try {\r\n            // Perform ticks for all the emitters in the scene.\r\n            for (var i = 0; i < this.emitters.length; i++) {\r\n                var emitter = this.emitters[i];\r\n                emitter.tick(delta);\r\n                if (emitter.isExpired && emitter.canRemove) {\r\n                    this.emitters.splice(i--, 1);\r\n                }\r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error(\"An error occurred while updating the scene's emitters:\\n\\\"\" + error + \"\\\"\");\r\n        }\r\n        try {\r\n            // Instruct the renderer to draw the particles of all systems.\r\n            this.renderer.begin();\r\n            for (var _i = 0, _a = this.emitters; _i < _a.length; _i++) {\r\n                var emitter = _a[_i];\r\n                for (var _b = 0, _c = emitter.particles; _b < _c.length; _b++) {\r\n                    var particle = _c[_b];\r\n                    this.renderer.renderParticle(particle, emitter);\r\n                }\r\n            }\r\n            this.renderer.end();\r\n        }\r\n        catch (error) {\r\n            console.error(\"An error occurred while rendering the scene's particles:\\n\\\"\" + error + \"\\\"\");\r\n        }\r\n        // Perform a tick on the debug interface\r\n        this.debug.tick(delta);\r\n        // Save the timestamp as the last tick timestamp and schedule a new tick.\r\n        this.lastTickTimestamp = timestamp;\r\n        this.scheduleTick();\r\n    };\r\n    return Scene;\r\n}());\r\nexports.Scene = Scene;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/scene.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/settings.js":
/*!***********************************************!*\
  !*** ./node_modules/party-js/lib/settings.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.settings = void 0;\r\nexports.settings = {\r\n    debug: false,\r\n    gravity: 800,\r\n    zIndex: 99999,\r\n    respectReducedMotion: true,\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3NldHRpbmdzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdCQUFnQjtBQUNoQixnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi9zZXR0aW5ncy5qcz8yZTJiIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xyXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XHJcbmV4cG9ydHMuc2V0dGluZ3MgPSB2b2lkIDA7XHJcbmV4cG9ydHMuc2V0dGluZ3MgPSB7XHJcbiAgICBkZWJ1ZzogZmFsc2UsXHJcbiAgICBncmF2aXR5OiA4MDAsXHJcbiAgICB6SW5kZXg6IDk5OTk5LFxyXG4gICAgcmVzcGVjdFJlZHVjZWRNb3Rpb246IHRydWUsXHJcbn07XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/settings.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/systems/math.js":
/*!***************************************************!*\
  !*** ./node_modules/party-js/lib/systems/math.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.approximately = exports.clamp = exports.invlerp = exports.slerp = exports.lerp = exports.epsilon = exports.rad2deg = exports.deg2rad = void 0;\r\n/**\r\n * Constant coefficient to convert degrees to radians.\r\n */\r\nexports.deg2rad = Math.PI / 180;\r\n/**\r\n * Constant coefficient to convert radians to degrees.\r\n */\r\nexports.rad2deg = 180 / Math.PI;\r\n/**\r\n * A small value to approximately compare values.\r\n */\r\nexports.epsilon = 0.000001;\r\n/**\r\n * Linearly interpolates between a and b by t.\r\n */\r\nfunction lerp(a, b, t) {\r\n    return (1 - t) * a + t * b;\r\n}\r\nexports.lerp = lerp;\r\n/**\r\n * Smoothly interpolates between a and b by t (using cosine interpolation).\r\n */\r\nfunction slerp(a, b, t) {\r\n    return lerp(a, b, (1 - Math.cos(t * Math.PI)) / 2);\r\n}\r\nexports.slerp = slerp;\r\n/**\r\n * Inversely lerps v between a and b to find t.\r\n */\r\nfunction invlerp(a, b, v) {\r\n    return (v - a) / (b - a);\r\n}\r\nexports.invlerp = invlerp;\r\n/**\r\n * Clamps the specified value between a minimum and a maximum.\r\n */\r\nfunction clamp(value, min, max) {\r\n    return Math.min(max, Math.max(min, value));\r\n}\r\nexports.clamp = clamp;\r\n/**\r\n * Checks if a is approximately equal to b.\r\n */\r\nfunction approximately(a, b) {\r\n    return Math.abs(a - b) < exports.epsilon;\r\n}\r\nexports.approximately = approximately;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/systems/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/systems/modules.js":
/*!******************************************************!*\
  !*** ./node_modules/party-js/lib/systems/modules.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.ModuleBuilder = void 0;\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\n/**\r\n * Represents a builder for particle modules. Returns an evaluatable module\r\n * function, that can be consumed by emitters.\r\n *\r\n * @remarks\r\n * Not all properties can be driven. TypeScript will validate this at compile time,\r\n * but no internal validation is performed due to performance reasons. Also, note\r\n * that the driving factor is \"lifetime\" by default.\r\n *\r\n * @example\r\n * ```ts\r\n * new ModuleBuilder()\r\n *     .drive(\"size\")\r\n *     .by((t) => t * 2)\r\n *     .through(\"lifetime\")\r\n *     .build();\r\n * ```\r\n */\r\nvar ModuleBuilder = /** @class */ (function () {\r\n    function ModuleBuilder() {\r\n        /**\r\n         * The factor driving the built function.\r\n         *\r\n         * @defaultValue \"lifetime\"\r\n         */\r\n        this.factor = \"lifetime\";\r\n        this.isRelative = false;\r\n    }\r\n    /**\r\n     * Specifies the key in the particle that should be driven.\r\n     *\r\n     * @remarks\r\n     * Note that not all of a particle's properties are drivable through modules. If you\r\n     * need full control of a particle inside of a module, you can use a module function directly.\r\n     *\r\n     * @returns The chained builder instance.\r\n     */\r\n    ModuleBuilder.prototype.drive = function (key) {\r\n        this.driverKey = key;\r\n        return this;\r\n    };\r\n    /**\r\n     * Specifies the factor to drive the evaluated value by. Supports \"lifetime\" and \"size\".\r\n     *\r\n     * @returns The chained builder instance.\r\n     */\r\n    ModuleBuilder.prototype.through = function (factor) {\r\n        this.factor = factor;\r\n        return this;\r\n    };\r\n    /**\r\n     * Specifies the value to drive the module behaviour by. This can be a constant,\r\n     * a spline or an evaluable function. Note that in the last case, the driving\r\n     * factor is passed as a parameter.\r\n     *\r\n     * @returns The chained builder instance.\r\n     */\r\n    ModuleBuilder.prototype.by = function (driver) {\r\n        this.driverValue = driver;\r\n        return this;\r\n    };\r\n    /**\r\n     * Specifies that the module function is supposed to act relative to the\r\n     * properties initial value.\r\n     *\r\n     * @remarks\r\n     * Note that this is only possible if an \"initial*\" property exists on the\r\n     * particle object. The operation applied to the initial and new value\r\n     * is dependant on their type:\r\n     * - `Vector`: Both vectors are added.\r\n     * - `number`: Both numbers are multiplied.\r\n     *\r\n     * For more advanced relative customizations, consider using the particle\r\n     * object in the driver value function instead, like:\r\n     * ```ts\r\n     * .by((t, p) => p.initialSize + t * 2);\r\n     * ```\r\n     */\r\n    ModuleBuilder.prototype.relative = function (isRelative) {\r\n        if (isRelative === void 0) { isRelative = true; }\r\n        this.isRelative = isRelative;\r\n        return this;\r\n    };\r\n    /**\r\n     * Consumes the builder and returns an evaluatable module function.\r\n     *\r\n     * @remarks\r\n     * Note that you need to specify the driving key and value, otherwise an error\r\n     * will be thrown.\r\n     */\r\n    ModuleBuilder.prototype.build = function () {\r\n        var _this = this;\r\n        if (typeof this.driverKey === \"undefined\") {\r\n            throw new Error(\"No driving key was provided in the module builder. Did you forget a '.drive()' call?\");\r\n        }\r\n        if (typeof this.driverValue === \"undefined\") {\r\n            throw new Error(\"No driving value was provided in the module builder. Did you forget a '.through()' call?\");\r\n        }\r\n        return function (particle) {\r\n            updateDrivenProperty(particle, _this.driverKey, evaluateModuleDriver(_this.driverValue, calculateModuleFactor(_this.factor, particle), particle), _this.isRelative);\r\n        };\r\n    };\r\n    return ModuleBuilder;\r\n}());\r\nexports.ModuleBuilder = ModuleBuilder;\r\n/**\r\n * Evaluates the module driver using a specified factor.\r\n */\r\nfunction evaluateModuleDriver(driver, factor, particle) {\r\n    if (typeof driver === \"object\" && \"evaluate\" in driver) {\r\n        return driver.evaluate(factor);\r\n    }\r\n    if (typeof driver === \"function\") {\r\n        return driver(factor, particle);\r\n    }\r\n    return driver;\r\n}\r\n/**\r\n * Calculates a module factor using a specified particle as context.\r\n */\r\nfunction calculateModuleFactor(factor, particle) {\r\n    switch (factor) {\r\n        case \"lifetime\":\r\n            return particle.initialLifetime - particle.lifetime;\r\n        case \"relativeLifetime\":\r\n            return ((particle.initialLifetime - particle.lifetime) /\r\n                particle.initialLifetime);\r\n        case \"size\":\r\n            return particle.size;\r\n        default:\r\n            throw new Error(\"Invalid driving factor '\" + factor + \"'.\");\r\n    }\r\n}\r\n/**\r\n * Updates a driven property of a particle using the specified value.\r\n *\r\n * @remarks\r\n * If the operation is marked as relative, the function infers the new value\r\n * through the value's type. Note that relative properties must have a\r\n * corresponding \"initial*\" value in the particle's properties.\r\n */\r\nfunction updateDrivenProperty(particle, key, value, relative) {\r\n    if (relative === void 0) { relative = false; }\r\n    if (!relative) {\r\n        particle[key] = value;\r\n    }\r\n    else {\r\n        var initial = particle[\"initial\" + key[0].toUpperCase() + key.substr(1)];\r\n        if (typeof initial === \"undefined\") {\r\n            throw new Error(\"Unable to use relative chaining with key '\" + key + \"'; no initial value exists.\");\r\n        }\r\n        if (value instanceof components_1.Vector) {\r\n            updateDrivenProperty(particle, key, initial.add(value));\r\n        }\r\n        else if (typeof value === \"number\") {\r\n            updateDrivenProperty(particle, key, initial * value);\r\n        }\r\n        else {\r\n            throw new Error(\"Unable to use relative chaining with particle key '\" + key + \"'; no relative operation for '\" + value + \"' could be inferred.\");\r\n        }\r\n    }\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/systems/modules.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/systems/random.js":
/*!*****************************************************!*\
  !*** ./node_modules/party-js/lib/systems/random.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.randomInsideCircle = exports.randomInsideRect = exports.randomUnitVector = exports.pick = exports.randomRange = void 0;\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar math_1 = __webpack_require__(/*! ./math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\n/**\r\n * Returns a random value from min to max.\r\n */\r\nfunction randomRange(min, max) {\r\n    if (min === void 0) { min = 0; }\r\n    if (max === void 0) { max = 1; }\r\n    return math_1.lerp(min, max, Math.random());\r\n}\r\nexports.randomRange = randomRange;\r\n/**\r\n * Picks a random element from the specified array. Returns undefined if the array is empty.\r\n */\r\nfunction pick(arr) {\r\n    return arr.length === 0\r\n        ? undefined\r\n        : arr[Math.floor(Math.random() * arr.length)];\r\n}\r\nexports.pick = pick;\r\n/**\r\n * Returns a random unit vector.\r\n */\r\nfunction randomUnitVector() {\r\n    var theta = randomRange(0, 2 * Math.PI);\r\n    var z = randomRange(-1, 1);\r\n    return new components_1.Vector(Math.sqrt(1 - z * z) * Math.cos(theta), Math.sqrt(1 - z * z) * Math.sin(theta), z);\r\n}\r\nexports.randomUnitVector = randomUnitVector;\r\n/**\r\n * Returns a random point inside the given rect.\r\n */\r\nfunction randomInsideRect(rect) {\r\n    return new components_1.Vector(rect.x + randomRange(0, rect.width), rect.y + randomRange(0, rect.height));\r\n}\r\nexports.randomInsideRect = randomInsideRect;\r\nfunction randomInsideCircle(circle) {\r\n    var theta = randomRange(0, 2 * Math.PI);\r\n    var radius = randomRange(0, circle.radius);\r\n    return new components_1.Vector(circle.x + Math.cos(theta) * radius, circle.y + Math.sin(theta) * radius);\r\n}\r\nexports.randomInsideCircle = randomInsideCircle;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/systems/random.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/systems/shapes.js":
/*!*****************************************************!*\
  !*** ./node_modules/party-js/lib/systems/shapes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.resolveShapeFactory = exports.resolvableShapes = void 0;\r\nvar variation_1 = __webpack_require__(/*! ./variation */ \"(ssr)/./node_modules/party-js/lib/systems/variation.js\");\r\n/**\r\n * Represents the lookup that maps resolveable element keys to their HTML strings.\r\n *\r\n * @remarks\r\n * The default shapes are made to fit inside a dimension of 10x10 pixels, except\r\n * the 'star' shape, which exceeds it slightly.\r\n */\r\nexports.resolvableShapes = {\r\n    square: \"<div style=\\\"height: 10px; width: 10px;\\\"></div>\",\r\n    rectangle: \"<div style=\\\"height: 6px; width: 10px;\\\"></div>\",\r\n    circle: \"<svg viewBox=\\\"0 0 2 2\\\" width=\\\"10\\\" height=\\\"10\\\"><circle cx=\\\"1\\\" cy=\\\"1\\\" r=\\\"1\\\" fill=\\\"currentColor\\\"/></svg>\",\r\n    roundedSquare: \"<div style=\\\"height: 10px; width: 10px; border-radius: 3px;\\\"></div>\",\r\n    roundedRectangle: \"<div style=\\\"height: 6px; width: 10px; border-radius: 3px;\\\"></div>\",\r\n    star: \"<svg viewBox=\\\"0 0 512 512\\\" width=\\\"15\\\" height=\\\"15\\\"><polygon fill=\\\"currentColor\\\" points=\\\"512,197.816 325.961,185.585 255.898,9.569 185.835,185.585 0,197.816 142.534,318.842 95.762,502.431 255.898,401.21 416.035,502.431 369.263,318.842\\\"/></svg>\",\r\n};\r\n/**\r\n * Resolves the specified element factory using the resolvable elements, if needed.\r\n */\r\nfunction resolveShapeFactory(factory) {\r\n    // Retrieve the unresolved element from the factory.\r\n    var shape = variation_1.evaluateVariation(factory);\r\n    // If a string is returned, we need to resolve the element. This means\r\n    // looking up the string in the resolver lookup. If the key was not\r\n    // resolvable, we throw an error.\r\n    if (typeof shape === \"string\") {\r\n        var resolved = exports.resolvableShapes[shape];\r\n        if (!resolved) {\r\n            throw new Error(\"Failed to resolve shape key '\" + shape + \"'. Did you forget to add it to the 'resolvableShapes' lookup?\");\r\n        }\r\n        // We're in luck, we can resolve the element! We create a dummy <div> element\r\n        // to set the innerHTML of, and return the first element child.\r\n        var dummy = document.createElement(\"div\");\r\n        dummy.innerHTML = resolved;\r\n        return dummy.firstElementChild;\r\n    }\r\n    return shape;\r\n}\r\nexports.resolveShapeFactory = resolveShapeFactory;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/systems/shapes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/systems/sources.js":
/*!******************************************************!*\
  !*** ./node_modules/party-js/lib/systems/sources.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.circleSource = exports.rectSource = exports.mouseSource = exports.elementSource = exports.dynamicSource = void 0;\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar random_1 = __webpack_require__(/*! ./random */ \"(ssr)/./node_modules/party-js/lib/systems/random.js\");\r\n/**\r\n * Dynamically infers a source sampler for the specified source type.\r\n */\r\nfunction dynamicSource(source) {\r\n    if (source instanceof HTMLElement) {\r\n        return elementSource(source);\r\n    }\r\n    if (source instanceof components_1.Circle) {\r\n        return circleSource(source);\r\n    }\r\n    if (source instanceof components_1.Rect) {\r\n        return rectSource(source);\r\n    }\r\n    if (source instanceof MouseEvent) {\r\n        return mouseSource(source);\r\n    }\r\n    throw new Error(\"Cannot infer the source type of '\" + source + \"'.\");\r\n}\r\nexports.dynamicSource = dynamicSource;\r\n/**\r\n * Creates a sampler to retrieve random points inside a specified HTMLElement.\r\n */\r\nfunction elementSource(source) {\r\n    return function () { return random_1.randomInsideRect(components_1.Rect.fromElement(source)); };\r\n}\r\nexports.elementSource = elementSource;\r\n/**\r\n * Creates a sampler to retrieve the position of a mouse event.\r\n */\r\nfunction mouseSource(source) {\r\n    return function () {\r\n        return new components_1.Vector(window.scrollX + source.clientX, window.scrollY + source.clientY);\r\n    };\r\n}\r\nexports.mouseSource = mouseSource;\r\n/**\r\n * Creates a sampler to retrieve random points inside a specified rectangle.\r\n */\r\nfunction rectSource(source) {\r\n    return function () { return random_1.randomInsideRect(source); };\r\n}\r\nexports.rectSource = rectSource;\r\n/**\r\n * Creates a sampler to retrieve random points inside a specified circle.\r\n */\r\nfunction circleSource(source) {\r\n    return function () { return random_1.randomInsideCircle(source); };\r\n}\r\nexports.circleSource = circleSource;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/systems/sources.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/systems/variation.js":
/*!********************************************************!*\
  !*** ./node_modules/party-js/lib/systems/variation.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.gradientSample = exports.splineSample = exports.skewRelative = exports.skew = exports.range = exports.evaluateVariation = void 0;\r\nvar random_1 = __webpack_require__(/*! ./random */ \"(ssr)/./node_modules/party-js/lib/systems/random.js\");\r\n/**\r\n * Returns a value instance of a variation.\r\n */\r\nfunction evaluateVariation(variation) {\r\n    if (Array.isArray(variation))\r\n        return random_1.pick(variation);\r\n    if (typeof variation === \"function\")\r\n        return variation();\r\n    return variation;\r\n}\r\nexports.evaluateVariation = evaluateVariation;\r\n/**\r\n * Creates a variation function that returns a random number from min to max.\r\n */\r\nfunction range(min, max) {\r\n    return function () { return random_1.randomRange(min, max); };\r\n}\r\nexports.range = range;\r\n/**\r\n * Creates a variation function that skews the specified value by a specified, absolute\r\n * amount. This means that instead of the value itself, a random number that deviates\r\n * at most by the specified amount is returned.\r\n *\r\n * @remarks\r\n * If you want to skew by a percentage instead, use `skewRelative`.\r\n */\r\nfunction skew(value, amount) {\r\n    return function () { return value + random_1.randomRange(-amount, amount); };\r\n}\r\nexports.skew = skew;\r\n/**\r\n * Creates a variation function that skews the specified value by a specified percentage.\r\n * This means that instead of the value itself, a random number that deviates by a maximum\r\n * of the specified percentage is returned.\r\n */\r\nfunction skewRelative(value, percentage) {\r\n    return function () { return value * (1 + random_1.randomRange(-percentage, percentage)); };\r\n}\r\nexports.skewRelative = skewRelative;\r\n/**\r\n * Creates a variation function that returns a random sample from the given spline.\r\n *\r\n * @param spline The spline to sample from.\r\n */\r\nfunction splineSample(spline) {\r\n    return function () { return spline.evaluate(Math.random()); };\r\n}\r\nexports.splineSample = splineSample;\r\n/**\r\n * Creates a variation function that returns a random sample from the given gradient.\r\n *\r\n * @remarks\r\n * This function is an alias for the spline variation, since a gradient is just\r\n * a spline under the hood.\r\n *\r\n * @param gradient The gradient to sample from.\r\n */\r\nfunction gradientSample(gradient) {\r\n    return splineSample(gradient);\r\n}\r\nexports.gradientSample = gradientSample;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/systems/variation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/templates/confetti.js":
/*!*********************************************************!*\
  !*** ./node_modules/party-js/lib/templates/confetti.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.confetti = void 0;\r\nvar __1 = __webpack_require__(/*! ../ */ \"(ssr)/./node_modules/party-js/lib/index.js\");\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar modules_1 = __webpack_require__(/*! ../systems/modules */ \"(ssr)/./node_modules/party-js/lib/systems/modules.js\");\r\nvar random = __webpack_require__(/*! ../systems/random */ \"(ssr)/./node_modules/party-js/lib/systems/random.js\");\r\nvar sources = __webpack_require__(/*! ../systems/sources */ \"(ssr)/./node_modules/party-js/lib/systems/sources.js\");\r\nvar variation = __webpack_require__(/*! ../systems/variation */ \"(ssr)/./node_modules/party-js/lib/systems/variation.js\");\r\nvar util = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/party-js/lib/util/index.js\");\r\n/**\r\n * The standard confetti template.\r\n *\r\n * @param source The source to emit the confetti from.\r\n * @param options The (optional) configuration overrides.\r\n */\r\nfunction confetti(source, options) {\r\n    var populated = util.overrideDefaults({\r\n        count: variation.range(20, 40),\r\n        spread: variation.range(35, 45),\r\n        speed: variation.range(300, 600),\r\n        size: variation.skew(1, 0.2),\r\n        rotation: function () { return random.randomUnitVector().scale(180); },\r\n        color: function () { return components_1.Color.fromHsl(random.randomRange(0, 360), 100, 70); },\r\n        modules: [\r\n            new modules_1.ModuleBuilder()\r\n                .drive(\"size\")\r\n                .by(function (t) { return Math.min(1, t * 3); })\r\n                .relative()\r\n                .build(),\r\n            new modules_1.ModuleBuilder()\r\n                .drive(\"rotation\")\r\n                .by(function (t) { return new components_1.Vector(140, 200, 260).scale(t); })\r\n                .relative()\r\n                .build(),\r\n        ],\r\n        shapes: [\"square\", \"circle\"],\r\n    }, options);\r\n    var emitter = __1.scene.current.createEmitter({\r\n        emitterOptions: {\r\n            loops: 1,\r\n            duration: 8,\r\n            modules: populated.modules,\r\n        },\r\n        emissionOptions: {\r\n            rate: 0,\r\n            bursts: [{ time: 0, count: populated.count }],\r\n            sourceSampler: sources.dynamicSource(source),\r\n            angle: variation.skew(-90, variation.evaluateVariation(populated.spread)),\r\n            initialLifetime: 8,\r\n            initialSpeed: populated.speed,\r\n            initialSize: populated.size,\r\n            initialRotation: populated.rotation,\r\n            initialColor: populated.color,\r\n        },\r\n        rendererOptions: {\r\n            shapeFactory: populated.shapes,\r\n        },\r\n    });\r\n    return emitter;\r\n}\r\nexports.confetti = confetti;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/templates/confetti.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/templates/index.js":
/*!******************************************************!*\
  !*** ./node_modules/party-js/lib/templates/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\n__exportStar(__webpack_require__(/*! ./confetti */ \"(ssr)/./node_modules/party-js/lib/templates/confetti.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./sparkles */ \"(ssr)/./node_modules/party-js/lib/templates/sparkles.js\"), exports);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3RlbXBsYXRlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLDJFQUFZO0FBQ2pDLGFBQWEsbUJBQU8sQ0FBQywyRUFBWSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi90ZW1wbGF0ZXMvaW5kZXguanM/ZTUzZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xyXG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcclxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcclxufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcclxuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XHJcbiAgICBvW2syXSA9IG1ba107XHJcbn0pKTtcclxudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XHJcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XHJcbn07XHJcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcclxuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2NvbmZldHRpXCIpLCBleHBvcnRzKTtcclxuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3NwYXJrbGVzXCIpLCBleHBvcnRzKTtcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/templates/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/templates/sparkles.js":
/*!*********************************************************!*\
  !*** ./node_modules/party-js/lib/templates/sparkles.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.sparkles = void 0;\r\nvar __1 = __webpack_require__(/*! .. */ \"(ssr)/./node_modules/party-js/lib/index.js\");\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar modules_1 = __webpack_require__(/*! ../systems/modules */ \"(ssr)/./node_modules/party-js/lib/systems/modules.js\");\r\nvar random = __webpack_require__(/*! ../systems/random */ \"(ssr)/./node_modules/party-js/lib/systems/random.js\");\r\nvar sources = __webpack_require__(/*! ../systems/sources */ \"(ssr)/./node_modules/party-js/lib/systems/sources.js\");\r\nvar variation = __webpack_require__(/*! ../systems/variation */ \"(ssr)/./node_modules/party-js/lib/systems/variation.js\");\r\nvar util = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/party-js/lib/util/index.js\");\r\n/**\r\n * The standard sparkles template.\r\n *\r\n * @param source The source to emit the sparkles from.\r\n * @param options The (optional) configuration overrides.\r\n */\r\nfunction sparkles(source, options) {\r\n    var populated = util.overrideDefaults({\r\n        lifetime: variation.range(1, 2),\r\n        count: variation.range(10, 20),\r\n        speed: variation.range(100, 200),\r\n        size: variation.range(0.8, 1.8),\r\n        rotation: function () { return new components_1.Vector(0, 0, random.randomRange(0, 360)); },\r\n        color: function () { return components_1.Color.fromHsl(50, 100, random.randomRange(55, 85)); },\r\n        modules: [\r\n            new modules_1.ModuleBuilder()\r\n                .drive(\"rotation\")\r\n                .by(function (t) { return new components_1.Vector(0, 0, 200).scale(t); })\r\n                .relative()\r\n                .build(),\r\n            new modules_1.ModuleBuilder()\r\n                .drive(\"size\")\r\n                .by(new components_1.NumericSpline({ time: 0, value: 0 }, { time: 0.3, value: 1 }, { time: 0.7, value: 1 }, { time: 1, value: 0 }))\r\n                .through(\"relativeLifetime\")\r\n                .relative()\r\n                .build(),\r\n            new modules_1.ModuleBuilder()\r\n                .drive(\"opacity\")\r\n                .by(new components_1.NumericSpline({ time: 0, value: 1 }, { time: 0.5, value: 1 }, { time: 1, value: 0 }))\r\n                .through(\"relativeLifetime\")\r\n                .build(),\r\n        ],\r\n        shapes: \"star\",\r\n    }, options);\r\n    var emitter = __1.scene.current.createEmitter({\r\n        emitterOptions: {\r\n            loops: 1,\r\n            duration: 3,\r\n            useGravity: false,\r\n            modules: populated.modules,\r\n        },\r\n        emissionOptions: {\r\n            rate: 0,\r\n            bursts: [{ time: 0, count: populated.count }],\r\n            sourceSampler: sources.dynamicSource(source),\r\n            angle: variation.range(0, 360),\r\n            initialLifetime: populated.lifetime,\r\n            initialSpeed: populated.speed,\r\n            initialSize: populated.size,\r\n            initialRotation: populated.rotation,\r\n            initialColor: populated.color,\r\n        },\r\n        rendererOptions: {\r\n            applyLighting: undefined,\r\n            shapeFactory: populated.shapes,\r\n        },\r\n    });\r\n    return emitter;\r\n}\r\nexports.sparkles = sparkles;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3RlbXBsYXRlcy9zcGFya2xlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0I7QUFDaEIsVUFBVSxtQkFBTyxDQUFDLHNEQUFJO0FBQ3RCLG1CQUFtQixtQkFBTyxDQUFDLDRFQUFlO0FBQzFDLGdCQUFnQixtQkFBTyxDQUFDLGdGQUFvQjtBQUM1QyxhQUFhLG1CQUFPLENBQUMsOEVBQW1CO0FBQ3hDLGNBQWMsbUJBQU8sQ0FBQyxnRkFBb0I7QUFDMUMsZ0JBQWdCLG1CQUFPLENBQUMsb0ZBQXNCO0FBQzlDLFdBQVcsbUJBQU8sQ0FBQyxnRUFBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsbUVBQW1FO0FBQ25HLDZCQUE2Qix5RUFBeUU7QUFDdEc7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLHFEQUFxRDtBQUN4RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxtQkFBbUIsSUFBSSxxQkFBcUIsSUFBSSxxQkFBcUIsSUFBSSxtQkFBbUI7QUFDako7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxtQkFBbUIsSUFBSSxxQkFBcUIsSUFBSSxtQkFBbUI7QUFDeEg7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSx1QkFBdUIsaUNBQWlDO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBLGdCQUFnQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi90ZW1wbGF0ZXMvc3BhcmtsZXMuanM/ZTAzOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLnNwYXJrbGVzID0gdm9pZCAwO1xyXG52YXIgX18xID0gcmVxdWlyZShcIi4uXCIpO1xyXG52YXIgY29tcG9uZW50c18xID0gcmVxdWlyZShcIi4uL2NvbXBvbmVudHNcIik7XHJcbnZhciBtb2R1bGVzXzEgPSByZXF1aXJlKFwiLi4vc3lzdGVtcy9tb2R1bGVzXCIpO1xyXG52YXIgcmFuZG9tID0gcmVxdWlyZShcIi4uL3N5c3RlbXMvcmFuZG9tXCIpO1xyXG52YXIgc291cmNlcyA9IHJlcXVpcmUoXCIuLi9zeXN0ZW1zL3NvdXJjZXNcIik7XHJcbnZhciB2YXJpYXRpb24gPSByZXF1aXJlKFwiLi4vc3lzdGVtcy92YXJpYXRpb25cIik7XHJcbnZhciB1dGlsID0gcmVxdWlyZShcIi4uL3V0aWxcIik7XHJcbi8qKlxyXG4gKiBUaGUgc3RhbmRhcmQgc3BhcmtsZXMgdGVtcGxhdGUuXHJcbiAqXHJcbiAqIEBwYXJhbSBzb3VyY2UgVGhlIHNvdXJjZSB0byBlbWl0IHRoZSBzcGFya2xlcyBmcm9tLlxyXG4gKiBAcGFyYW0gb3B0aW9ucyBUaGUgKG9wdGlvbmFsKSBjb25maWd1cmF0aW9uIG92ZXJyaWRlcy5cclxuICovXHJcbmZ1bmN0aW9uIHNwYXJrbGVzKHNvdXJjZSwgb3B0aW9ucykge1xyXG4gICAgdmFyIHBvcHVsYXRlZCA9IHV0aWwub3ZlcnJpZGVEZWZhdWx0cyh7XHJcbiAgICAgICAgbGlmZXRpbWU6IHZhcmlhdGlvbi5yYW5nZSgxLCAyKSxcclxuICAgICAgICBjb3VudDogdmFyaWF0aW9uLnJhbmdlKDEwLCAyMCksXHJcbiAgICAgICAgc3BlZWQ6IHZhcmlhdGlvbi5yYW5nZSgxMDAsIDIwMCksXHJcbiAgICAgICAgc2l6ZTogdmFyaWF0aW9uLnJhbmdlKDAuOCwgMS44KSxcclxuICAgICAgICByb3RhdGlvbjogZnVuY3Rpb24gKCkgeyByZXR1cm4gbmV3IGNvbXBvbmVudHNfMS5WZWN0b3IoMCwgMCwgcmFuZG9tLnJhbmRvbVJhbmdlKDAsIDM2MCkpOyB9LFxyXG4gICAgICAgIGNvbG9yOiBmdW5jdGlvbiAoKSB7IHJldHVybiBjb21wb25lbnRzXzEuQ29sb3IuZnJvbUhzbCg1MCwgMTAwLCByYW5kb20ucmFuZG9tUmFuZ2UoNTUsIDg1KSk7IH0sXHJcbiAgICAgICAgbW9kdWxlczogW1xyXG4gICAgICAgICAgICBuZXcgbW9kdWxlc18xLk1vZHVsZUJ1aWxkZXIoKVxyXG4gICAgICAgICAgICAgICAgLmRyaXZlKFwicm90YXRpb25cIilcclxuICAgICAgICAgICAgICAgIC5ieShmdW5jdGlvbiAodCkgeyByZXR1cm4gbmV3IGNvbXBvbmVudHNfMS5WZWN0b3IoMCwgMCwgMjAwKS5zY2FsZSh0KTsgfSlcclxuICAgICAgICAgICAgICAgIC5yZWxhdGl2ZSgpXHJcbiAgICAgICAgICAgICAgICAuYnVpbGQoKSxcclxuICAgICAgICAgICAgbmV3IG1vZHVsZXNfMS5Nb2R1bGVCdWlsZGVyKClcclxuICAgICAgICAgICAgICAgIC5kcml2ZShcInNpemVcIilcclxuICAgICAgICAgICAgICAgIC5ieShuZXcgY29tcG9uZW50c18xLk51bWVyaWNTcGxpbmUoeyB0aW1lOiAwLCB2YWx1ZTogMCB9LCB7IHRpbWU6IDAuMywgdmFsdWU6IDEgfSwgeyB0aW1lOiAwLjcsIHZhbHVlOiAxIH0sIHsgdGltZTogMSwgdmFsdWU6IDAgfSkpXHJcbiAgICAgICAgICAgICAgICAudGhyb3VnaChcInJlbGF0aXZlTGlmZXRpbWVcIilcclxuICAgICAgICAgICAgICAgIC5yZWxhdGl2ZSgpXHJcbiAgICAgICAgICAgICAgICAuYnVpbGQoKSxcclxuICAgICAgICAgICAgbmV3IG1vZHVsZXNfMS5Nb2R1bGVCdWlsZGVyKClcclxuICAgICAgICAgICAgICAgIC5kcml2ZShcIm9wYWNpdHlcIilcclxuICAgICAgICAgICAgICAgIC5ieShuZXcgY29tcG9uZW50c18xLk51bWVyaWNTcGxpbmUoeyB0aW1lOiAwLCB2YWx1ZTogMSB9LCB7IHRpbWU6IDAuNSwgdmFsdWU6IDEgfSwgeyB0aW1lOiAxLCB2YWx1ZTogMCB9KSlcclxuICAgICAgICAgICAgICAgIC50aHJvdWdoKFwicmVsYXRpdmVMaWZldGltZVwiKVxyXG4gICAgICAgICAgICAgICAgLmJ1aWxkKCksXHJcbiAgICAgICAgXSxcclxuICAgICAgICBzaGFwZXM6IFwic3RhclwiLFxyXG4gICAgfSwgb3B0aW9ucyk7XHJcbiAgICB2YXIgZW1pdHRlciA9IF9fMS5zY2VuZS5jdXJyZW50LmNyZWF0ZUVtaXR0ZXIoe1xyXG4gICAgICAgIGVtaXR0ZXJPcHRpb25zOiB7XHJcbiAgICAgICAgICAgIGxvb3BzOiAxLFxyXG4gICAgICAgICAgICBkdXJhdGlvbjogMyxcclxuICAgICAgICAgICAgdXNlR3Jhdml0eTogZmFsc2UsXHJcbiAgICAgICAgICAgIG1vZHVsZXM6IHBvcHVsYXRlZC5tb2R1bGVzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgZW1pc3Npb25PcHRpb25zOiB7XHJcbiAgICAgICAgICAgIHJhdGU6IDAsXHJcbiAgICAgICAgICAgIGJ1cnN0czogW3sgdGltZTogMCwgY291bnQ6IHBvcHVsYXRlZC5jb3VudCB9XSxcclxuICAgICAgICAgICAgc291cmNlU2FtcGxlcjogc291cmNlcy5keW5hbWljU291cmNlKHNvdXJjZSksXHJcbiAgICAgICAgICAgIGFuZ2xlOiB2YXJpYXRpb24ucmFuZ2UoMCwgMzYwKSxcclxuICAgICAgICAgICAgaW5pdGlhbExpZmV0aW1lOiBwb3B1bGF0ZWQubGlmZXRpbWUsXHJcbiAgICAgICAgICAgIGluaXRpYWxTcGVlZDogcG9wdWxhdGVkLnNwZWVkLFxyXG4gICAgICAgICAgICBpbml0aWFsU2l6ZTogcG9wdWxhdGVkLnNpemUsXHJcbiAgICAgICAgICAgIGluaXRpYWxSb3RhdGlvbjogcG9wdWxhdGVkLnJvdGF0aW9uLFxyXG4gICAgICAgICAgICBpbml0aWFsQ29sb3I6IHBvcHVsYXRlZC5jb2xvcixcclxuICAgICAgICB9LFxyXG4gICAgICAgIHJlbmRlcmVyT3B0aW9uczoge1xyXG4gICAgICAgICAgICBhcHBseUxpZ2h0aW5nOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICAgIHNoYXBlRmFjdG9yeTogcG9wdWxhdGVkLnNoYXBlcyxcclxuICAgICAgICB9LFxyXG4gICAgfSk7XHJcbiAgICByZXR1cm4gZW1pdHRlcjtcclxufVxyXG5leHBvcnRzLnNwYXJrbGVzID0gc3BhcmtsZXM7XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/templates/sparkles.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/util/config.js":
/*!**************************************************!*\
  !*** ./node_modules/party-js/lib/util/config.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.overrideDefaults = void 0;\r\n/**\r\n * Replaces the supplied defaults with the properties specified in the overrides.\r\n * This returns a new object.\r\n */\r\nfunction overrideDefaults(defaults, overrides) {\r\n    return Object.assign({}, defaults, overrides);\r\n}\r\nexports.overrideDefaults = overrideDefaults;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3V0aWwvY29uZmlnLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0Esd0JBQXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3V0aWwvY29uZmlnLmpzPzlkYmEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcclxuZXhwb3J0cy5vdmVycmlkZURlZmF1bHRzID0gdm9pZCAwO1xyXG4vKipcclxuICogUmVwbGFjZXMgdGhlIHN1cHBsaWVkIGRlZmF1bHRzIHdpdGggdGhlIHByb3BlcnRpZXMgc3BlY2lmaWVkIGluIHRoZSBvdmVycmlkZXMuXHJcbiAqIFRoaXMgcmV0dXJucyBhIG5ldyBvYmplY3QuXHJcbiAqL1xyXG5mdW5jdGlvbiBvdmVycmlkZURlZmF1bHRzKGRlZmF1bHRzLCBvdmVycmlkZXMpIHtcclxuICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBkZWZhdWx0cywgb3ZlcnJpZGVzKTtcclxufVxyXG5leHBvcnRzLm92ZXJyaWRlRGVmYXVsdHMgPSBvdmVycmlkZURlZmF1bHRzO1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/util/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/util/index.js":
/*!*************************************************!*\
  !*** ./node_modules/party-js/lib/util/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\n__exportStar(__webpack_require__(/*! ./config */ \"(ssr)/./node_modules/party-js/lib/util/config.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./rotation */ \"(ssr)/./node_modules/party-js/lib/util/rotation.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./rules */ \"(ssr)/./node_modules/party-js/lib/util/rules.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./lazy */ \"(ssr)/./node_modules/party-js/lib/util/lazy.js\"), exports);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3V0aWwvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0EsbUNBQW1DLG9DQUFvQyxnQkFBZ0I7QUFDdkYsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQixhQUFhLG1CQUFPLENBQUMsc0VBQVk7QUFDakMsYUFBYSxtQkFBTyxDQUFDLGdFQUFTO0FBQzlCLGFBQWEsbUJBQU8sQ0FBQyw4REFBUSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3BhcnR5LWpzL2xpYi91dGlsL2luZGV4LmpzPzU5ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcclxuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XHJcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XHJcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XHJcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xyXG4gICAgb1trMl0gPSBtW2tdO1xyXG59KSk7XHJcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xyXG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xyXG59O1xyXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XHJcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9jb25maWdcIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vcm90YXRpb25cIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vcnVsZXNcIiksIGV4cG9ydHMpO1xyXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vbGF6eVwiKSwgZXhwb3J0cyk7XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/util/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/util/lazy.js":
/*!************************************************!*\
  !*** ./node_modules/party-js/lib/util/lazy.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Lazy = void 0;\r\n/**\r\n * A wrapper class to lazily initialize a value.\r\n * Supports custom factory and predicate methods.\r\n */\r\nvar Lazy = /** @class */ (function () {\r\n    function Lazy(factory, exists) {\r\n        if (exists === void 0) { exists = Lazy.defaultExists; }\r\n        this.factory = factory;\r\n        this.exists = exists;\r\n    }\r\n    Object.defineProperty(Lazy.prototype, \"current\", {\r\n        /**\r\n         * The current value of the lazy object. Will be initialized, if the 'exists'\r\n         * predicate doesn't match.\r\n         */\r\n        get: function () {\r\n            if (!this.exists(this.value)) {\r\n                this.value = this.factory();\r\n            }\r\n            return this.value;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Lazy.defaultExists = function (value) {\r\n        return typeof value !== \"undefined\";\r\n    };\r\n    return Lazy;\r\n}());\r\nexports.Lazy = Lazy;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/util/lazy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/util/rotation.js":
/*!****************************************************!*\
  !*** ./node_modules/party-js/lib/util/rotation.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.rotationToNormal = void 0;\r\nvar components_1 = __webpack_require__(/*! ../components */ \"(ssr)/./node_modules/party-js/lib/components/index.js\");\r\nvar math_1 = __webpack_require__(/*! ../systems/math */ \"(ssr)/./node_modules/party-js/lib/systems/math.js\");\r\n/**\r\n * Converts the specified euler rotation (in degrees) into the corresponding normal vector.\r\n *\r\n * @remarks\r\n * The normal is calculated by placing a (figurative) plane in a coordinate-system's\r\n * origin, and rotating it by the specified angles. Note that the z-component of the\r\n * rotation is irrelevant for the normal and can be ignored. Then, two vectors\r\n * describing the orientation of the plane are calculated. Their cross product\r\n * denotes the normal vector.\r\n *\r\n * @param rotation The euler rotation angles (in degrees) to calculate the normal for.\r\n */\r\nfunction rotationToNormal(rotation) {\r\n    var alpha = rotation.x * math_1.deg2rad;\r\n    var beta = rotation.y * math_1.deg2rad;\r\n    var a = new components_1.Vector(Math.cos(beta), 0, Math.sin(beta));\r\n    var b = new components_1.Vector(0, Math.cos(alpha), Math.sin(alpha));\r\n    return a.cross(b);\r\n}\r\nexports.rotationToNormal = rotationToNormal;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/util/rotation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/party-js/lib/util/rules.js":
/*!*************************************************!*\
  !*** ./node_modules/party-js/lib/util/rules.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.despawningRules = void 0;\r\n/**\r\n * Contains a set of pre-defined particle despawning rules.\r\n */\r\nexports.despawningRules = {\r\n    /**\r\n     * A rule that despawns a particle once its lifetime is over.\r\n     */\r\n    lifetime: function (particle) {\r\n        return particle.lifetime <= 0;\r\n    },\r\n    /**\r\n     * A rule that despawns a particle once its y-coordinate is outside of the document.\r\n     */\r\n    bounds: function (particle) {\r\n        // Get document height: https://stackoverflow.com/a/44077777/5507624\r\n        var height = document.documentElement.scrollHeight;\r\n        return particle.location.y > height;\r\n    },\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3V0aWwvcnVsZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcGFydHktanMvbGliL3V0aWwvcnVsZXMuanM/NzEwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLmRlc3Bhd25pbmdSdWxlcyA9IHZvaWQgMDtcclxuLyoqXHJcbiAqIENvbnRhaW5zIGEgc2V0IG9mIHByZS1kZWZpbmVkIHBhcnRpY2xlIGRlc3Bhd25pbmcgcnVsZXMuXHJcbiAqL1xyXG5leHBvcnRzLmRlc3Bhd25pbmdSdWxlcyA9IHtcclxuICAgIC8qKlxyXG4gICAgICogQSBydWxlIHRoYXQgZGVzcGF3bnMgYSBwYXJ0aWNsZSBvbmNlIGl0cyBsaWZldGltZSBpcyBvdmVyLlxyXG4gICAgICovXHJcbiAgICBsaWZldGltZTogZnVuY3Rpb24gKHBhcnRpY2xlKSB7XHJcbiAgICAgICAgcmV0dXJuIHBhcnRpY2xlLmxpZmV0aW1lIDw9IDA7XHJcbiAgICB9LFxyXG4gICAgLyoqXHJcbiAgICAgKiBBIHJ1bGUgdGhhdCBkZXNwYXducyBhIHBhcnRpY2xlIG9uY2UgaXRzIHktY29vcmRpbmF0ZSBpcyBvdXRzaWRlIG9mIHRoZSBkb2N1bWVudC5cclxuICAgICAqL1xyXG4gICAgYm91bmRzOiBmdW5jdGlvbiAocGFydGljbGUpIHtcclxuICAgICAgICAvLyBHZXQgZG9jdW1lbnQgaGVpZ2h0OiBodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL2EvNDQwNzc3NzcvNTUwNzYyNFxyXG4gICAgICAgIHZhciBoZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2Nyb2xsSGVpZ2h0O1xyXG4gICAgICAgIHJldHVybiBwYXJ0aWNsZS5sb2NhdGlvbi55ID4gaGVpZ2h0O1xyXG4gICAgfSxcclxufTtcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/party-js/lib/util/rules.js\n");

/***/ })

};
;