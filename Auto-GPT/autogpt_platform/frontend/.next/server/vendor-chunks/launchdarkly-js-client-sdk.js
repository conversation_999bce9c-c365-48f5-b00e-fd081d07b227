"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/launchdarkly-js-client-sdk";
exports.ids = ["vendor-chunks/launchdarkly-js-client-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/launchdarkly-js-client-sdk/dist/ldclient.es.js":
/*!*********************************************************************!*\
  !*** ./node_modules/launchdarkly-js-client-sdk/dist/ldclient.es.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicLogger: () => (/* binding */ Nt),\n/* harmony export */   createConsoleLogger: () => (/* binding */ $t),\n/* harmony export */   \"default\": () => (/* binding */ Mt),\n/* harmony export */   initialize: () => (/* binding */ Ft),\n/* harmony export */   version: () => (/* binding */ Vt)\n/* harmony export */ });\nfunction e(e){function t(e,t){Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.message=e,this.code=t}return t.prototype=new Error,t.prototype.name=e,t.prototype.constructor=t,t}const t=e(\"LaunchDarklyUnexpectedResponseError\"),n=e(\"LaunchDarklyInvalidEnvironmentIdError\"),r=e(\"LaunchDarklyInvalidUserError\"),o=e(\"LaunchDarklyInvalidEventKeyError\"),i=e(\"LaunchDarklyInvalidArgumentError\"),a=e(\"LaunchDarklyFlagFetchError\");for(var s={LDUnexpectedResponseError:t,LDInvalidEnvironmentIdError:n,LDInvalidUserError:r,LDInvalidEventKeyError:o,LDInvalidArgumentError:i,LDInvalidDataError:e(\"LaunchDarklyInvalidDataError\"),LDFlagFetchError:a,LDTimeoutError:e(\"LaunchDarklyTimeoutError\"),isHttpErrorRecoverable:function(e){return!(e>=400&&e<500)||(400===e||408===e||429===e)}},c=function(e){var t=m(e),n=t[0],r=t[1];return 3*(n+r)/4-r},u=function(e){var t,n,r=m(e),o=r[0],i=r[1],a=new g(function(e,t,n){return 3*(t+n)/4-n}(0,o,i)),s=0,c=i>0?o-4:o;for(n=0;n<c;n+=4)t=f[e.charCodeAt(n)]<<18|f[e.charCodeAt(n+1)]<<12|f[e.charCodeAt(n+2)]<<6|f[e.charCodeAt(n+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=255&t;2===i&&(t=f[e.charCodeAt(n)]<<2|f[e.charCodeAt(n+1)]>>4,a[s++]=255&t);1===i&&(t=f[e.charCodeAt(n)]<<10|f[e.charCodeAt(n+1)]<<4|f[e.charCodeAt(n+2)]>>2,a[s++]=t>>8&255,a[s++]=255&t);return a},l=function(e){for(var t,n=e.length,r=n%3,o=[],i=16383,a=0,s=n-r;a<s;a+=i)o.push(h(e,a,a+i>s?s:a+i));1===r?(t=e[n-1],o.push(d[t>>2]+d[t<<4&63]+\"==\")):2===r&&(t=(e[n-2]<<8)+e[n-1],o.push(d[t>>10]+d[t>>4&63]+d[t<<2&63]+\"=\"));return o.join(\"\")},d=[],f=[],g=\"undefined\"!=typeof Uint8Array?Uint8Array:Array,v=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",p=0;p<64;++p)d[p]=v[p],f[v.charCodeAt(p)]=p;function m(e){var t=e.length;if(t%4>0)throw new Error(\"Invalid string. Length must be a multiple of 4\");var n=e.indexOf(\"=\");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function h(e,t,n){for(var r,o,i=[],a=t;a<n;a+=3)r=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),i.push(d[(o=r)>>18&63]+d[o>>12&63]+d[o>>6&63]+d[63&o]);return i.join(\"\")}f[\"-\".charCodeAt(0)]=62,f[\"_\".charCodeAt(0)]=63;var y={byteLength:c,toByteArray:u,fromByteArray:l},w=Array.isArray,b=Object.keys,k=Object.prototype.hasOwnProperty,E=function e(t,n){if(t===n)return!0;if(t&&n&&\"object\"==typeof t&&\"object\"==typeof n){var r,o,i,a=w(t),s=w(n);if(a&&s){if((o=t.length)!=n.length)return!1;for(r=o;0!=r--;)if(!e(t[r],n[r]))return!1;return!0}if(a!=s)return!1;var c=t instanceof Date,u=n instanceof Date;if(c!=u)return!1;if(c&&u)return t.getTime()==n.getTime();var l=t instanceof RegExp,d=n instanceof RegExp;if(l!=d)return!1;if(l&&d)return t.toString()==n.toString();var f=b(t);if((o=f.length)!==b(n).length)return!1;for(r=o;0!=r--;)if(!k.call(n,f[r]))return!1;for(r=o;0!=r--;)if(!e(t[i=f[r]],n[i]))return!1;return!0}return t!=t&&n!=n};const D=[\"key\",\"ip\",\"country\",\"email\",\"firstName\",\"lastName\",\"avatar\",\"name\"];function x(e){const t=unescape(encodeURIComponent(e));return y.fromByteArray(function(e){const t=[];for(let n=0;n<e.length;n++)t.push(e.charCodeAt(n));return t}(t))}function C(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var P,S={appendUrlPath:function(e,t){return(e.endsWith(\"/\")?e.substring(0,e.length-1):e)+(t.startsWith(\"/\")?\"\":\"/\")+t},base64URLEncode:function(e){return x(e).replace(/=/g,\"\").replace(/\\+/g,\"-\").replace(/\\//g,\"_\")},btoa:x,clone:function(e){return JSON.parse(JSON.stringify(e))},deepEquals:function(e,t){return E(e,t)},extend:function(...e){return e.reduce(((e,t)=>({...e,...t})),{})},getLDUserAgentString:function(e){const t=e.version||\"?\";return e.userAgent+\"/\"+t},objectHasOwnProperty:C,onNextTick:function(e){setTimeout(e,0)},sanitizeContext:function(e){if(!e)return e;let t;return null!==e.kind&&void 0!==e.kind||D.forEach((n=>{const r=e[n];void 0!==r&&\"string\"!=typeof r&&(t=t||{...e},t[n]=String(r))})),t||e},transformValuesToVersionedValues:function(e){const t={};for(const n in e)C(e,n)&&(t[n]={value:e[n],version:0});return t},transformVersionedValuesToValues:function(e){const t={};for(const n in e)C(e,n)&&(t[n]=e[n].value);return t},wrapPromiseCallback:function(e,t){const n=e.then((e=>(t&&setTimeout((()=>{t(null,e)}),0),e)),(e=>{if(!t)return Promise.reject(e);setTimeout((()=>{t(e,null)}),0)}));return t?void 0:n}},I=new Uint8Array(16);function O(){if(!P&&!(P=\"undefined\"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||\"undefined\"!=typeof msCrypto&&\"function\"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error(\"crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported\");return P(I)}var T=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function L(e){return\"string\"==typeof e&&T.test(e)}for(var U,R,A=[],j=0;j<256;++j)A.push((j+256).toString(16).substr(1));function F(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(A[e[t+0]]+A[e[t+1]]+A[e[t+2]]+A[e[t+3]]+\"-\"+A[e[t+4]]+A[e[t+5]]+\"-\"+A[e[t+6]]+A[e[t+7]]+\"-\"+A[e[t+8]]+A[e[t+9]]+\"-\"+A[e[t+10]]+A[e[t+11]]+A[e[t+12]]+A[e[t+13]]+A[e[t+14]]+A[e[t+15]]).toLowerCase();if(!L(n))throw TypeError(\"Stringified UUID is invalid\");return n}var N=0,$=0;function V(e){if(!L(e))throw TypeError(\"Invalid UUID\");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}function M(e,t,n){function r(e,r,o,i){if(\"string\"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),\"string\"==typeof r&&(r=V(r)),16!==r.length)throw TypeError(\"Namespace must be array-like (16 iterable integer values, 0-255)\");var a=new Uint8Array(16+e.length);if(a.set(r),a.set(e,r.length),(a=n(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,o){i=i||0;for(var s=0;s<16;++s)o[i+s]=a[s];return o}return F(a)}try{r.name=e}catch(e){}return r.DNS=\"6ba7b810-9dad-11d1-80b4-00c04fd430c8\",r.URL=\"6ba7b811-9dad-11d1-80b4-00c04fd430c8\",r}function q(e){return 14+(e+64>>>9<<4)+1}function H(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function z(e,t,n,r,o,i){return H((a=H(H(t,e),H(r,i)))<<(s=o)|a>>>32-s,n);var a,s}function K(e,t,n,r,o,i,a){return z(t&n|~t&r,e,t,o,i,a)}function _(e,t,n,r,o,i,a){return z(t&r|n&~r,e,t,o,i,a)}function J(e,t,n,r,o,i,a){return z(t^n^r,e,t,o,i,a)}function B(e,t,n,r,o,i,a){return z(n^(t|~r),e,t,o,i,a)}var G=M(\"v3\",48,(function(e){if(\"string\"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(e){for(var t=[],n=32*e.length,r=\"0123456789abcdef\",o=0;o<n;o+=8){var i=e[o>>5]>>>o%32&255,a=parseInt(r.charAt(i>>>4&15)+r.charAt(15&i),16);t.push(a)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[q(t)-1]=t;for(var n=1732584193,r=-271733879,o=-1732584194,i=271733878,a=0;a<e.length;a+=16){var s=n,c=r,u=o,l=i;n=K(n,r,o,i,e[a],7,-680876936),i=K(i,n,r,o,e[a+1],12,-389564586),o=K(o,i,n,r,e[a+2],17,606105819),r=K(r,o,i,n,e[a+3],22,-1044525330),n=K(n,r,o,i,e[a+4],7,-176418897),i=K(i,n,r,o,e[a+5],12,1200080426),o=K(o,i,n,r,e[a+6],17,-1473231341),r=K(r,o,i,n,e[a+7],22,-45705983),n=K(n,r,o,i,e[a+8],7,1770035416),i=K(i,n,r,o,e[a+9],12,-1958414417),o=K(o,i,n,r,e[a+10],17,-42063),r=K(r,o,i,n,e[a+11],22,-1990404162),n=K(n,r,o,i,e[a+12],7,1804603682),i=K(i,n,r,o,e[a+13],12,-40341101),o=K(o,i,n,r,e[a+14],17,-1502002290),n=_(n,r=K(r,o,i,n,e[a+15],22,1236535329),o,i,e[a+1],5,-165796510),i=_(i,n,r,o,e[a+6],9,-1069501632),o=_(o,i,n,r,e[a+11],14,643717713),r=_(r,o,i,n,e[a],20,-373897302),n=_(n,r,o,i,e[a+5],5,-701558691),i=_(i,n,r,o,e[a+10],9,38016083),o=_(o,i,n,r,e[a+15],14,-660478335),r=_(r,o,i,n,e[a+4],20,-405537848),n=_(n,r,o,i,e[a+9],5,568446438),i=_(i,n,r,o,e[a+14],9,-1019803690),o=_(o,i,n,r,e[a+3],14,-187363961),r=_(r,o,i,n,e[a+8],20,1163531501),n=_(n,r,o,i,e[a+13],5,-1444681467),i=_(i,n,r,o,e[a+2],9,-51403784),o=_(o,i,n,r,e[a+7],14,1735328473),n=J(n,r=_(r,o,i,n,e[a+12],20,-1926607734),o,i,e[a+5],4,-378558),i=J(i,n,r,o,e[a+8],11,-2022574463),o=J(o,i,n,r,e[a+11],16,1839030562),r=J(r,o,i,n,e[a+14],23,-35309556),n=J(n,r,o,i,e[a+1],4,-1530992060),i=J(i,n,r,o,e[a+4],11,1272893353),o=J(o,i,n,r,e[a+7],16,-155497632),r=J(r,o,i,n,e[a+10],23,-1094730640),n=J(n,r,o,i,e[a+13],4,681279174),i=J(i,n,r,o,e[a],11,-358537222),o=J(o,i,n,r,e[a+3],16,-722521979),r=J(r,o,i,n,e[a+6],23,76029189),n=J(n,r,o,i,e[a+9],4,-640364487),i=J(i,n,r,o,e[a+12],11,-421815835),o=J(o,i,n,r,e[a+15],16,530742520),n=B(n,r=J(r,o,i,n,e[a+2],23,-995338651),o,i,e[a],6,-198630844),i=B(i,n,r,o,e[a+7],10,1126891415),o=B(o,i,n,r,e[a+14],15,-1416354905),r=B(r,o,i,n,e[a+5],21,-57434055),n=B(n,r,o,i,e[a+12],6,1700485571),i=B(i,n,r,o,e[a+3],10,-1894986606),o=B(o,i,n,r,e[a+10],15,-1051523),r=B(r,o,i,n,e[a+1],21,-2054922799),n=B(n,r,o,i,e[a+8],6,1873313359),i=B(i,n,r,o,e[a+15],10,-30611744),o=B(o,i,n,r,e[a+6],15,-1560198380),r=B(r,o,i,n,e[a+13],21,1309151649),n=B(n,r,o,i,e[a+4],6,-145523070),i=B(i,n,r,o,e[a+11],10,-1120210379),o=B(o,i,n,r,e[a+2],15,718787259),r=B(r,o,i,n,e[a+9],21,-343485551),n=H(n,s),r=H(r,c),o=H(o,u),i=H(i,l)}return[n,r,o,i]}(function(e){if(0===e.length)return[];for(var t=8*e.length,n=new Uint32Array(q(t)),r=0;r<t;r+=8)n[r>>5]|=(255&e[r/8])<<r%32;return n}(e),8*e.length))})),W=G;function X(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function Q(e,t){return e<<t|e>>>32-t}var Y=M(\"v5\",80,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(\"string\"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var o=0;o<r.length;++o)e.push(r.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=e.length/4+2,a=Math.ceil(i/16),s=new Array(a),c=0;c<a;++c){for(var u=new Uint32Array(16),l=0;l<16;++l)u[l]=e[64*c+4*l]<<24|e[64*c+4*l+1]<<16|e[64*c+4*l+2]<<8|e[64*c+4*l+3];s[c]=u}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<a;++d){for(var f=new Uint32Array(80),g=0;g<16;++g)f[g]=s[d][g];for(var v=16;v<80;++v)f[v]=Q(f[v-3]^f[v-8]^f[v-14]^f[v-16],1);for(var p=n[0],m=n[1],h=n[2],y=n[3],w=n[4],b=0;b<80;++b){var k=Math.floor(b/20),E=Q(p,5)+X(k,m,h,y)+w+t[k]+f[b]>>>0;w=y,y=h,h=Q(m,30)>>>0,m=p,p=E}n[0]=n[0]+p>>>0,n[1]=n[1]+m>>>0,n[2]=n[2]+h>>>0,n[3]=n[3]+y>>>0,n[4]=n[4]+w>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),Z=Y;var ee=Object.freeze({__proto__:null,v1:function(e,t,n){var r=t&&n||0,o=t||new Array(16),i=(e=e||{}).node||U,a=void 0!==e.clockseq?e.clockseq:R;if(null==i||null==a){var s=e.random||(e.rng||O)();null==i&&(i=U=[1|s[0],s[1],s[2],s[3],s[4],s[5]]),null==a&&(a=R=16383&(s[6]<<8|s[7]))}var c=void 0!==e.msecs?e.msecs:Date.now(),u=void 0!==e.nsecs?e.nsecs:$+1,l=c-N+(u-$)/1e4;if(l<0&&void 0===e.clockseq&&(a=a+1&16383),(l<0||c>N)&&void 0===e.nsecs&&(u=0),u>=1e4)throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");N=c,$=u,R=a;var d=(1e4*(268435455&(c+=122192928e5))+u)%4294967296;o[r++]=d>>>24&255,o[r++]=d>>>16&255,o[r++]=d>>>8&255,o[r++]=255&d;var f=c/4294967296*1e4&268435455;o[r++]=f>>>8&255,o[r++]=255&f,o[r++]=f>>>24&15|16,o[r++]=f>>>16&255,o[r++]=a>>>8|128,o[r++]=255&a;for(var g=0;g<6;++g)o[r+g]=i[g];return t||F(o)},v3:W,v4:function(e,t,n){var r=(e=e||{}).random||(e.rng||O)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return F(r)},v5:Z,NIL:\"00000000-0000-0000-0000-000000000000\",version:function(e){if(!L(e))throw TypeError(\"Invalid UUID\");return parseInt(e.substr(14,1),16)},validate:L,stringify:F,parse:V});const te=[\"debug\",\"info\",\"warn\",\"error\",\"none\"];var ne={commonBasicLogger:function(e,t){if(e&&e.destination&&\"function\"!=typeof e.destination)throw new Error(\"destination for basicLogger was set to a non-function\");function n(e){return function(t){console&&console[e]&&console[e].call(console,t)}}const r=e&&e.destination?[e.destination,e.destination,e.destination,e.destination]:[n(\"log\"),n(\"info\"),n(\"warn\"),n(\"error\")],o=!(!e||!e.destination),i=e&&void 0!==e.prefix&&null!==e.prefix?e.prefix:\"[LaunchDarkly] \";let a=1;if(e&&e.level)for(let t=0;t<te.length;t++)te[t]===e.level&&(a=t);function s(e,n,a){if(a.length<1)return;let s;const c=o?n+\": \"+i:i;if(1!==a.length&&t){const e=[...a];e[0]=c+e[0],s=t(...e)}else s=c+a[0];try{r[e](s)}catch(e){console&&console.log&&console.log(\"[LaunchDarkly] Configured logger's \"+n+\" method threw an exception: \"+e)}}const c={};for(let e=0;e<te.length;e++){const t=te[e];if(\"none\"!==t)if(e<a)c[t]=()=>{};else{const n=e;c[t]=function(){s(n,t,arguments)}}}return c},validateLogger:function(e){te.forEach((t=>{if(\"none\"!==t&&(!e[t]||\"function\"!=typeof e[t]))throw new Error(\"Provided logger instance must support logger.\"+t+\"(...) method\")}))}};function re(e){return e&&e.message?e.message:\"string\"==typeof e||e instanceof String?e:JSON.stringify(e)}const oe=\" Please see https://docs.launchdarkly.com/sdk/client-side/javascript#initialize-the-client for instructions on SDK initialization.\";var ie={bootstrapInvalid:function(){return\"LaunchDarkly bootstrap data is not available because the back end could not read the flags.\"},bootstrapOldFormat:function(){return\"LaunchDarkly client was initialized with bootstrap data that did not include flag metadata. Events may not be sent correctly.\"+oe},clientInitialized:function(){return\"LaunchDarkly client initialized\"},clientNotReady:function(){return\"LaunchDarkly client is not ready\"},debugEnqueueingEvent:function(e){return'enqueueing \"'+e+'\" event'},debugPostingDiagnosticEvent:function(e){return\"sending diagnostic event (\"+e.kind+\")\"},debugPostingEvents:function(e){return\"sending \"+e+\" events\"},debugStreamDelete:function(e){return'received streaming deletion for flag \"'+e+'\"'},debugStreamDeleteIgnored:function(e){return'received streaming deletion for flag \"'+e+'\" but ignored due to version check'},debugStreamPatch:function(e){return'received streaming update for flag \"'+e+'\"'},debugStreamPatchIgnored:function(e){return'received streaming update for flag \"'+e+'\" but ignored due to version check'},debugStreamPing:function(){return\"received ping message from stream\"},debugPolling:function(e){return\"polling for feature flags at \"+e},debugStreamPut:function(){return\"received streaming update for all flags\"},deprecated:function(e,t){return t?'\"'+e+'\" is deprecated, please use \"'+t+'\"':'\"'+e+'\" is deprecated'},environmentNotFound:function(){return\"Environment not found. Double check that you specified a valid environment/client-side ID.\"+oe},environmentNotSpecified:function(){return\"No environment/client-side ID was specified.\"+oe},errorFetchingFlags:function(e){return\"Error fetching flag settings: \"+re(e)},eventCapacityExceeded:function(){return\"Exceeded event queue capacity. Increase capacity to avoid dropping events.\"},eventWithoutContext:function(){return\"Be sure to call `identify` in the LaunchDarkly client: https://docs.launchdarkly.com/sdk/features/identify#javascript\"},httpErrorMessage:function(e,t,n){return\"Received error \"+e+(401===e?\" (invalid SDK key)\":\"\")+\" for \"+t+\" - \"+(s.isHttpErrorRecoverable(e)?n:\"giving up permanently\")},httpUnavailable:function(){return\"Cannot make HTTP requests in this environment.\"+oe},identifyDisabled:function(){return\"identify() has no effect here; it must be called on the main client instance\"},inspectorMethodError:(e,t)=>`an inspector: \"${t}\" of type: \"${e}\" generated an exception`,invalidContentType:function(e){return'Expected application/json content type but got \"'+e+'\"'},invalidData:function(){return\"Invalid data received from LaunchDarkly; connection may have been interrupted\"},invalidInspector:(e,t)=>`an inspector: \"${t}\" of an invalid type (${e}) was configured`,invalidKey:function(){return\"Event key must be a string\"},invalidMetricValue:e=>`The track function was called with a non-numeric \"metricValue\" (${e}), only numeric metric values are supported.`,invalidContext:function(){return\"Invalid context specified.\"+oe},invalidTagValue:e=>`Config option \"${e}\" must only contain letters, numbers, ., _ or -.`,localStorageUnavailable:function(e){return\"local storage is unavailable: \"+re(e)},networkError:e=>\"network error\"+(e?\" (\"+e+\")\":\"\"),optionBelowMinimum:(e,t,n)=>'Config option \"'+e+'\" was set to '+t+\", changing to minimum value of \"+n,streamClosing:function(){return\"Closing stream connection\"},streamConnecting:function(e){return\"Opening stream connection to \"+e},streamError:function(e,t){return\"Error on stream connection: \"+re(e)+\", will continue retrying after \"+t+\" milliseconds.\"},tagValueTooLong:e=>`Value of \"${e}\" was longer than 64 characters and was discarded.`,unknownCustomEventKey:function(e){return'Custom event \"'+e+'\" does not exist'},unknownOption:e=>'Ignoring unknown config option \"'+e+'\"',contextNotSpecified:function(){return\"No context specified.\"+oe},unrecoverableStreamError:e=>`Error on stream connection ${re(e)}, giving up permanently`,wrongOptionType:(e,t,n)=>'Config option \"'+e+'\" should be of type '+t+\", got \"+n+\", using default value\",wrongOptionTypeBoolean:(e,t)=>'Config option \"'+e+'\" should be a boolean, got '+t+\", converting to boolean\"};const{validateLogger:ae}=ne,se={baseUrl:{default:\"https://app.launchdarkly.com\"},streamUrl:{default:\"https://clientstream.launchdarkly.com\"},eventsUrl:{default:\"https://events.launchdarkly.com\"},sendEvents:{default:!0},streaming:{type:\"boolean\"},sendLDHeaders:{default:!0},requestHeaderTransform:{type:\"function\"},sendEventsOnlyForVariation:{default:!1},useReport:{default:!1},evaluationReasons:{default:!1},eventCapacity:{default:100,minimum:1},flushInterval:{default:2e3,minimum:2e3},samplingInterval:{default:0,minimum:0},streamReconnectDelay:{default:1e3,minimum:0},allAttributesPrivate:{default:!1},privateAttributes:{default:[]},bootstrap:{type:\"string|object\"},diagnosticRecordingInterval:{default:9e5,minimum:2e3},diagnosticOptOut:{default:!1},wrapperName:{type:\"string\"},wrapperVersion:{type:\"string\"},stateProvider:{type:\"object\"},application:{validator:function(e,t,n){const r={};t.id&&(r.id=le(`${e}.id`,t.id,n));t.version&&(r.version=le(`${e}.version`,t.version,n));return r}},inspectors:{default:[]}},ce=/^(\\w|\\.|-)+$/;function ue(e){return e&&e.replace(/\\/+$/,\"\")}function le(e,t,n){if(\"string\"==typeof t&&t.match(ce)){if(!(t.length>64))return t;n.warn(ie.tagValueTooLong(e))}else n.warn(ie.invalidTagValue(e))}var de={baseOptionDefs:se,validate:function(e,t,n,r){const o=S.extend({logger:{default:r}},se,n),i={};function a(e){S.onNextTick((()=>{t&&t.maybeReportError(new s.LDInvalidArgumentError(e))}))}let c=S.extend({},e||{});return function(e){const t=e;Object.keys(i).forEach((e=>{if(void 0!==t[e]){const n=i[e];r&&r.warn(ie.deprecated(e,n)),n&&(void 0===t[n]&&(t[n]=t[e]),delete t[e])}}))}(c),c=function(e){const t=S.extend({},e);return Object.keys(o).forEach((e=>{void 0!==t[e]&&null!==t[e]||(t[e]=o[e]&&o[e].default)})),t}(c),c=function(e){const t=S.extend({},e),n=e=>{if(null===e)return\"any\";if(void 0===e)return;if(Array.isArray(e))return\"array\";const t=typeof e;return\"boolean\"===t||\"string\"===t||\"number\"===t||\"function\"===t?t:\"object\"};return Object.keys(e).forEach((i=>{const s=e[i];if(null!=s){const c=o[i];if(void 0===c)a(ie.unknownOption(i));else{const o=c.type||n(c.default),u=c.validator;if(u){const n=u(i,e[i],r);void 0!==n?t[i]=n:delete t[i]}else if(\"any\"!==o){const e=o.split(\"|\"),r=n(s);e.indexOf(r)<0?\"boolean\"===o?(t[i]=!!s,a(ie.wrongOptionTypeBoolean(i,r))):(a(ie.wrongOptionType(i,o,r)),t[i]=c.default):\"number\"===r&&void 0!==c.minimum&&s<c.minimum&&(a(ie.optionBelowMinimum(i,s,c.minimum)),t[i]=c.minimum)}}}})),t.baseUrl=ue(t.baseUrl),t.streamUrl=ue(t.streamUrl),t.eventsUrl=ue(t.eventsUrl),t}(c),ae(c.logger),c},getTags:function(e){const t={};return e&&(e.application&&void 0!==e.application.id&&null!==e.application.id&&(t[\"application-id\"]=[e.application.id]),e.application&&void 0!==e.application.version&&null!==e.application.id&&(t[\"application-version\"]=[e.application.version])),t}};const{getLDUserAgentString:fe}=S;var ge={getLDHeaders:function(e,t){if(t&&!t.sendLDHeaders)return{};const n={};n[e.userAgentHeaderName||\"User-Agent\"]=fe(e),t&&t.wrapperName&&(n[\"X-LaunchDarkly-Wrapper\"]=t.wrapperVersion?t.wrapperName+\"/\"+t.wrapperVersion:t.wrapperName);const r=de.getTags(t),o=Object.keys(r);return o.length&&(n[\"x-launchdarkly-tags\"]=o.sort().map((e=>Array.isArray(r[e])?r[e].sort().map((t=>`${e}/${t}`)):[`${e}/${r[e]}`])).reduce(((e,t)=>e.concat(t)),[]).join(\" \")),n},transformHeaders:function(e,t){return t&&t.requestHeaderTransform?t.requestHeaderTransform({...e}):e}};const{v1:ve}=ee,{getLDHeaders:pe,transformHeaders:me}=ge;var he=function(e,t,n){const r=S.extend({\"Content-Type\":\"application/json\"},pe(e,n)),o={};return o.sendEvents=(t,o,i)=>{if(!e.httpRequest)return Promise.resolve();const a=JSON.stringify(t),c=i?null:ve();return function t(u){const l=i?r:S.extend({},r,{\"X-LaunchDarkly-Event-Schema\":\"4\",\"X-LaunchDarkly-Payload-ID\":c});return e.httpRequest(\"POST\",o,me(l,n),a).promise.then((e=>{if(e)return e.status>=400&&s.isHttpErrorRecoverable(e.status)&&u?t(!1):function(e){const t={status:e.status},n=e.header(\"date\");if(n){const e=Date.parse(n);e&&(t.serverTime=e)}return t}(e)})).catch((()=>u?t(!1):Promise.reject()))}(!0).catch((()=>{}))},o};const{commonBasicLogger:ye}=ne;function we(e){return\"string\"==typeof e&&\"kind\"!==e&&e.match(/^(\\w|\\.|-)+$/)}function be(e){return e.includes(\"%\")||e.includes(\":\")?e.replace(/%/g,\"%25\").replace(/:/g,\"%3A\"):e}var ke={checkContext:function(e,t){if(e){if(t&&(void 0===e.kind||null===e.kind))return void 0!==e.key&&null!==e.key;const n=e.key,r=void 0===e.kind?\"user\":e.kind,o=we(r),i=\"multi\"===r||null!=n&&\"\"!==n;if(\"multi\"===r){const t=Object.keys(e).filter((e=>\"kind\"!==e));return i&&t.every((e=>we(e)))&&t.every((t=>{const n=e[t].key;return null!=n&&\"\"!==n}))}return i&&o}return!1},getContextKeys:function(e,t=ye()){if(!e)return;const n={},{kind:r,key:o}=e;switch(r){case void 0:n.user=`${o}`;break;case\"multi\":Object.entries(e).filter((([e])=>\"kind\"!==e)).forEach((([e,t])=>{t&&t.key&&(n[e]=t.key)}));break;case null:t.warn(`null is not a valid context kind: ${e}`);break;case\"\":t.warn(`'' is not a valid context kind: ${e}`);break;default:n[r]=`${o}`}return n},getContextKinds:function(e){return e?null===e.kind||void 0===e.kind?[\"user\"]:\"multi\"!==e.kind?[e.kind]:Object.keys(e).filter((e=>\"kind\"!==e)):[]},getCanonicalKey:function(e){if(e){if((void 0===e.kind||null===e.kind||\"user\"===e.kind)&&e.key)return e.key;if(\"multi\"!==e.kind&&e.key)return`${e.kind}:${be(e.key)}`;if(\"multi\"===e.kind)return Object.keys(e).sort().filter((e=>\"kind\"!==e)).map((t=>`${t}:${be(e[t].key)}`)).join(\":\")}}};const{getContextKinds:Ee}=ke;var De=function(){const e={};let t=0,n=0,r={},o={};return e.summarizeEvent=e=>{if(\"feature\"===e.kind){const i=e.key+\":\"+(null!==e.variation&&void 0!==e.variation?e.variation:\"\")+\":\"+(null!==e.version&&void 0!==e.version?e.version:\"\"),a=r[i];let s=o[e.key];s||(s=new Set,o[e.key]=s),function(e){return e.context?Ee(e.context):e.contextKeys?Object.keys(e.contextKeys):[]}(e).forEach((e=>s.add(e))),a?a.count=a.count+1:r[i]={count:1,key:e.key,version:e.version,variation:e.variation,value:e.value,default:e.default},(0===t||e.creationDate<t)&&(t=e.creationDate),e.creationDate>n&&(n=e.creationDate)}},e.getSummary=()=>{const e={};let i=!0;for(const t of Object.values(r)){let n=e[t.key];n||(n={default:t.default,counters:[],contextKinds:[...o[t.key]]},e[t.key]=n);const r={value:t.value,count:t.count};void 0!==t.variation&&null!==t.variation&&(r.variation=t.variation),void 0!==t.version&&null!==t.version?r.version=t.version:r.unknown=!0,n.counters.push(r),i=!1}return i?null:{startDate:t,endDate:n,features:e}},e.clearSummary=()=>{t=0,n=0,r={},o={}},e};function xe(e){return e.replace(/~/g,\"~0\").replace(/\\//g,\"~1\")}function Ce(e){return(e.startsWith(\"/\")?e.substring(1):e).split(\"/\").map((e=>e.indexOf(\"~\")>=0?e.replace(/~1/g,\"/\").replace(/~0/g,\"~\"):e))}function Pe(e){return!e.startsWith(\"/\")}function Se(e,t){const n=Pe(e),r=Pe(t);if(n&&r)return e===t;if(n){const n=Ce(t);return 1===n.length&&e===n[0]}if(r){const n=Ce(e);return 1===n.length&&t===n[0]}return e===t}function Ie(e){return`/${xe(e)}`}var Oe={cloneExcluding:function(e,t){const n=[],r={},o=[];for(n.push(...Object.keys(e).map((t=>({key:t,ptr:Ie(t),source:e,parent:r,visited:[e]}))));n.length;){const e=n.pop();if(t.some((t=>Se(t,e.ptr))))o.push(e.ptr);else{const t=e.source[e.key];if(null===t)e.parent[e.key]=t;else if(Array.isArray(t))e.parent[e.key]=[...t];else if(\"object\"==typeof t){if(e.visited.includes(t))continue;e.parent[e.key]={},n.push(...Object.keys(t).map((n=>{return{key:n,ptr:(r=e.ptr,o=xe(n),`${r}/${o}`),source:t,parent:e.parent[e.key],visited:[...e.visited,t]};var r,o})))}else e.parent[e.key]=t}}return{cloned:r,excluded:o.sort()}},compare:Se,literalToReference:Ie};var Te=function(e){const t={},n=e.allAttributesPrivate,r=e.privateAttributes||[],o=[\"key\",\"kind\",\"_meta\",\"anonymous\"],i=[\"name\",\"ip\",\"firstName\",\"lastName\",\"email\",\"avatar\",\"country\"],a=(e,t)=>{if(\"object\"!=typeof e||null===e||Array.isArray(e))return;const{cloned:i,excluded:a}=Oe.cloneExcluding(e,((e,t)=>(n||t&&e.anonymous?Object.keys(e):[...r,...e._meta&&e._meta.privateAttributes||[]]).filter((e=>!o.some((t=>Oe.compare(e,t))))))(e,t));return i.key=String(i.key),a.length&&(i._meta||(i._meta={}),i._meta.redactedAttributes=a),i._meta&&(delete i._meta.privateAttributes,0===Object.keys(i._meta).length&&delete i._meta),void 0!==i.anonymous&&(i.anonymous=!!i.anonymous),i};return t.filter=(e,t=!1)=>void 0===e.kind||null===e.kind?a((e=>{const t={...e.custom||{},kind:\"user\",key:e.key};void 0!==e.anonymous&&(t.anonymous=!!e.anonymous);for(const n of i)delete t[n],void 0!==e[n]&&null!==e[n]&&(t[n]=String(e[n]));return void 0!==e.privateAttributeNames&&null!==e.privateAttributeNames&&(t._meta=t._meta||{},t._meta.privateAttributes=e.privateAttributeNames.map((e=>e.startsWith(\"/\")?Oe.literalToReference(e):e))),t})(e),t):\"multi\"===e.kind?((e,t)=>{const n={kind:e.kind},r=Object.keys(e);for(const o of r)if(\"kind\"!==o){const r=a(e[o],t);r&&(n[o]=r)}return n})(e,t):a(e,t),t};const{getContextKeys:Le}=ke;var Ue=function(e,t,n,r=null,o=null,i=null){const a={},c=i||he(e,n,t),u=S.appendUrlPath(t.eventsUrl,\"/events/bulk/\"+n),l=De(),d=Te(t),f=t.samplingInterval,g=t.eventCapacity,v=t.flushInterval,p=t.logger;let m,h=[],y=0,w=!1,b=!1;function k(){return 0===f||0===Math.floor(Math.random()*f)}function E(e){const t=S.extend({},e);return\"identify\"===e.kind?t.context=d.filter(e.context):\"feature\"===e.kind?t.context=d.filter(e.context,!0):(t.contextKeys=Le(e.context,p),delete t.context),\"feature\"===e.kind&&(delete t.trackEvents,delete t.debugEventsUntilDate),t}function D(e){h.length<g?(h.push(e),b=!1):(b||(b=!0,p.warn(ie.eventCapacityExceeded())),r&&r.incrementDroppedEvents())}return a.enqueue=function(e){if(w)return;let t=!1,n=!1;var r;if(l.summarizeEvent(e),\"feature\"===e.kind?k()&&(t=!!e.trackEvents,n=!!(r=e).debugEventsUntilDate&&r.debugEventsUntilDate>y&&r.debugEventsUntilDate>(new Date).getTime()):t=k(),t&&D(E(e)),n){const t=S.extend({},e,{kind:\"debug\"});t.context=d.filter(t.context),delete t.trackEvents,delete t.debugEventsUntilDate,D(t)}},a.flush=function(){if(w)return Promise.resolve();const e=h,t=l.getSummary();return l.clearSummary(),t&&(t.kind=\"summary\",e.push(t)),r&&r.setEventsInLastBatch(e.length),0===e.length?Promise.resolve():(h=[],p.debug(ie.debugPostingEvents(e.length)),c.sendEvents(e,u).then((e=>{e&&(e.serverTime&&(y=e.serverTime),s.isHttpErrorRecoverable(e.status)||(w=!0),e.status>=400&&S.onNextTick((()=>{o.maybeReportError(new s.LDUnexpectedResponseError(ie.httpErrorMessage(e.status,\"event posting\",\"some events were dropped\")))})))})))},a.start=function(){const e=()=>{a.flush(),m=setTimeout(e,v)};m=setTimeout(e,v)},a.stop=function(){clearTimeout(m)},a};var Re=function(e){const t={},n={};return t.on=function(e,t,r){n[e]=n[e]||[],n[e]=n[e].concat({handler:t,context:r})},t.off=function(e,t,r){if(n[e])for(let o=0;o<n[e].length;o++)n[e][o].handler===t&&n[e][o].context===r&&(n[e]=n[e].slice(0,o).concat(n[e].slice(o+1)))},t.emit=function(e){if(!n[e])return;const t=n[e].slice(0);for(let e=0;e<t.length;e++)t[e].handler.apply(t[e].context,Array.prototype.slice.call(arguments,1))},t.getEvents=function(){return Object.keys(n)},t.getEventListenerCount=function(e){return n[e]?n[e].length:0},t.maybeReportError=function(t){t&&(n[\"error\"]?this.emit(\"error\",t):(e||console).error(t.message))},t};const Ae=\"ready\",je=\"initialized\",Fe=\"failed\";var Ne=function(e){let t=!1,n=!1,r=null,o=null;const i=new Promise((t=>{const n=()=>{e.off(Ae,n),t()};e.on(Ae,n)})).catch((()=>{}));return{getInitializationPromise:()=>o||(t?Promise.resolve():n?Promise.reject(r):(o=new Promise(((t,n)=>{const r=()=>{e.off(je,r),t()},o=t=>{e.off(Fe,o),n(t)};e.on(je,r),e.on(Fe,o)})),o)),getReadyPromise:()=>i,signalSuccess:()=>{t||n||(t=!0,e.emit(je),e.emit(Ae))},signalFailure:o=>{t||n||(n=!0,r=o,e.emit(Fe,o),e.emit(Ae)),e.maybeReportError(o)}}};var $e=function(e,t,n,r){const o={};function i(){let e=\"\";const o=r.getContext();return o&&(e=n||S.btoa(JSON.stringify(o))),\"ld:\"+t+\":\"+e}return o.loadFlags=()=>e.get(i()).then((e=>{if(null==e)return null;try{let t=JSON.parse(e);if(t){const e=t.$schema;void 0===e||e<1?t=S.transformValuesToVersionedValues(t):delete t.$schema}return t}catch(e){return o.clearFlags().then((()=>null))}})),o.saveFlags=t=>{const n=S.extend({},t,{$schema:1});return e.set(i(),JSON.stringify(n))},o.clearFlags=()=>e.clear(i()),o};var Ve=function(e,t){const n={};let r=!1;const o=e=>{r||(r=!0,t.warn(ie.localStorageUnavailable(e)))};return n.isEnabled=()=>!!e,n.get=t=>new Promise((n=>{e?e.get(t).then(n).catch((e=>{o(e),n(void 0)})):n(void 0)})),n.set=(t,n)=>new Promise((r=>{e?e.set(t,n).then((()=>r(!0))).catch((e=>{o(e),r(!1)})):r(!1)})),n.clear=t=>new Promise((n=>{e?e.clear(t).then((()=>n(!0))).catch((e=>{o(e),n(!1)})):n(!1)})),n};const{appendUrlPath:Me,base64URLEncode:qe,objectHasOwnProperty:He}=S,{getLDHeaders:ze,transformHeaders:Ke}=ge,{isHttpErrorRecoverable:_e}=s;var Je=function(e,t,n,r){const o=t.streamUrl,i=t.logger,a={},s=Me(o,\"/eval/\"+n),c=t.useReport,u=t.evaluationReasons,l=t.streamReconnectDelay,d=ze(e,t);let f,g=!1,v=null,p=null,m=null,h=null,y=null,w=0;function b(){const e=(t=function(){const e=l*Math.pow(2,w);return e>3e4?3e4:e}(),t-Math.trunc(.5*Math.random()*t));var t;return w+=1,e}function k(e){if(e.status&&\"number\"==typeof e.status&&!_e(e.status))return x(),i.error(ie.unrecoverableStreamError(e)),void(p&&(clearTimeout(p),p=null));const t=b();g||(i.warn(ie.streamError(e,t)),g=!0),C(!1),x(),E(t)}function E(e){p||(e?p=setTimeout(D,e):D())}function D(){let r;p=null;let a=\"\";const l={headers:d,readTimeoutMillis:3e5};if(e.eventSourceFactory){null!=h&&(a=\"h=\"+h),c?e.eventSourceAllowsReport?(r=s,l.method=\"REPORT\",l.headers[\"Content-Type\"]=\"application/json\",l.body=JSON.stringify(m)):(r=Me(o,\"/ping/\"+n),a=\"\"):r=s+\"/\"+qe(JSON.stringify(m)),l.headers=Ke(l.headers,t),u&&(a=a+(a?\"&\":\"\")+\"withReasons=true\"),r=r+(a?\"?\":\"\")+a,x(),i.info(ie.streamConnecting(r)),f=(new Date).getTime(),v=e.eventSourceFactory(r,l);for(const e in y)He(y,e)&&v.addEventListener(e,y[e]);v.onerror=k,v.onopen=()=>{w=0}}}function x(){v&&(i.info(ie.streamClosing()),v.close(),v=null)}function C(e){f&&r&&r.recordStreamInit(f,!e,(new Date).getTime()-f),f=null}return a.connect=function(e,t,n){m=e,h=t,y={};for(const e in n||{})y[e]=function(t){g=!1,C(!0),n[e]&&n[e](t)};E()},a.disconnect=function(){clearTimeout(p),p=null,x()},a.isConnected=function(){return!!(v&&e.eventSourceIsActive&&e.eventSourceIsActive(v))},a};var Be=function(e){let t,n,r,o;const i={addPromise:(i,a)=>{t=i,n&&n(),n=a,i.then((n=>{t===i&&(r(n),e&&e())}),(n=>{t===i&&(o(n),e&&e())}))}};return i.resultPromise=new Promise(((e,t)=>{r=e,o=t})),i};const{transformHeaders:Ge,getLDHeaders:We}=ge,Xe=\"application/json\";var Qe=function(e,t,n){const r=t.baseUrl,o=t.useReport,i=t.evaluationReasons,a=t.logger,c={},u={};function l(n,r){if(!e.httpRequest)return new Promise(((e,t)=>{t(new s.LDFlagFetchError(ie.httpUnavailable()))}));const o=r?\"REPORT\":\"GET\",i=We(e,t);r&&(i[\"Content-Type\"]=Xe);let a=u[n];a||(a=Be((()=>{delete u[n]})),u[n]=a);const c=e.httpRequest(o,n,Ge(i,t),r),l=c.promise.then((e=>{if(200===e.status){if(e.header(\"content-type\")&&e.header(\"content-type\").substring(0,16)===Xe)return JSON.parse(e.body);{const t=ie.invalidContentType(e.header(\"content-type\")||\"\");return Promise.reject(new s.LDFlagFetchError(t))}}return Promise.reject(function(e){return 404===e.status?new s.LDInvalidEnvironmentIdError(ie.environmentNotFound()):new s.LDFlagFetchError(ie.errorFetchingFlags(e.statusText||String(e.status)))}(e))}),(e=>Promise.reject(new s.LDFlagFetchError(ie.networkError(e)))));return a.addPromise(l,(()=>{c.cancel&&c.cancel()})),a.resultPromise}return c.fetchJSON=function(e){return l(S.appendUrlPath(r,e),null)},c.fetchFlagSettings=function(e,t){let s,c,u,d=\"\";return o?(c=[r,\"/sdk/evalx/\",n,\"/context\"].join(\"\"),u=JSON.stringify(e)):(s=S.base64URLEncode(JSON.stringify(e)),c=[r,\"/sdk/evalx/\",n,\"/contexts/\",s].join(\"\")),t&&(d=\"h=\"+t),i&&(d=d+(d?\"&\":\"\")+\"withReasons=true\"),c=c+(d?\"?\":\"\")+d,a.debug(ie.debugPolling(c)),l(c,u)},c};var Ye=function(e,t){const n={};let r;return n.setContext=function(e){r=S.sanitizeContext(e),r&&t&&t(S.clone(r))},n.getContext=function(){return r?S.clone(r):null},e&&n.setContext(e),n};const{v1:Ze}=ee,{getContextKinds:et}=ke;var tt=function(e){function t(e){return null==e||\"user\"===e?\"ld:$anonUserId\":`ld:$contextKey:${e}`}function n(n,r){return null!==r.key&&void 0!==r.key?(r.key=r.key.toString(),Promise.resolve(r)):r.anonymous?function(n){return e.get(t(n))}(n).then((o=>{if(o)return r.key=o,r;{const o=Ze();return r.key=o,function(n,r){return e.set(t(r),n)}(o,n).then((()=>r))}})):Promise.reject(new s.LDInvalidUserError(ie.invalidContext()))}this.processContext=e=>{if(!e)return Promise.reject(new s.LDInvalidUserError(ie.contextNotSpecified()));const t=S.clone(e);if(\"multi\"===e.kind){const e=et(t);return Promise.all(e.map((e=>n(e,t[e])))).then((()=>t))}return n(e.kind,t)}};const{v1:nt}=ee,{baseOptionDefs:rt}=de,{appendUrlPath:ot}=S;var it={DiagnosticId:function(e){const t={diagnosticId:nt()};return e&&(t.sdkKeySuffix=e.length>6?e.substring(e.length-6):e),t},DiagnosticsAccumulator:function(e){let t,n,r,o;function i(e){t=e,n=0,r=0,o=[]}return i(e),{getProps:()=>({dataSinceDate:t,droppedEvents:n,eventsInLastBatch:r,streamInits:o}),setProps:e=>{t=e.dataSinceDate,n=e.droppedEvents||0,r=e.eventsInLastBatch||0,o=e.streamInits||[]},incrementDroppedEvents:()=>{n++},setEventsInLastBatch:e=>{r=e},recordStreamInit:(e,t,n)=>{const r={timestamp:e,failed:t,durationMillis:n};o.push(r)},reset:i}},DiagnosticsManager:function(e,t,n,r,o,i,a){const s=!!e.diagnosticUseCombinedEvent,c=\"ld:\"+o+\":$diagnostics\",u=ot(i.eventsUrl,\"/events/diagnostic/\"+o),l=i.diagnosticRecordingInterval,d=n;let f,g,v=!!i.streaming;const p={};function m(){return{sdk:w(),configuration:b(),platform:e.diagnosticPlatformData}}function h(e){i.logger&&i.logger.debug(ie.debugPostingDiagnosticEvent(e)),r.sendEvents(e,u,!0).then((()=>{})).catch((()=>{}))}function y(){h(function(){const e=(new Date).getTime();let t={kind:s?\"diagnostic-combined\":\"diagnostic\",id:a,creationDate:e,...d.getProps()};return s&&(t={...t,...m()}),d.reset(e),t}()),g=setTimeout(y,l),f=(new Date).getTime(),s&&function(){if(t.isEnabled()){const e={...d.getProps()};t.set(c,JSON.stringify(e))}}()}function w(){const t={...e.diagnosticSdkData};return i.wrapperName&&(t.wrapperName=i.wrapperName),i.wrapperVersion&&(t.wrapperVersion=i.wrapperVersion),t}function b(){return{customBaseURI:i.baseUrl!==rt.baseUrl.default,customStreamURI:i.streamUrl!==rt.streamUrl.default,customEventsURI:i.eventsUrl!==rt.eventsUrl.default,eventsCapacity:i.eventCapacity,eventsFlushIntervalMillis:i.flushInterval,reconnectTimeMillis:i.streamReconnectDelay,streamingDisabled:!v,allAttributesPrivate:!!i.allAttributesPrivate,diagnosticRecordingIntervalMillis:i.diagnosticRecordingInterval,usingSecureMode:!!i.hash,bootstrapMode:!!i.bootstrap,fetchGoalsDisabled:!i.fetchGoals,sendEventsOnlyForVariation:!!i.sendEventsOnlyForVariation}}return p.start=()=>{s?function(e){if(!t.isEnabled())return e(!1);t.get(c).then((t=>{if(t)try{const e=JSON.parse(t);d.setProps(e),f=e.dataSinceDate}catch(e){}e(!0)})).catch((()=>{e(!1)}))}((e=>{if(e){const e=(f||0)+l,t=(new Date).getTime();t>=e?y():g=setTimeout(y,e-t)}else 0===Math.floor(4*Math.random())?y():g=setTimeout(y,l)})):(h({kind:\"diagnostic-init\",id:a,creationDate:d.getProps().dataSinceDate,...m()}),g=setTimeout(y,l))},p.stop=()=>{g&&clearTimeout(g)},p.setStreaming=e=>{v=e},p}};var at=function(e,t){let n=!1;const r={type:e.type,name:e.name,synchronous:e.synchronous,method:(...o)=>{try{e.method(...o)}catch{n||(n=!0,t.warn(ie.inspectorMethodError(r.type,r.name)))}}};return r};const{onNextTick:st}=S,ct={flagUsed:\"flag-used\",flagDetailsChanged:\"flag-details-changed\",flagDetailChanged:\"flag-detail-changed\",clientIdentityChanged:\"client-identity-changed\"};Object.freeze(ct);var ut={InspectorTypes:ct,InspectorManager:function(e,t){const n={},r={[ct.flagUsed]:[],[ct.flagDetailsChanged]:[],[ct.flagDetailChanged]:[],[ct.clientIdentityChanged]:[]},o={[ct.flagUsed]:[],[ct.flagDetailsChanged]:[],[ct.flagDetailChanged]:[],[ct.clientIdentityChanged]:[]},i=e&&e.map((e=>at(e,t)));return i&&i.forEach((e=>{Object.prototype.hasOwnProperty.call(r,e.type)&&!e.synchronous?r[e.type].push(e):Object.prototype.hasOwnProperty.call(o,e.type)&&e.synchronous?o[e.type].push(e):t.warn(ie.invalidInspector(e.type,e.name))})),n.hasListeners=e=>r[e]&&r[e].length||o[e]&&o[e].length,n.onFlagUsed=(e,t,n)=>{const i=ct.flagUsed;o[i].length&&o[i].forEach((r=>r.method(e,t,n))),r[i].length&&st((()=>{r[i].forEach((r=>r.method(e,t,n)))}))},n.onFlags=e=>{const t=ct.flagDetailsChanged;o[t].length&&o[t].forEach((t=>t.method(e))),r[t].length&&st((()=>{r[t].forEach((t=>t.method(e)))}))},n.onFlagChanged=(e,t)=>{const n=ct.flagDetailChanged;o[n].length&&o[n].forEach((n=>n.method(e,t))),r[n].length&&st((()=>{r[n].forEach((n=>n.method(e,t)))}))},n.onIdentityChanged=e=>{const t=ct.clientIdentityChanged;o[t].length&&o[t].forEach((t=>t.method(e))),r[t].length&&st((()=>{r[t].forEach((t=>t.method(e)))}))},n}};const{LDTimeoutError:lt}=s;var dt=function(e,t){return new Promise(((n,r)=>{setTimeout((()=>{r(new lt(`${t} timed out after ${e} seconds.`))}),1e3*e)}))};const{commonBasicLogger:ft}=ne,{checkContext:gt,getContextKeys:vt}=ke,{InspectorTypes:pt,InspectorManager:mt}=ut,ht=\"change\",yt=\"internal-change\";var wt={initialize:function(e,t,n,r,o){const i=function(){if(n&&n.logger)return n.logger;return o&&o.logger&&o.logger.default||ft(\"warn\")}(),a=Re(i),c=Ne(a),u=de.validate(n,a,o,i),l=mt(u.inspectors,i),d=u.sendEvents;let f=e,g=u.hash;const v=Ve(r.localStorage,i),p=he(r,f,u),m=u.sendEvents&&!u.diagnosticOptOut,h=m?it.DiagnosticId(f):null,y=m?it.DiagnosticsAccumulator((new Date).getTime()):null,w=m?it.DiagnosticsManager(r,v,y,p,f,u,h):null,b=Je(r,u,f,y),k=u.eventProcessor||Ue(r,u,f,y,a,p),E=Qe(r,u,f);let D,x,C,P={},I=u.streaming,O=!1,T=!1,L=!0;const U=u.stateProvider,R=Ye(null,(function(e){(function(e){if(U)return;e&&F({kind:\"identify\",context:e,creationDate:(new Date).getTime()})})(e),l.hasListeners(pt.clientIdentityChanged)&&l.onIdentityChanged(R.getContext())})),A=new tt(v),j=v.isEnabled()?$e(v,f,g,R):null;function F(e){f&&(U&&U.enqueueEvent&&U.enqueueEvent(e)||(e.context?(L=!1,!d||T||r.isDoNotTrack()||(i.debug(ie.debugEnqueueingEvent(e.kind)),k.enqueue(e))):L&&(i.warn(ie.eventWithoutContext()),L=!1)))}function N(e,t){l.hasListeners(pt.flagDetailChanged)&&l.onFlagChanged(e.key,H(t))}function $(){l.hasListeners(pt.flagDetailsChanged)&&l.onFlags(Object.entries(P).map((([e,t])=>({key:e,detail:H(t)}))).reduce(((e,t)=>(e[t.key]=t.detail,e)),{}))}function V(e,t,n,r){const o=R.getContext(),i=new Date,a={kind:\"feature\",key:e,context:o,value:t?t.value:null,variation:t?t.variationIndex:null,default:n,creationDate:i.getTime()},s=P[e];s&&(a.version=s.flagVersion?s.flagVersion:s.version,a.trackEvents=s.trackEvents,a.debugEventsUntilDate=s.debugEventsUntilDate),(r||s&&s.trackReason)&&t&&(a.reason=t.reason),F(a)}function M(e){return gt(e,!1)?Promise.resolve(e):Promise.reject(new s.LDInvalidUserError(ie.invalidContext()))}function q(e,t,n,r,o,i){let a,s;return P&&S.objectHasOwnProperty(P,e)&&P[e]&&!P[e].deleted?(s=P[e],a=H(s),null!==s.value&&void 0!==s.value||(a.value=t)):a={value:t,variationIndex:null,reason:{kind:\"ERROR\",errorKind:\"FLAG_NOT_FOUND\"}},n&&(o||s?.prerequisites?.forEach((e=>{q(e,void 0,n,!1,!1,!1)})),V(e,a,t,r)),!o&&i&&function(e,t){l.hasListeners(pt.flagUsed)&&l.onFlagUsed(e,t,R.getContext())}(e,a),a}function H(e){return{value:e.value,variationIndex:void 0===e.variation?null:e.variation,reason:e.reason||null}}function z(){if(x=!0,!R.getContext())return;const e=e=>{try{return JSON.parse(e)}catch(e){return void a.maybeReportError(new s.LDInvalidDataError(ie.invalidData()))}};b.connect(R.getContext(),g,{ping:function(){i.debug(ie.debugStreamPing());const e=R.getContext();E.fetchFlagSettings(e,g).then((t=>{S.deepEquals(e,R.getContext())&&_(t||{})})).catch((e=>{a.maybeReportError(new s.LDFlagFetchError(ie.errorFetchingFlags(e)))}))},put:function(t){const n=e(t.data);n&&(i.debug(ie.debugStreamPut()),_(n))},patch:function(t){const n=e(t.data);if(!n)return;const r=P[n.key];if(!r||!r.version||!n.version||r.version<n.version){i.debug(ie.debugStreamPatch(n.key));const e={},t=S.extend({},n);delete t.key,P[n.key]=t;const o=H(t);e[n.key]=r?{previous:r.value,current:o}:{current:o},N(n,t),J(e)}else i.debug(ie.debugStreamPatchIgnored(n.key))},delete:function(t){const n=e(t.data);if(n)if(!P[n.key]||P[n.key].version<n.version){i.debug(ie.debugStreamDelete(n.key));const e={};P[n.key]&&!P[n.key].deleted&&(e[n.key]={previous:P[n.key].value}),P[n.key]={version:n.version,deleted:!0},N(n,P[n.key]),J(e)}else i.debug(ie.debugStreamDeleteIgnored(n.key))}})}function K(){x&&(b.disconnect(),x=!1)}function _(e){const t={};if(!e)return Promise.resolve();for(const n in P)S.objectHasOwnProperty(P,n)&&P[n]&&(e[n]&&!S.deepEquals(e[n].value,P[n].value)?t[n]={previous:P[n].value,current:H(e[n])}:e[n]&&!e[n].deleted||(t[n]={previous:P[n].value}));for(const n in e)S.objectHasOwnProperty(e,n)&&e[n]&&(!P[n]||P[n].deleted)&&(t[n]={current:H(e[n])});return P={...e},$(),J(t).catch((()=>{}))}function J(e){const t=Object.keys(e);if(t.length>0){const n={};t.forEach((t=>{const r=e[t].current,o=r?r.value:void 0,i=e[t].previous;a.emit(ht+\":\"+t,o,i),n[t]=r?{current:o,previous:i}:{previous:i}})),a.emit(ht,n),a.emit(yt,P),u.sendEventsOnlyForVariation||U||t.forEach((t=>{V(t,e[t].current)}))}return D&&j?j.saveFlags(P):Promise.resolve()}function B(){const e=I||C&&void 0===I;e&&!x?z():!e&&x&&K(),w&&w.setStreaming(e)}function G(e){return e===ht||e.substr(0,7)===ht+\":\"}if(\"string\"==typeof u.bootstrap&&\"LOCALSTORAGE\"===u.bootstrap.toUpperCase()&&(j?D=!0:i.warn(ie.localStorageUnavailable())),\"object\"==typeof u.bootstrap&&(P=function(e){const t=Object.keys(e),n=\"$flagsState\",r=\"$valid\",o=e[n];!o&&t.length&&i.warn(ie.bootstrapOldFormat()),!1===e[r]&&i.warn(ie.bootstrapInvalid());const a={};return t.forEach((t=>{if(t!==n&&t!==r){let n={value:e[t]};o&&o[t]?n=S.extend(n,o[t]):n.version=0,a[t]=n}})),a}(u.bootstrap)),U){const e=U.getInitialState();e?W(e):U.on(\"init\",W),U.on(\"update\",(function(e){e.context&&R.setContext(e.context);e.flags&&_(e.flags)}))}else(function(){if(!e)return Promise.reject(new s.LDInvalidEnvironmentIdError(ie.environmentNotSpecified()));return A.processContext(t).then(M).then((e=>(R.setContext(e),\"object\"==typeof u.bootstrap?X():D?j.loadFlags().then((e=>null==e?(P={},E.fetchFlagSettings(R.getContext(),g).then((e=>_(e||{}))).then(X).catch((e=>{Q(new s.LDFlagFetchError(ie.errorFetchingFlags(e)))}))):(P=e,S.onNextTick(X),E.fetchFlagSettings(R.getContext(),g).then((e=>_(e))).catch((e=>a.maybeReportError(e)))))):E.fetchFlagSettings(R.getContext(),g).then((e=>{P=e||{},$(),X()})).catch((e=>{P={},Q(e)})))))})().catch(Q);function W(e){f=e.environment,R.setContext(e.context),P={...e.flags},S.onNextTick(X)}function X(){i.info(ie.clientInitialized()),O=!0,B(),c.signalSuccess()}function Q(e){c.signalFailure(e)}const Y={waitForInitialization:function(e=void 0){if(null!=e){if(\"number\"==typeof e)return function(e){e>5&&i.warn(\"The waitForInitialization function was called with a timeout greater than 5 seconds. We recommend a timeout of 5 seconds or less.\");const t=c.getInitializationPromise(),n=dt(e,\"waitForInitialization\");return Promise.race([n,t]).catch((e=>{throw e instanceof s.LDTimeoutError&&i.error(`waitForInitialization error: ${e}`),e}))}(e);i.warn(\"The waitForInitialization method was provided with a non-numeric timeout.\")}return i.warn(\"The waitForInitialization function was called without a timeout specified. In a future version a default timeout will be applied.\"),c.getInitializationPromise()},waitUntilReady:()=>c.getReadyPromise(),identify:function(e,t,n){if(T)return S.wrapPromiseCallback(Promise.resolve({}),n);if(U)return i.warn(ie.identifyDisabled()),S.wrapPromiseCallback(Promise.resolve(S.transformVersionedValuesToValues(P)),n);const r=D&&j?j.clearFlags():Promise.resolve();return S.wrapPromiseCallback(r.then((()=>A.processContext(e))).then(M).then((e=>E.fetchFlagSettings(e,t).then((n=>{const r=S.transformVersionedValuesToValues(n);return R.setContext(e),g=t,n?_(n).then((()=>r)):r})))).then((e=>(x&&z(),e))).catch((e=>(a.maybeReportError(e),Promise.reject(e)))),n)},getContext:function(){return R.getContext()},variation:function(e,t){return q(e,t,!0,!1,!1,!0).value},variationDetail:function(e,t){return q(e,t,!0,!0,!1,!0)},track:function(e,t,n){if(\"string\"!=typeof e)return void a.maybeReportError(new s.LDInvalidEventKeyError(ie.unknownCustomEventKey(e)));void 0!==n&&\"number\"!=typeof n&&i.warn(ie.invalidMetricValue(typeof n)),r.customEventFilter&&!r.customEventFilter(e)&&i.warn(ie.unknownCustomEventKey(e));const o=R.getContext(),c={kind:\"custom\",key:e,context:o,url:r.getCurrentUrl(),creationDate:(new Date).getTime()};o&&o.anonymous&&(c.contextKind=o.anonymous?\"anonymousUser\":\"user\"),null!=t&&(c.data=t),null!=n&&(c.metricValue=n),F(c)},on:function(e,t,n){G(e)?(C=!0,O&&B(),a.on(e,t,n)):a.on(...arguments)},off:function(e){if(a.off(...arguments),G(e)){let e=!1;a.getEvents().forEach((t=>{G(t)&&a.getEventListenerCount(t)>0&&(e=!0)})),e||(C=!1,x&&void 0===I&&K())}},setStreaming:function(e){const t=null===e?void 0:e;t!==I&&(I=t,B())},flush:function(e){return S.wrapPromiseCallback(d?k.flush():Promise.resolve(),e)},allFlags:function(){const e={};if(!P)return e;for(const t in P)S.objectHasOwnProperty(P,t)&&!P[t].deleted&&(e[t]=q(t,null,!u.sendEventsOnlyForVariation,!1,!0,!1).value);return e},close:function(e){if(T)return S.wrapPromiseCallback(Promise.resolve(),e);const t=()=>{T=!0,P={}},n=Promise.resolve().then((()=>{if(K(),w&&w.stop(),d)return k.stop(),k.flush()})).then(t).catch(t);return S.wrapPromiseCallback(n,e)}};return{client:Y,options:u,emitter:a,ident:R,logger:i,requestor:E,start:function(){d&&(w&&w.start(),k.start())},enqueueEvent:F,getFlagsInternal:function(){return P},getEnvironmentId:()=>f,internalChangeEventName:yt}},commonBasicLogger:ft,errors:s,messages:ie,utils:S,getContextKeys:vt},bt=wt.initialize,kt=wt.errors,Et=wt.messages;function Dt(e,t,n){return(t=function(e){var t=function(e,t){if(\"object\"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||\"default\");if(\"object\"!=typeof r)return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}(e,\"string\");return\"symbol\"==typeof t?t:t+\"\"}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ct(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(n),!0).forEach((function(t){Dt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Pt=wt.commonBasicLogger;var St=function(e){return Pt(Ct({destination:console.log},e))};var It={promise:Promise.resolve({status:200,header:function(){return null},body:null})};function Ot(e,t,n,r,o){if(o&&!function(){var e=window.navigator&&window.navigator.userAgent;if(e){var t=e.match(/Chrom(e|ium)\\/([0-9]+)\\./);if(t)return parseInt(t[2],10)<73}return!0}())return It;var i=new window.XMLHttpRequest;for(var a in i.open(e,t,!o),n||{})Object.prototype.hasOwnProperty.call(n,a)&&i.setRequestHeader(a,n[a]);if(o){try{i.send(r)}catch(e){}return It}var s,c=new Promise((function(e,t){i.addEventListener(\"load\",(function(){s||e({status:i.status,header:function(e){return i.getResponseHeader(e)},body:i.responseText})})),i.addEventListener(\"error\",(function(){s||t(new Error)})),i.send(r)}));return{promise:c,cancel:function(){s=!0,i.abort()}}}var Tt=e=>{if(\"string\"!=typeof e)throw new TypeError(\"Expected a string\");return e.replace(/[|\\\\{}()[\\]^$+*?.]/g,\"\\\\$&\").replace(/-/g,\"\\\\x2d\")};function Lt(e,t,n,r){var o,i,a=((\"substring\"===e.kind||\"regex\"===e.kind)&&r.includes(\"/\")?t:t.replace(r,\"\")).replace(n,\"\");switch(e.kind){case\"exact\":i=t,o=new RegExp(\"^\"+Tt(e.url)+\"/?$\");break;case\"canonical\":i=a,o=new RegExp(\"^\"+Tt(e.url)+\"/?$\");break;case\"substring\":i=a,o=new RegExp(\".*\"+Tt(e.substring)+\".*$\");break;case\"regex\":i=a,o=new RegExp(e.pattern);break;default:return!1}return o.test(i)}function Ut(e,t){for(var n={},r=null,o=[],i=0;i<e.length;i++)for(var a=e[i],s=a.urls||[],c=0;c<s.length;c++)if(Lt(s[c],window.location.href,window.location.search,window.location.hash)){\"pageview\"===a.kind?t(\"pageview\",a):(o.push(a),t(\"click_pageview\",a));break}return o.length>0&&(r=function(e){for(var n=function(e,t){for(var n=[],r=0;r<t.length;r++)for(var o=e.target,i=t[r],a=i.selector,s=document.querySelectorAll(a);o&&s.length>0;){for(var c=0;c<s.length;c++)o===s[c]&&n.push(i);o=o.parentNode}return n}(e,o),r=0;r<n.length;r++)t(\"click\",n[r])},document.addEventListener(\"click\",r)),n.dispose=function(){document.removeEventListener(\"click\",r)},n}function Rt(e,t){var n,r;function o(){r&&r.dispose(),n&&n.length&&(r=Ut(n,i))}function i(t,n){var r=e.ident.getContext(),o={kind:t,key:n.key,data:null,url:window.location.href,creationDate:(new Date).getTime(),context:r};return\"click\"===t&&(o.selector=n.selector),e.enqueueEvent(o)}return e.requestor.fetchJSON(\"/sdk/goals/\"+e.getEnvironmentId()).then((function(e){e&&e.length>0&&(r=Ut(n=e,i),function(e,t){var n,r=window.location.href;function o(){(n=window.location.href)!==r&&(r=n,t())}!function e(t,n){t(),setTimeout((function(){e(t,n)}),n)}(o,e),window.history&&window.history.pushState?window.addEventListener(\"popstate\",o):window.addEventListener(\"hashchange\",o)}(300,o)),t()})).catch((function(n){e.emitter.maybeReportError(new kt.LDUnexpectedResponseError((n&&n.message,n.message))),t()})),{}}var At=\"goalsReady\",jt={fetchGoals:{default:!0},hash:{type:\"string\"},eventProcessor:{type:\"object\"},eventUrlTransformer:{type:\"function\"},disableSyncEventPost:{default:!1}};function Ft(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(e){var t,n={userAgentHeaderName:\"X-LaunchDarkly-User-Agent\",synchronousFlush:!1};if(window.XMLHttpRequest){var r=e&&e.disableSyncEventPost;n.httpRequest=function(e,t,o,i){var a=n.synchronousFlush&!r;return n.synchronousFlush=!1,Ot(e,t,o,i,a)}}n.httpAllowsPost=function(){return void 0===t&&(t=!!window.XMLHttpRequest&&\"withCredentials\"in new window.XMLHttpRequest),t},n.httpFallbackPing=function(e){(new window.Image).src=e};var o,i=e&&e.eventUrlTransformer;n.getCurrentUrl=function(){return i?i(window.location.href):window.location.href},n.isDoNotTrack=function(){var e;return 1===(e=window.navigator&&void 0!==window.navigator.doNotTrack?window.navigator.doNotTrack:window.navigator&&void 0!==window.navigator.msDoNotTrack?window.navigator.msDoNotTrack:window.doNotTrack)||!0===e||\"1\"===e||\"yes\"===e};try{window.localStorage&&(n.localStorage={get:function(e){return new Promise((function(t){t(window.localStorage.getItem(e))}))},set:function(e,t){return new Promise((function(n){window.localStorage.setItem(e,t),n()}))},clear:function(e){return new Promise((function(t){window.localStorage.removeItem(e),t()}))}})}catch(e){n.localStorage=null}if(e&&e.useReport&&\"function\"==typeof window.EventSourcePolyfill&&window.EventSourcePolyfill.supportedOptions&&window.EventSourcePolyfill.supportedOptions.method?(n.eventSourceAllowsReport=!0,o=window.EventSourcePolyfill):(n.eventSourceAllowsReport=!1,o=window.EventSource),window.EventSource){var a=3e5;n.eventSourceFactory=function(e,t){var n=Ct(Ct({},{heartbeatTimeout:a,silentTimeout:a,skipDefaultHeaders:!0}),t);return new o(e,n)},n.eventSourceIsActive=function(e){return e.readyState===window.EventSource.OPEN||e.readyState===window.EventSource.CONNECTING}}return n.userAgent=\"JSClient\",n.version=\"3.5.0\",n.diagnosticSdkData={name:\"js-client-sdk\",version:\"3.5.0\"},n.diagnosticPlatformData={name:\"JS\"},n.diagnosticUseCombinedEvent=!0,n}(n),o=bt(e,t,n,r,jt),i=o.client,a=o.options,s=o.emitter,c=new Promise((function(e){var t=s.on(At,(function(){s.off(At,t),e()}))}));i.waitUntilGoalsReady=function(){return c},a.fetchGoals?Rt(o,(function(){return s.emit(At)})):s.emit(At),\"complete\"!==document.readyState?window.addEventListener(\"load\",o.start):o.start();var u=function(){r.synchronousFlush=!0,i.flush().catch((function(){})),r.synchronousFlush=!1};return document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&u()})),window.addEventListener(\"pagehide\",u),i}var Nt=St,$t=void 0,Vt=\"3.5.0\";var Mt={initialize:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return console&&console.warn&&console.warn(Et.deprecated(\"default export\",\"named LDClient export\")),Ft(e,t,n)},version:Vt};\n//# sourceMappingURL=ldclient.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/launchdarkly-js-client-sdk/dist/ldclient.es.js\n");

/***/ })

};
;