"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/require-in-the-middle";
exports.ids = ["vendor-chunks/require-in-the-middle"];
exports.modules = {

/***/ "(ssr)/./node_modules/require-in-the-middle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/require-in-the-middle/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst debug = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(ssr)/./node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\nlet builtinModules // Set<string>\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else if (Module.builtinModules) { // Added in node v9.3.0, v8.10.0, v6.13.0\n  isCore = moduleName => {\n    if (moduleName.startsWith('node:')) {\n      return true\n    }\n\n    if (builtinModules === undefined) {\n      builtinModules = new Set(Module.builtinModules)\n    }\n\n    return builtinModules.has(moduleName)\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(ssr)/./node_modules/resolve/index.js\")\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  }\n}\n\n// Feature detection: This property was added in Node.js 8.9.0, the same time\n// as the `paths` options argument was added to the `require.resolve` function,\n// which is the one we want\nlet resolve\nif (__webpack_require__(\"(ssr)/./node_modules/require-in-the-middle sync recursive\").resolve.paths) {\n  resolve = function (moduleName, basedir) {\n    return __webpack_require__(\"(ssr)/./node_modules/require-in-the-middle sync recursive\").resolve(moduleName, { paths: [basedir] })\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(ssr)/./node_modules/resolve/index.js\")\n  resolve = function (moduleName, basedir) {\n    return _resolve.sync(moduleName, { basedir })\n  }\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(ssr)/./node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    return patchedRequire.call(this, arguments, false)\n  }\n\n  if (typeof process.getBuiltinModule === 'function') {\n    this._origGetBuiltinModule = process.getBuiltinModule\n    this._getBuiltinModule = process.getBuiltinModule = function (id) {\n      if (self._unhooked === true) {\n        // if the patched process.getBuiltinModule function could not be removed because\n        // someone else patched it after it was patched here, we just abort and pass the\n        // request onwards to the original process.getBuiltinModule\n        debug('ignoring process.getBuiltinModule call - module is soft-unhooked')\n        return self._origGetBuiltinModule.apply(this, arguments)\n      }\n\n      return patchedRequire.call(this, arguments, true)\n    }\n  }\n\n  // Preserve the original require/process.getBuiltinModule arguments in `args`\n  function patchedRequire (args, coreOnly) {\n    const id = args[0]\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else if (coreOnly) {\n      // `coreOnly` is `true` if this was a call to `process.getBuiltinModule`, in which case\n      // we don't want to return anything if the requested `id` isn't a core module. Falling\n      // back to default behaviour, which at the time of this wrting is simply returning `undefined`\n      debug('call to process.getBuiltinModule with unknown built-in id')\n      return self._origGetBuiltinModule.apply(this, args)\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, args)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = coreOnly\n      ? self._origGetBuiltinModule.apply(this, args)\n      : self._origRequire.apply(this, args)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      let matchFound = false\n      if (hasWhitelist) {\n        if (!id.startsWith('.') && modules.includes(id)) {\n          // Not starting with '.' means `id` is identifying a module path,\n          // as opposed to a local file path. (Note: I'm not sure about\n          // absolute paths, but those are handled above.)\n          // If this `id` is in `modules`, then this could be a match to an\n          // package \"exports\" entry point that wouldn't otherwise match below.\n          moduleName = id\n          matchFound = true\n        }\n\n        // abort if module name isn't on whitelist\n        if (!modules.includes(moduleName) && !modules.includes(fullModuleName)) {\n          return exports\n        }\n\n        if (modules.includes(fullModuleName) && fullModuleName !== moduleName) {\n          // if we get to this point, it means that we're requiring a whitelisted sub-module\n          moduleName = fullModuleName\n          matchFound = true\n        }\n      }\n\n      if (!matchFound) {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve(moduleName, basedir)\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('require unhook successful')\n  } else {\n    debug('require unhook unsuccessful')\n  }\n\n  if (process.getBuiltinModule !== undefined) {\n    if (this._getBuiltinModule === process.getBuiltinModule) {\n      process.getBuiltinModule = this._origGetBuiltinModule\n      debug('process.getBuiltinModule unhook successful')\n    } else {\n      debug('process.getBuiltinModule unhook unsuccessful')\n    }\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/require-in-the-middle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/require-in-the-middle/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst debug = __webpack_require__(/*! debug */ \"(instrument)/./node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(instrument)/./node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\nlet builtinModules // Set<string>\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else if (Module.builtinModules) { // Added in node v9.3.0, v8.10.0, v6.13.0\n  isCore = moduleName => {\n    if (moduleName.startsWith('node:')) {\n      return true\n    }\n\n    if (builtinModules === undefined) {\n      builtinModules = new Set(Module.builtinModules)\n    }\n\n    return builtinModules.has(moduleName)\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(instrument)/./node_modules/resolve/index.js\")\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  }\n}\n\n// Feature detection: This property was added in Node.js 8.9.0, the same time\n// as the `paths` options argument was added to the `require.resolve` function,\n// which is the one we want\nlet resolve\nif (__webpack_require__(\"(instrument)/./node_modules/require-in-the-middle sync recursive\").resolve.paths) {\n  resolve = function (moduleName, basedir) {\n    return __webpack_require__(\"(instrument)/./node_modules/require-in-the-middle sync recursive\").resolve(moduleName, { paths: [basedir] })\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(instrument)/./node_modules/resolve/index.js\")\n  resolve = function (moduleName, basedir) {\n    return _resolve.sync(moduleName, { basedir })\n  }\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(instrument)/./node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    return patchedRequire.call(this, arguments, false)\n  }\n\n  if (typeof process.getBuiltinModule === 'function') {\n    this._origGetBuiltinModule = process.getBuiltinModule\n    this._getBuiltinModule = process.getBuiltinModule = function (id) {\n      if (self._unhooked === true) {\n        // if the patched process.getBuiltinModule function could not be removed because\n        // someone else patched it after it was patched here, we just abort and pass the\n        // request onwards to the original process.getBuiltinModule\n        debug('ignoring process.getBuiltinModule call - module is soft-unhooked')\n        return self._origGetBuiltinModule.apply(this, arguments)\n      }\n\n      return patchedRequire.call(this, arguments, true)\n    }\n  }\n\n  // Preserve the original require/process.getBuiltinModule arguments in `args`\n  function patchedRequire (args, coreOnly) {\n    const id = args[0]\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else if (coreOnly) {\n      // `coreOnly` is `true` if this was a call to `process.getBuiltinModule`, in which case\n      // we don't want to return anything if the requested `id` isn't a core module. Falling\n      // back to default behaviour, which at the time of this wrting is simply returning `undefined`\n      debug('call to process.getBuiltinModule with unknown built-in id')\n      return self._origGetBuiltinModule.apply(this, args)\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, args)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = coreOnly\n      ? self._origGetBuiltinModule.apply(this, args)\n      : self._origRequire.apply(this, args)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      let matchFound = false\n      if (hasWhitelist) {\n        if (!id.startsWith('.') && modules.includes(id)) {\n          // Not starting with '.' means `id` is identifying a module path,\n          // as opposed to a local file path. (Note: I'm not sure about\n          // absolute paths, but those are handled above.)\n          // If this `id` is in `modules`, then this could be a match to an\n          // package \"exports\" entry point that wouldn't otherwise match below.\n          moduleName = id\n          matchFound = true\n        }\n\n        // abort if module name isn't on whitelist\n        if (!modules.includes(moduleName) && !modules.includes(fullModuleName)) {\n          return exports\n        }\n\n        if (modules.includes(fullModuleName) && fullModuleName !== moduleName) {\n          // if we get to this point, it means that we're requiring a whitelisted sub-module\n          moduleName = fullModuleName\n          matchFound = true\n        }\n      }\n\n      if (!matchFound) {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve(moduleName, basedir)\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('require unhook successful')\n  } else {\n    debug('require unhook unsuccessful')\n  }\n\n  if (process.getBuiltinModule !== undefined) {\n    if (this._getBuiltinModule === process.getBuiltinModule) {\n      process.getBuiltinModule = this._origGetBuiltinModule\n      debug('process.getBuiltinModule unhook successful')\n    } else {\n      debug('process.getBuiltinModule unhook unsuccessful')\n    }\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/require-in-the-middle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/require-in-the-middle/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst debug = __webpack_require__(/*! debug */ \"(action-browser)/./node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(action-browser)/./node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\nlet builtinModules // Set<string>\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else if (Module.builtinModules) { // Added in node v9.3.0, v8.10.0, v6.13.0\n  isCore = moduleName => {\n    if (moduleName.startsWith('node:')) {\n      return true\n    }\n\n    if (builtinModules === undefined) {\n      builtinModules = new Set(Module.builtinModules)\n    }\n\n    return builtinModules.has(moduleName)\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(action-browser)/./node_modules/resolve/index.js\")\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  }\n}\n\n// Feature detection: This property was added in Node.js 8.9.0, the same time\n// as the `paths` options argument was added to the `require.resolve` function,\n// which is the one we want\nlet resolve\nif (__webpack_require__(\"(action-browser)/./node_modules/require-in-the-middle sync recursive\").resolve.paths) {\n  resolve = function (moduleName, basedir) {\n    return __webpack_require__(\"(action-browser)/./node_modules/require-in-the-middle sync recursive\").resolve(moduleName, { paths: [basedir] })\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(action-browser)/./node_modules/resolve/index.js\")\n  resolve = function (moduleName, basedir) {\n    return _resolve.sync(moduleName, { basedir })\n  }\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(action-browser)/./node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    return patchedRequire.call(this, arguments, false)\n  }\n\n  if (typeof process.getBuiltinModule === 'function') {\n    this._origGetBuiltinModule = process.getBuiltinModule\n    this._getBuiltinModule = process.getBuiltinModule = function (id) {\n      if (self._unhooked === true) {\n        // if the patched process.getBuiltinModule function could not be removed because\n        // someone else patched it after it was patched here, we just abort and pass the\n        // request onwards to the original process.getBuiltinModule\n        debug('ignoring process.getBuiltinModule call - module is soft-unhooked')\n        return self._origGetBuiltinModule.apply(this, arguments)\n      }\n\n      return patchedRequire.call(this, arguments, true)\n    }\n  }\n\n  // Preserve the original require/process.getBuiltinModule arguments in `args`\n  function patchedRequire (args, coreOnly) {\n    const id = args[0]\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else if (coreOnly) {\n      // `coreOnly` is `true` if this was a call to `process.getBuiltinModule`, in which case\n      // we don't want to return anything if the requested `id` isn't a core module. Falling\n      // back to default behaviour, which at the time of this wrting is simply returning `undefined`\n      debug('call to process.getBuiltinModule with unknown built-in id')\n      return self._origGetBuiltinModule.apply(this, args)\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, args)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = coreOnly\n      ? self._origGetBuiltinModule.apply(this, args)\n      : self._origRequire.apply(this, args)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      let matchFound = false\n      if (hasWhitelist) {\n        if (!id.startsWith('.') && modules.includes(id)) {\n          // Not starting with '.' means `id` is identifying a module path,\n          // as opposed to a local file path. (Note: I'm not sure about\n          // absolute paths, but those are handled above.)\n          // If this `id` is in `modules`, then this could be a match to an\n          // package \"exports\" entry point that wouldn't otherwise match below.\n          moduleName = id\n          matchFound = true\n        }\n\n        // abort if module name isn't on whitelist\n        if (!modules.includes(moduleName) && !modules.includes(fullModuleName)) {\n          return exports\n        }\n\n        if (modules.includes(fullModuleName) && fullModuleName !== moduleName) {\n          // if we get to this point, it means that we're requiring a whitelisted sub-module\n          moduleName = fullModuleName\n          matchFound = true\n        }\n      }\n\n      if (!matchFound) {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve(moduleName, basedir)\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('require unhook successful')\n  } else {\n    debug('require unhook unsuccessful')\n  }\n\n  if (process.getBuiltinModule !== undefined) {\n    if (this._getBuiltinModule === process.getBuiltinModule) {\n      process.getBuiltinModule = this._origGetBuiltinModule\n      debug('process.getBuiltinModule unhook successful')\n    } else {\n      debug('process.getBuiltinModule unhook unsuccessful')\n    }\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZXF1aXJlLWluLXRoZS1taWRkbGUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosYUFBYSxtQkFBTyxDQUFDLGtCQUFNO0FBQzNCLGVBQWUsbUJBQU8sQ0FBQyxzQkFBUTtBQUMvQixjQUFjLG1CQUFPLENBQUMsaUVBQU87QUFDN0IsOEJBQThCLG1CQUFPLENBQUMsbUdBQTBCOztBQUVoRTtBQUNBO0FBQ0EsY0FBYyxPQUFPO0FBQ3JCO0FBQ0EsbUJBQW1COztBQUVuQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0EsRUFBRSxrQ0FBa0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxFQUFFO0FBQ0YsbUJBQW1CLG1CQUFPLENBQUMsaUVBQVM7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyRkFBTztBQUNYO0FBQ0EsV0FBVywyRkFBTyx1QkFBdUIsa0JBQWtCO0FBQzNEO0FBQ0EsRUFBRTtBQUNGLG1CQUFtQixtQkFBTyxDQUFDLGlFQUFTO0FBQ3BDO0FBQ0EsdUNBQXVDLFNBQVM7QUFDaEQ7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sa0JBQWtCLHFCQUFhO0FBQy9CO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixrQkFBa0IscUJBQWE7QUFDL0I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU0scUJBQXFCLHFCQUFhO0FBQ3hDLE1BQU0scUJBQWE7QUFDbkIsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsdUdBQXVHLDBIQUFrQztBQUN6STtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVxdWlyZS1pbi10aGUtbWlkZGxlL2luZGV4LmpzPzAzMTYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IHBhdGggPSByZXF1aXJlKCdwYXRoJylcbmNvbnN0IE1vZHVsZSA9IHJlcXVpcmUoJ21vZHVsZScpXG5jb25zdCBkZWJ1ZyA9IHJlcXVpcmUoJ2RlYnVnJykoJ3JlcXVpcmUtaW4tdGhlLW1pZGRsZScpXG5jb25zdCBtb2R1bGVEZXRhaWxzRnJvbVBhdGggPSByZXF1aXJlKCdtb2R1bGUtZGV0YWlscy1mcm9tLXBhdGgnKVxuXG4vLyBVc2luZyB0aGUgZGVmYXVsdCBleHBvcnQgaXMgZGlzY291cmFnZWQsIGJ1dCBrZXB0IGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5LlxuLy8gVXNlIHRoaXMgaW5zdGVhZDpcbi8vICAgIGNvbnN0IHsgSG9vayB9ID0gcmVxdWlyZSgncmVxdWlyZS1pbi10aGUtbWlkZGxlJylcbm1vZHVsZS5leHBvcnRzID0gSG9va1xubW9kdWxlLmV4cG9ydHMuSG9vayA9IEhvb2tcblxubGV0IGJ1aWx0aW5Nb2R1bGVzIC8vIFNldDxzdHJpbmc+XG5cbi8qKlxuICogSXMgdGhlIGdpdmVuIG1vZHVsZSBhIFwiY29yZVwiIG1vZHVsZT9cbiAqIGh0dHBzOi8vbm9kZWpzLm9yZy9hcGkvbW9kdWxlcy5odG1sI2NvcmUtbW9kdWxlc1xuICpcbiAqIEB0eXBlIHsobW9kdWxlTmFtZTogc3RyaW5nKSA9PiBib29sZWFufVxuICovXG5sZXQgaXNDb3JlXG5pZiAoTW9kdWxlLmlzQnVpbHRpbikgeyAvLyBBZGRlZCBpbiBub2RlIHYxOC42LjAsIHYxNi4xNy4wXG4gIGlzQ29yZSA9IE1vZHVsZS5pc0J1aWx0aW5cbn0gZWxzZSBpZiAoTW9kdWxlLmJ1aWx0aW5Nb2R1bGVzKSB7IC8vIEFkZGVkIGluIG5vZGUgdjkuMy4wLCB2OC4xMC4wLCB2Ni4xMy4wXG4gIGlzQ29yZSA9IG1vZHVsZU5hbWUgPT4ge1xuICAgIGlmIChtb2R1bGVOYW1lLnN0YXJ0c1dpdGgoJ25vZGU6JykpIHtcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfVxuXG4gICAgaWYgKGJ1aWx0aW5Nb2R1bGVzID09PSB1bmRlZmluZWQpIHtcbiAgICAgIGJ1aWx0aW5Nb2R1bGVzID0gbmV3IFNldChNb2R1bGUuYnVpbHRpbk1vZHVsZXMpXG4gICAgfVxuXG4gICAgcmV0dXJuIGJ1aWx0aW5Nb2R1bGVzLmhhcyhtb2R1bGVOYW1lKVxuICB9XG59IGVsc2Uge1xuICBjb25zdCBfcmVzb2x2ZSA9IHJlcXVpcmUoJ3Jlc29sdmUnKVxuICBjb25zdCBbbWFqb3IsIG1pbm9yXSA9IHByb2Nlc3MudmVyc2lvbnMubm9kZS5zcGxpdCgnLicpLm1hcChOdW1iZXIpXG4gIGlmIChtYWpvciA9PT0gOCAmJiBtaW5vciA8IDgpIHtcbiAgICAvLyBGb3Igbm9kZSB2ZXJzaW9ucyBgWzguMCwgOC44KWAgdGhlIFwiaHR0cDJcIiBtb2R1bGUgd2FzIGJ1aWx0LWluIGJ1dFxuICAgIC8vIGJlaGluZCB0aGUgYC0tZXhwb3NlLWh0dHAyYCBmbGFnLiBgcmVzb2x2ZWAgb25seSBjb25zaWRlcnMgdW5mbGFnZ2VkXG4gICAgLy8gbW9kdWxlcyB0byBiZSBjb3JlOiBodHRwczovL2dpdGh1Yi5jb20vYnJvd3NlcmlmeS9yZXNvbHZlL2lzc3Vlcy8xMzlcbiAgICAvLyBIb3dldmVyLCBmb3IgYEV4cG9ydHNDYWNoZWAgdG8gd29yayBmb3IgXCJodHRwMlwiIHdlIG5lZWQgaXQgdG8gYmVcbiAgICAvLyBjb25zaWRlcmVkIGNvcmUuXG4gICAgaXNDb3JlID0gbW9kdWxlTmFtZSA9PiB7XG4gICAgICBpZiAobW9kdWxlTmFtZSA9PT0gJ2h0dHAyJykge1xuICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgfVxuICAgICAgLy8gUHJlZmVyIGByZXNvbHZlLmNvcmVgIGxvb2t1cCB0byBgcmVzb2x2ZS5pc0NvcmUobW9kdWxlTmFtZSlgIGJlY2F1c2VcbiAgICAgIC8vIHRoZSBsYXR0ZXIgaXMgZG9pbmcgdmVyc2lvbiByYW5nZSBtYXRjaGVzIGZvciBldmVyeSBjYWxsLlxuICAgICAgcmV0dXJuICEhX3Jlc29sdmUuY29yZVttb2R1bGVOYW1lXVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBpc0NvcmUgPSBtb2R1bGVOYW1lID0+IHtcbiAgICAgIC8vIFByZWZlciBgcmVzb2x2ZS5jb3JlYCBsb29rdXAgdG8gYHJlc29sdmUuaXNDb3JlKG1vZHVsZU5hbWUpYCBiZWNhdXNlXG4gICAgICAvLyB0aGUgbGF0dGVyIGlzIGRvaW5nIHZlcnNpb24gcmFuZ2UgbWF0Y2hlcyBmb3IgZXZlcnkgY2FsbC5cbiAgICAgIHJldHVybiAhIV9yZXNvbHZlLmNvcmVbbW9kdWxlTmFtZV1cbiAgICB9XG4gIH1cbn1cblxuLy8gRmVhdHVyZSBkZXRlY3Rpb246IFRoaXMgcHJvcGVydHkgd2FzIGFkZGVkIGluIE5vZGUuanMgOC45LjAsIHRoZSBzYW1lIHRpbWVcbi8vIGFzIHRoZSBgcGF0aHNgIG9wdGlvbnMgYXJndW1lbnQgd2FzIGFkZGVkIHRvIHRoZSBgcmVxdWlyZS5yZXNvbHZlYCBmdW5jdGlvbixcbi8vIHdoaWNoIGlzIHRoZSBvbmUgd2Ugd2FudFxubGV0IHJlc29sdmVcbmlmIChyZXF1aXJlLnJlc29sdmUucGF0aHMpIHtcbiAgcmVzb2x2ZSA9IGZ1bmN0aW9uIChtb2R1bGVOYW1lLCBiYXNlZGlyKSB7XG4gICAgcmV0dXJuIHJlcXVpcmUucmVzb2x2ZShtb2R1bGVOYW1lLCB7IHBhdGhzOiBbYmFzZWRpcl0gfSlcbiAgfVxufSBlbHNlIHtcbiAgY29uc3QgX3Jlc29sdmUgPSByZXF1aXJlKCdyZXNvbHZlJylcbiAgcmVzb2x2ZSA9IGZ1bmN0aW9uIChtb2R1bGVOYW1lLCBiYXNlZGlyKSB7XG4gICAgcmV0dXJuIF9yZXNvbHZlLnN5bmMobW9kdWxlTmFtZSwgeyBiYXNlZGlyIH0pXG4gIH1cbn1cblxuLy8gJ2Zvby9iYXIuanMnIG9yICdmb28vYmFyL2luZGV4LmpzJyA9PiAnZm9vL2JhcidcbmNvbnN0IG5vcm1hbGl6ZSA9IC8oWy9cXFxcXWluZGV4KT8oXFwuanMpPyQvXG5cbi8vIENhY2hlIGBvbnJlcXVpcmVgLXBhdGNoZWQgZXhwb3J0cyBmb3IgbW9kdWxlcy5cbi8vXG4vLyBFeHBvcnRzIGZvciBidWlsdC1pbiAoYS5rLmEuIFwiY29yZVwiKSBtb2R1bGVzIGFyZSBzdG9yZWQgaW4gYW4gaW50ZXJuYWwgTWFwLlxuLy9cbi8vIEV4cG9ydHMgZm9yIG5vbi1jb3JlIG1vZHVsZXMgYXJlIHN0b3JlZCBvbiBhIHByaXZhdGUgZmllbGQgb24gdGhlIGBNb2R1bGVgXG4vLyBvYmplY3QgaW4gYHJlcXVpcmUuY2FjaGVgLiBUaGlzIGFsbG93cyB1c2VycyB0byBkZWxldGUgZnJvbSBgcmVxdWlyZS5jYWNoZWBcbi8vIHRvIHRyaWdnZXIgYSByZS1sb2FkIChhbmQgcmUtcnVuIG9mIHRoZSBob29rJ3MgYG9ucmVxdWlyZWApIG9mIGEgbW9kdWxlIHRoZVxuLy8gbmV4dCB0aW1lIGl0IGlzIHJlcXVpcmVkLlxuLy8gaHR0cHM6Ly9ub2RlanMub3JnL2RvY3MvbGF0ZXN0L2FwaS9hbGwuaHRtbCNhbGxfbW9kdWxlc19yZXF1aXJlY2FjaGVcbi8vXG4vLyBJbiBzb21lIHNwZWNpYWwgY2FzZXMgLS0gZS5nLiBzb21lIG90aGVyIGByZXF1aXJlKClgIGhvb2sgc3dhcHBpbmcgb3V0XG4vLyBgTW9kdWxlLl9jYWNoZWAgbGlrZSBgQGJhYmVsL3JlZ2lzdGVyYCAtLSBhIG5vbi1jb3JlIG1vZHVsZSB3b24ndCBiZSBpblxuLy8gYHJlcXVpcmUuY2FjaGVgLiBJbiB0aGF0IGNhc2UgdGhpcyBmYWxscyBiYWNrIHRvIGNhY2hpbmcgb24gdGhlIGludGVybmFsIE1hcC5cbmNsYXNzIEV4cG9ydHNDYWNoZSB7XG4gIGNvbnN0cnVjdG9yICgpIHtcbiAgICB0aGlzLl9sb2NhbENhY2hlID0gbmV3IE1hcCgpIC8vIDxtb2R1bGUgZmlsZW5hbWUgb3IgaWQ+IC0+IDxleHBvcnRzPlxuICAgIHRoaXMuX2tSaXRtRXhwb3J0cyA9IFN5bWJvbCgnUml0bUV4cG9ydHMnKVxuICB9XG5cbiAgaGFzIChmaWxlbmFtZSwgaXNCdWlsdGluKSB7XG4gICAgaWYgKHRoaXMuX2xvY2FsQ2FjaGUuaGFzKGZpbGVuYW1lKSkge1xuICAgICAgcmV0dXJuIHRydWVcbiAgICB9IGVsc2UgaWYgKCFpc0J1aWx0aW4pIHtcbiAgICAgIGNvbnN0IG1vZCA9IHJlcXVpcmUuY2FjaGVbZmlsZW5hbWVdXG4gICAgICByZXR1cm4gISEobW9kICYmIHRoaXMuX2tSaXRtRXhwb3J0cyBpbiBtb2QpXG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cbiAgfVxuXG4gIGdldCAoZmlsZW5hbWUsIGlzQnVpbHRpbikge1xuICAgIGNvbnN0IGNhY2hlZEV4cG9ydHMgPSB0aGlzLl9sb2NhbENhY2hlLmdldChmaWxlbmFtZSlcbiAgICBpZiAoY2FjaGVkRXhwb3J0cyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm4gY2FjaGVkRXhwb3J0c1xuICAgIH0gZWxzZSBpZiAoIWlzQnVpbHRpbikge1xuICAgICAgY29uc3QgbW9kID0gcmVxdWlyZS5jYWNoZVtmaWxlbmFtZV1cbiAgICAgIHJldHVybiAobW9kICYmIG1vZFt0aGlzLl9rUml0bUV4cG9ydHNdKVxuICAgIH1cbiAgfVxuXG4gIHNldCAoZmlsZW5hbWUsIGV4cG9ydHMsIGlzQnVpbHRpbikge1xuICAgIGlmIChpc0J1aWx0aW4pIHtcbiAgICAgIHRoaXMuX2xvY2FsQ2FjaGUuc2V0KGZpbGVuYW1lLCBleHBvcnRzKVxuICAgIH0gZWxzZSBpZiAoZmlsZW5hbWUgaW4gcmVxdWlyZS5jYWNoZSkge1xuICAgICAgcmVxdWlyZS5jYWNoZVtmaWxlbmFtZV1bdGhpcy5fa1JpdG1FeHBvcnRzXSA9IGV4cG9ydHNcbiAgICB9IGVsc2Uge1xuICAgICAgZGVidWcoJ25vbi1jb3JlIG1vZHVsZSBpcyB1bmV4cGVjdGVkbHkgbm90IGluIHJlcXVpcmUuY2FjaGU6IFwiJXNcIicsIGZpbGVuYW1lKVxuICAgICAgdGhpcy5fbG9jYWxDYWNoZS5zZXQoZmlsZW5hbWUsIGV4cG9ydHMpXG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIEhvb2sgKG1vZHVsZXMsIG9wdGlvbnMsIG9ucmVxdWlyZSkge1xuICBpZiAoKHRoaXMgaW5zdGFuY2VvZiBIb29rKSA9PT0gZmFsc2UpIHJldHVybiBuZXcgSG9vayhtb2R1bGVzLCBvcHRpb25zLCBvbnJlcXVpcmUpXG4gIGlmICh0eXBlb2YgbW9kdWxlcyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIG9ucmVxdWlyZSA9IG1vZHVsZXNcbiAgICBtb2R1bGVzID0gbnVsbFxuICAgIG9wdGlvbnMgPSBudWxsXG4gIH0gZWxzZSBpZiAodHlwZW9mIG9wdGlvbnMgPT09ICdmdW5jdGlvbicpIHtcbiAgICBvbnJlcXVpcmUgPSBvcHRpb25zXG4gICAgb3B0aW9ucyA9IG51bGxcbiAgfVxuXG4gIGlmICh0eXBlb2YgTW9kdWxlLl9yZXNvbHZlRmlsZW5hbWUgIT09ICdmdW5jdGlvbicpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvcjogRXhwZWN0ZWQgTW9kdWxlLl9yZXNvbHZlRmlsZW5hbWUgdG8gYmUgYSBmdW5jdGlvbiAod2FzOiAlcykgLSBhYm9ydGluZyEnLCB0eXBlb2YgTW9kdWxlLl9yZXNvbHZlRmlsZW5hbWUpXG4gICAgY29uc29sZS5lcnJvcignUGxlYXNlIHJlcG9ydCB0aGlzIGVycm9yIGFzIGFuIGlzc3VlIHJlbGF0ZWQgdG8gTm9kZS5qcyAlcyBhdCAlcycsIHByb2Nlc3MudmVyc2lvbiwgcmVxdWlyZSgnLi9wYWNrYWdlLmpzb24nKS5idWdzLnVybClcbiAgICByZXR1cm5cbiAgfVxuXG4gIHRoaXMuX2NhY2hlID0gbmV3IEV4cG9ydHNDYWNoZSgpXG5cbiAgdGhpcy5fdW5ob29rZWQgPSBmYWxzZVxuICB0aGlzLl9vcmlnUmVxdWlyZSA9IE1vZHVsZS5wcm90b3R5cGUucmVxdWlyZVxuXG4gIGNvbnN0IHNlbGYgPSB0aGlzXG4gIGNvbnN0IHBhdGNoaW5nID0gbmV3IFNldCgpXG4gIGNvbnN0IGludGVybmFscyA9IG9wdGlvbnMgPyBvcHRpb25zLmludGVybmFscyA9PT0gdHJ1ZSA6IGZhbHNlXG4gIGNvbnN0IGhhc1doaXRlbGlzdCA9IEFycmF5LmlzQXJyYXkobW9kdWxlcylcblxuICBkZWJ1ZygncmVnaXN0ZXJpbmcgcmVxdWlyZSBob29rJylcblxuICB0aGlzLl9yZXF1aXJlID0gTW9kdWxlLnByb3RvdHlwZS5yZXF1aXJlID0gZnVuY3Rpb24gKGlkKSB7XG4gICAgaWYgKHNlbGYuX3VuaG9va2VkID09PSB0cnVlKSB7XG4gICAgICAvLyBpZiB0aGUgcGF0Y2hlZCByZXF1aXJlIGZ1bmN0aW9uIGNvdWxkIG5vdCBiZSByZW1vdmVkIGJlY2F1c2VcbiAgICAgIC8vIHNvbWVvbmUgZWxzZSBwYXRjaGVkIGl0IGFmdGVyIGl0IHdhcyBwYXRjaGVkIGhlcmUsIHdlIGp1c3RcbiAgICAgIC8vIGFib3J0IGFuZCBwYXNzIHRoZSByZXF1ZXN0IG9ud2FyZHMgdG8gdGhlIG9yaWdpbmFsIHJlcXVpcmVcbiAgICAgIGRlYnVnKCdpZ25vcmluZyByZXF1aXJlIGNhbGwgLSBtb2R1bGUgaXMgc29mdC11bmhvb2tlZCcpXG4gICAgICByZXR1cm4gc2VsZi5fb3JpZ1JlcXVpcmUuYXBwbHkodGhpcywgYXJndW1lbnRzKVxuICAgIH1cblxuICAgIHJldHVybiBwYXRjaGVkUmVxdWlyZS5jYWxsKHRoaXMsIGFyZ3VtZW50cywgZmFsc2UpXG4gIH1cblxuICBpZiAodHlwZW9mIHByb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHRoaXMuX29yaWdHZXRCdWlsdGluTW9kdWxlID0gcHJvY2Vzcy5nZXRCdWlsdGluTW9kdWxlXG4gICAgdGhpcy5fZ2V0QnVpbHRpbk1vZHVsZSA9IHByb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSA9IGZ1bmN0aW9uIChpZCkge1xuICAgICAgaWYgKHNlbGYuX3VuaG9va2VkID09PSB0cnVlKSB7XG4gICAgICAgIC8vIGlmIHRoZSBwYXRjaGVkIHByb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSBmdW5jdGlvbiBjb3VsZCBub3QgYmUgcmVtb3ZlZCBiZWNhdXNlXG4gICAgICAgIC8vIHNvbWVvbmUgZWxzZSBwYXRjaGVkIGl0IGFmdGVyIGl0IHdhcyBwYXRjaGVkIGhlcmUsIHdlIGp1c3QgYWJvcnQgYW5kIHBhc3MgdGhlXG4gICAgICAgIC8vIHJlcXVlc3Qgb253YXJkcyB0byB0aGUgb3JpZ2luYWwgcHJvY2Vzcy5nZXRCdWlsdGluTW9kdWxlXG4gICAgICAgIGRlYnVnKCdpZ25vcmluZyBwcm9jZXNzLmdldEJ1aWx0aW5Nb2R1bGUgY2FsbCAtIG1vZHVsZSBpcyBzb2Z0LXVuaG9va2VkJylcbiAgICAgICAgcmV0dXJuIHNlbGYuX29yaWdHZXRCdWlsdGluTW9kdWxlLmFwcGx5KHRoaXMsIGFyZ3VtZW50cylcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHBhdGNoZWRSZXF1aXJlLmNhbGwodGhpcywgYXJndW1lbnRzLCB0cnVlKVxuICAgIH1cbiAgfVxuXG4gIC8vIFByZXNlcnZlIHRoZSBvcmlnaW5hbCByZXF1aXJlL3Byb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSBhcmd1bWVudHMgaW4gYGFyZ3NgXG4gIGZ1bmN0aW9uIHBhdGNoZWRSZXF1aXJlIChhcmdzLCBjb3JlT25seSkge1xuICAgIGNvbnN0IGlkID0gYXJnc1swXVxuICAgIGNvbnN0IGNvcmUgPSBpc0NvcmUoaWQpXG4gICAgbGV0IGZpbGVuYW1lIC8vIHRoZSBzdHJpbmcgdXNlZCBmb3IgY2FjaGluZ1xuICAgIGlmIChjb3JlKSB7XG4gICAgICBmaWxlbmFtZSA9IGlkXG4gICAgICAvLyBJZiB0aGlzIGlzIGEgYnVpbHRpbiBtb2R1bGUgdGhhdCBjYW4gYmUgaWRlbnRpZmllZCBib3RoIGFzICdmb28nIGFuZFxuICAgICAgLy8gJ25vZGU6Zm9vJywgdGhlbiBwcmVmZXIgJ2ZvbycgYXMgdGhlIGNhY2hpbmcga2V5LlxuICAgICAgaWYgKGlkLnN0YXJ0c1dpdGgoJ25vZGU6JykpIHtcbiAgICAgICAgY29uc3QgaWRXaXRob3V0UHJlZml4ID0gaWQuc2xpY2UoNSlcbiAgICAgICAgaWYgKGlzQ29yZShpZFdpdGhvdXRQcmVmaXgpKSB7XG4gICAgICAgICAgZmlsZW5hbWUgPSBpZFdpdGhvdXRQcmVmaXhcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoY29yZU9ubHkpIHtcbiAgICAgIC8vIGBjb3JlT25seWAgaXMgYHRydWVgIGlmIHRoaXMgd2FzIGEgY2FsbCB0byBgcHJvY2Vzcy5nZXRCdWlsdGluTW9kdWxlYCwgaW4gd2hpY2ggY2FzZVxuICAgICAgLy8gd2UgZG9uJ3Qgd2FudCB0byByZXR1cm4gYW55dGhpbmcgaWYgdGhlIHJlcXVlc3RlZCBgaWRgIGlzbid0IGEgY29yZSBtb2R1bGUuIEZhbGxpbmdcbiAgICAgIC8vIGJhY2sgdG8gZGVmYXVsdCBiZWhhdmlvdXIsIHdoaWNoIGF0IHRoZSB0aW1lIG9mIHRoaXMgd3J0aW5nIGlzIHNpbXBseSByZXR1cm5pbmcgYHVuZGVmaW5lZGBcbiAgICAgIGRlYnVnKCdjYWxsIHRvIHByb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSB3aXRoIHVua25vd24gYnVpbHQtaW4gaWQnKVxuICAgICAgcmV0dXJuIHNlbGYuX29yaWdHZXRCdWlsdGluTW9kdWxlLmFwcGx5KHRoaXMsIGFyZ3MpXG4gICAgfSBlbHNlIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGZpbGVuYW1lID0gTW9kdWxlLl9yZXNvbHZlRmlsZW5hbWUoaWQsIHRoaXMpXG4gICAgICB9IGNhdGNoIChyZXNvbHZlRXJyKSB7XG4gICAgICAgIC8vIElmIHNvbWVvbmUgKmVsc2UqIG1vbmtleS1wYXRjaGVzIGJlZm9yZSB0aGlzIG1vbmtleS1wYXRjaCwgdGhlbiB0aGF0XG4gICAgICAgIC8vIGNvZGUgbWlnaHQgZXhwZWN0IGByZXF1aXJlKHNvbWVJZClgIHRvIGdldCB0aHJvdWdoIHNvIGl0IGNhbiBiZVxuICAgICAgICAvLyBoYW5kbGVkLCBldmVuIGlmIGBzb21lSWRgIGNhbm5vdCBiZSByZXNvbHZlZCB0byBhIGZpbGVuYW1lLiBJbiB0aGlzXG4gICAgICAgIC8vIGNhc2UsIGluc3RlYWQgb2YgdGhyb3dpbmcgd2UgZGVmZXIgdG8gdGhlIHVuZGVybHlpbmcgYHJlcXVpcmVgLlxuICAgICAgICAvL1xuICAgICAgICAvLyBGb3IgZXhhbXBsZSB0aGUgQXp1cmUgRnVuY3Rpb25zIE5vZGUuanMgd29ya2VyIG1vZHVsZSBkb2VzIHRoaXMsXG4gICAgICAgIC8vIHdoZXJlIGBAYXp1cmUvZnVuY3Rpb25zLWNvcmVgIHJlc29sdmVzIHRvIGFuIGludGVybmFsIG9iamVjdC5cbiAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL0F6dXJlL2F6dXJlLWZ1bmN0aW9ucy1ub2RlanMtd29ya2VyL2Jsb2IvdjMuNS4yL3NyYy9zZXR1cENvcmVNb2R1bGUudHMjTDQ2LUw1NFxuICAgICAgICBkZWJ1ZygnTW9kdWxlLl9yZXNvbHZlRmlsZW5hbWUoXCIlc1wiKSB0aHJldyAlaiwgY2FsbGluZyBvcmlnaW5hbCBNb2R1bGUucmVxdWlyZScsIGlkLCByZXNvbHZlRXJyLm1lc3NhZ2UpXG4gICAgICAgIHJldHVybiBzZWxmLl9vcmlnUmVxdWlyZS5hcHBseSh0aGlzLCBhcmdzKVxuICAgICAgfVxuICAgIH1cblxuICAgIGxldCBtb2R1bGVOYW1lLCBiYXNlZGlyXG5cbiAgICBkZWJ1ZygncHJvY2Vzc2luZyAlcyBtb2R1bGUgcmVxdWlyZShcXCclc1xcJyk6ICVzJywgY29yZSA9PT0gdHJ1ZSA/ICdjb3JlJyA6ICdub24tY29yZScsIGlkLCBmaWxlbmFtZSlcblxuICAgIC8vIHJldHVybiBrbm93biBwYXRjaGVkIG1vZHVsZXMgaW1tZWRpYXRlbHlcbiAgICBpZiAoc2VsZi5fY2FjaGUuaGFzKGZpbGVuYW1lLCBjb3JlKSA9PT0gdHJ1ZSkge1xuICAgICAgZGVidWcoJ3JldHVybmluZyBhbHJlYWR5IHBhdGNoZWQgY2FjaGVkIG1vZHVsZTogJXMnLCBmaWxlbmFtZSlcbiAgICAgIHJldHVybiBzZWxmLl9jYWNoZS5nZXQoZmlsZW5hbWUsIGNvcmUpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdGhpcyBtb2R1bGUgaGFzIGEgcGF0Y2hlciBpbi1wcm9ncmVzcyBhbHJlYWR5LlxuICAgIC8vIE90aGVyd2lzZSwgbWFyayB0aGlzIG1vZHVsZSBhcyBwYXRjaGluZyBpbi1wcm9ncmVzcy5cbiAgICBjb25zdCBpc1BhdGNoaW5nID0gcGF0Y2hpbmcuaGFzKGZpbGVuYW1lKVxuICAgIGlmIChpc1BhdGNoaW5nID09PSBmYWxzZSkge1xuICAgICAgcGF0Y2hpbmcuYWRkKGZpbGVuYW1lKVxuICAgIH1cblxuICAgIGNvbnN0IGV4cG9ydHMgPSBjb3JlT25seVxuICAgICAgPyBzZWxmLl9vcmlnR2V0QnVpbHRpbk1vZHVsZS5hcHBseSh0aGlzLCBhcmdzKVxuICAgICAgOiBzZWxmLl9vcmlnUmVxdWlyZS5hcHBseSh0aGlzLCBhcmdzKVxuXG4gICAgLy8gSWYgaXQncyBhbHJlYWR5IHBhdGNoZWQsIGp1c3QgcmV0dXJuIGl0IGFzLWlzLlxuICAgIGlmIChpc1BhdGNoaW5nID09PSB0cnVlKSB7XG4gICAgICBkZWJ1ZygnbW9kdWxlIGlzIGluIHRoZSBwcm9jZXNzIG9mIGJlaW5nIHBhdGNoZWQgYWxyZWFkeSAtIGlnbm9yaW5nOiAlcycsIGZpbGVuYW1lKVxuICAgICAgcmV0dXJuIGV4cG9ydHNcbiAgICB9XG5cbiAgICAvLyBUaGUgbW9kdWxlIGhhcyBhbHJlYWR5IGJlZW4gbG9hZGVkLFxuICAgIC8vIHNvIHRoZSBwYXRjaGluZyBtYXJrIGNhbiBiZSBjbGVhbmVkIHVwLlxuICAgIHBhdGNoaW5nLmRlbGV0ZShmaWxlbmFtZSlcblxuICAgIGlmIChjb3JlID09PSB0cnVlKSB7XG4gICAgICBpZiAoaGFzV2hpdGVsaXN0ID09PSB0cnVlICYmIG1vZHVsZXMuaW5jbHVkZXMoZmlsZW5hbWUpID09PSBmYWxzZSkge1xuICAgICAgICBkZWJ1ZygnaWdub3JpbmcgY29yZSBtb2R1bGUgbm90IG9uIHdoaXRlbGlzdDogJXMnLCBmaWxlbmFtZSlcbiAgICAgICAgcmV0dXJuIGV4cG9ydHMgLy8gYWJvcnQgaWYgbW9kdWxlIG5hbWUgaXNuJ3Qgb24gd2hpdGVsaXN0XG4gICAgICB9XG4gICAgICBtb2R1bGVOYW1lID0gZmlsZW5hbWVcbiAgICB9IGVsc2UgaWYgKGhhc1doaXRlbGlzdCA9PT0gdHJ1ZSAmJiBtb2R1bGVzLmluY2x1ZGVzKGZpbGVuYW1lKSkge1xuICAgICAgLy8gd2hpdGVsaXN0IGluY2x1ZGVzIHRoZSBhYnNvbHV0ZSBwYXRoIHRvIHRoZSBmaWxlIGluY2x1ZGluZyBleHRlbnNpb25cbiAgICAgIGNvbnN0IHBhcnNlZFBhdGggPSBwYXRoLnBhcnNlKGZpbGVuYW1lKVxuICAgICAgbW9kdWxlTmFtZSA9IHBhcnNlZFBhdGgubmFtZVxuICAgICAgYmFzZWRpciA9IHBhcnNlZFBhdGguZGlyXG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHN0YXQgPSBtb2R1bGVEZXRhaWxzRnJvbVBhdGgoZmlsZW5hbWUpXG4gICAgICBpZiAoc3RhdCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGRlYnVnKCdjb3VsZCBub3QgcGFyc2UgZmlsZW5hbWU6ICVzJywgZmlsZW5hbWUpXG4gICAgICAgIHJldHVybiBleHBvcnRzIC8vIGFib3J0IGlmIGZpbGVuYW1lIGNvdWxkIG5vdCBiZSBwYXJzZWRcbiAgICAgIH1cbiAgICAgIG1vZHVsZU5hbWUgPSBzdGF0Lm5hbWVcbiAgICAgIGJhc2VkaXIgPSBzdGF0LmJhc2VkaXJcblxuICAgICAgLy8gRXg6IHJlcXVpcmUoJ2Zvby9saWIvLi4vYmFyLmpzJylcbiAgICAgIC8vIG1vZHVsZU5hbWUgPSAnZm9vJ1xuICAgICAgLy8gZnVsbE1vZHVsZU5hbWUgPSAnZm9vL2JhcidcbiAgICAgIGNvbnN0IGZ1bGxNb2R1bGVOYW1lID0gcmVzb2x2ZU1vZHVsZU5hbWUoc3RhdClcblxuICAgICAgZGVidWcoJ3Jlc29sdmVkIGZpbGVuYW1lIHRvIG1vZHVsZTogJXMgKGlkOiAlcywgcmVzb2x2ZWQ6ICVzLCBiYXNlZGlyOiAlcyknLCBtb2R1bGVOYW1lLCBpZCwgZnVsbE1vZHVsZU5hbWUsIGJhc2VkaXIpXG5cbiAgICAgIGxldCBtYXRjaEZvdW5kID0gZmFsc2VcbiAgICAgIGlmIChoYXNXaGl0ZWxpc3QpIHtcbiAgICAgICAgaWYgKCFpZC5zdGFydHNXaXRoKCcuJykgJiYgbW9kdWxlcy5pbmNsdWRlcyhpZCkpIHtcbiAgICAgICAgICAvLyBOb3Qgc3RhcnRpbmcgd2l0aCAnLicgbWVhbnMgYGlkYCBpcyBpZGVudGlmeWluZyBhIG1vZHVsZSBwYXRoLFxuICAgICAgICAgIC8vIGFzIG9wcG9zZWQgdG8gYSBsb2NhbCBmaWxlIHBhdGguIChOb3RlOiBJJ20gbm90IHN1cmUgYWJvdXRcbiAgICAgICAgICAvLyBhYnNvbHV0ZSBwYXRocywgYnV0IHRob3NlIGFyZSBoYW5kbGVkIGFib3ZlLilcbiAgICAgICAgICAvLyBJZiB0aGlzIGBpZGAgaXMgaW4gYG1vZHVsZXNgLCB0aGVuIHRoaXMgY291bGQgYmUgYSBtYXRjaCB0byBhblxuICAgICAgICAgIC8vIHBhY2thZ2UgXCJleHBvcnRzXCIgZW50cnkgcG9pbnQgdGhhdCB3b3VsZG4ndCBvdGhlcndpc2UgbWF0Y2ggYmVsb3cuXG4gICAgICAgICAgbW9kdWxlTmFtZSA9IGlkXG4gICAgICAgICAgbWF0Y2hGb3VuZCA9IHRydWVcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIGFib3J0IGlmIG1vZHVsZSBuYW1lIGlzbid0IG9uIHdoaXRlbGlzdFxuICAgICAgICBpZiAoIW1vZHVsZXMuaW5jbHVkZXMobW9kdWxlTmFtZSkgJiYgIW1vZHVsZXMuaW5jbHVkZXMoZnVsbE1vZHVsZU5hbWUpKSB7XG4gICAgICAgICAgcmV0dXJuIGV4cG9ydHNcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChtb2R1bGVzLmluY2x1ZGVzKGZ1bGxNb2R1bGVOYW1lKSAmJiBmdWxsTW9kdWxlTmFtZSAhPT0gbW9kdWxlTmFtZSkge1xuICAgICAgICAgIC8vIGlmIHdlIGdldCB0byB0aGlzIHBvaW50LCBpdCBtZWFucyB0aGF0IHdlJ3JlIHJlcXVpcmluZyBhIHdoaXRlbGlzdGVkIHN1Yi1tb2R1bGVcbiAgICAgICAgICBtb2R1bGVOYW1lID0gZnVsbE1vZHVsZU5hbWVcbiAgICAgICAgICBtYXRjaEZvdW5kID0gdHJ1ZVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmICghbWF0Y2hGb3VuZCkge1xuICAgICAgICAvLyBmaWd1cmUgb3V0IGlmIHRoaXMgaXMgdGhlIG1haW4gbW9kdWxlIGZpbGUsIG9yIGEgZmlsZSBpbnNpZGUgdGhlIG1vZHVsZVxuICAgICAgICBsZXQgcmVzXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgcmVzID0gcmVzb2x2ZShtb2R1bGVOYW1lLCBiYXNlZGlyKVxuICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgZGVidWcoJ2NvdWxkIG5vdCByZXNvbHZlIG1vZHVsZTogJXMnLCBtb2R1bGVOYW1lKVxuICAgICAgICAgIHNlbGYuX2NhY2hlLnNldChmaWxlbmFtZSwgZXhwb3J0cywgY29yZSlcbiAgICAgICAgICByZXR1cm4gZXhwb3J0cyAvLyBhYm9ydCBpZiBtb2R1bGUgY291bGQgbm90IGJlIHJlc29sdmVkIChlLmcuIG5vIG1haW4gaW4gcGFja2FnZS5qc29uIGFuZCBubyBpbmRleC5qcyBmaWxlKVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKHJlcyAhPT0gZmlsZW5hbWUpIHtcbiAgICAgICAgICAvLyB0aGlzIGlzIGEgbW9kdWxlLWludGVybmFsIGZpbGVcbiAgICAgICAgICBpZiAoaW50ZXJuYWxzID09PSB0cnVlKSB7XG4gICAgICAgICAgICAvLyB1c2UgdGhlIG1vZHVsZS1yZWxhdGl2ZSBwYXRoIHRvIHRoZSBmaWxlLCBwcmVmaXhlZCBieSBvcmlnaW5hbCBtb2R1bGUgbmFtZVxuICAgICAgICAgICAgbW9kdWxlTmFtZSA9IG1vZHVsZU5hbWUgKyBwYXRoLnNlcCArIHBhdGgucmVsYXRpdmUoYmFzZWRpciwgZmlsZW5hbWUpXG4gICAgICAgICAgICBkZWJ1ZygncHJlcGFyaW5nIHRvIHByb2Nlc3MgcmVxdWlyZSBvZiBpbnRlcm5hbCBmaWxlOiAlcycsIG1vZHVsZU5hbWUpXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGRlYnVnKCdpZ25vcmluZyByZXF1aXJlIG9mIG5vbi1tYWluIG1vZHVsZSBmaWxlOiAlcycsIHJlcylcbiAgICAgICAgICAgIHNlbGYuX2NhY2hlLnNldChmaWxlbmFtZSwgZXhwb3J0cywgY29yZSlcbiAgICAgICAgICAgIHJldHVybiBleHBvcnRzIC8vIGFib3J0IGlmIG5vdCBtYWluIG1vZHVsZSBmaWxlXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gZW5zdXJlIHRoYXQgdGhlIGNhY2hlIGVudHJ5IGlzIGFzc2lnbmVkIGEgdmFsdWUgYmVmb3JlIGNhbGxpbmdcbiAgICAvLyBvbnJlcXVpcmUsIGluIGNhc2UgY2FsbGluZyBvbnJlcXVpcmUgcmVxdWlyZXMgdGhlIHNhbWUgbW9kdWxlLlxuICAgIHNlbGYuX2NhY2hlLnNldChmaWxlbmFtZSwgZXhwb3J0cywgY29yZSlcbiAgICBkZWJ1ZygnY2FsbGluZyByZXF1aXJlIGhvb2s6ICVzJywgbW9kdWxlTmFtZSlcbiAgICBjb25zdCBwYXRjaGVkRXhwb3J0cyA9IG9ucmVxdWlyZShleHBvcnRzLCBtb2R1bGVOYW1lLCBiYXNlZGlyKVxuICAgIHNlbGYuX2NhY2hlLnNldChmaWxlbmFtZSwgcGF0Y2hlZEV4cG9ydHMsIGNvcmUpXG5cbiAgICBkZWJ1ZygncmV0dXJuaW5nIG1vZHVsZTogJXMnLCBtb2R1bGVOYW1lKVxuICAgIHJldHVybiBwYXRjaGVkRXhwb3J0c1xuICB9XG59XG5cbkhvb2sucHJvdG90eXBlLnVuaG9vayA9IGZ1bmN0aW9uICgpIHtcbiAgdGhpcy5fdW5ob29rZWQgPSB0cnVlXG5cbiAgaWYgKHRoaXMuX3JlcXVpcmUgPT09IE1vZHVsZS5wcm90b3R5cGUucmVxdWlyZSkge1xuICAgIE1vZHVsZS5wcm90b3R5cGUucmVxdWlyZSA9IHRoaXMuX29yaWdSZXF1aXJlXG4gICAgZGVidWcoJ3JlcXVpcmUgdW5ob29rIHN1Y2Nlc3NmdWwnKVxuICB9IGVsc2Uge1xuICAgIGRlYnVnKCdyZXF1aXJlIHVuaG9vayB1bnN1Y2Nlc3NmdWwnKVxuICB9XG5cbiAgaWYgKHByb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgaWYgKHRoaXMuX2dldEJ1aWx0aW5Nb2R1bGUgPT09IHByb2Nlc3MuZ2V0QnVpbHRpbk1vZHVsZSkge1xuICAgICAgcHJvY2Vzcy5nZXRCdWlsdGluTW9kdWxlID0gdGhpcy5fb3JpZ0dldEJ1aWx0aW5Nb2R1bGVcbiAgICAgIGRlYnVnKCdwcm9jZXNzLmdldEJ1aWx0aW5Nb2R1bGUgdW5ob29rIHN1Y2Nlc3NmdWwnKVxuICAgIH0gZWxzZSB7XG4gICAgICBkZWJ1ZygncHJvY2Vzcy5nZXRCdWlsdGluTW9kdWxlIHVuaG9vayB1bnN1Y2Nlc3NmdWwnKVxuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiByZXNvbHZlTW9kdWxlTmFtZSAoc3RhdCkge1xuICBjb25zdCBub3JtYWxpemVkUGF0aCA9IHBhdGguc2VwICE9PSAnLycgPyBzdGF0LnBhdGguc3BsaXQocGF0aC5zZXApLmpvaW4oJy8nKSA6IHN0YXQucGF0aFxuICByZXR1cm4gcGF0aC5wb3NpeC5qb2luKHN0YXQubmFtZSwgbm9ybWFsaXplZFBhdGgpLnJlcGxhY2Uobm9ybWFsaXplLCAnJylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/require-in-the-middle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/require-in-the-middle/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(rsc)/./node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\nlet builtinModules // Set<string>\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else if (Module.builtinModules) { // Added in node v9.3.0, v8.10.0, v6.13.0\n  isCore = moduleName => {\n    if (moduleName.startsWith('node:')) {\n      return true\n    }\n\n    if (builtinModules === undefined) {\n      builtinModules = new Set(Module.builtinModules)\n    }\n\n    return builtinModules.has(moduleName)\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(rsc)/./node_modules/resolve/index.js\")\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!_resolve.core[moduleName]\n    }\n  }\n}\n\n// Feature detection: This property was added in Node.js 8.9.0, the same time\n// as the `paths` options argument was added to the `require.resolve` function,\n// which is the one we want\nlet resolve\nif (__webpack_require__(\"(rsc)/./node_modules/require-in-the-middle sync recursive\").resolve.paths) {\n  resolve = function (moduleName, basedir) {\n    return __webpack_require__(\"(rsc)/./node_modules/require-in-the-middle sync recursive\").resolve(moduleName, { paths: [basedir] })\n  }\n} else {\n  const _resolve = __webpack_require__(/*! resolve */ \"(rsc)/./node_modules/resolve/index.js\")\n  resolve = function (moduleName, basedir) {\n    return _resolve.sync(moduleName, { basedir })\n  }\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    return patchedRequire.call(this, arguments, false)\n  }\n\n  if (typeof process.getBuiltinModule === 'function') {\n    this._origGetBuiltinModule = process.getBuiltinModule\n    this._getBuiltinModule = process.getBuiltinModule = function (id) {\n      if (self._unhooked === true) {\n        // if the patched process.getBuiltinModule function could not be removed because\n        // someone else patched it after it was patched here, we just abort and pass the\n        // request onwards to the original process.getBuiltinModule\n        debug('ignoring process.getBuiltinModule call - module is soft-unhooked')\n        return self._origGetBuiltinModule.apply(this, arguments)\n      }\n\n      return patchedRequire.call(this, arguments, true)\n    }\n  }\n\n  // Preserve the original require/process.getBuiltinModule arguments in `args`\n  function patchedRequire (args, coreOnly) {\n    const id = args[0]\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else if (coreOnly) {\n      // `coreOnly` is `true` if this was a call to `process.getBuiltinModule`, in which case\n      // we don't want to return anything if the requested `id` isn't a core module. Falling\n      // back to default behaviour, which at the time of this wrting is simply returning `undefined`\n      debug('call to process.getBuiltinModule with unknown built-in id')\n      return self._origGetBuiltinModule.apply(this, args)\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, args)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = coreOnly\n      ? self._origGetBuiltinModule.apply(this, args)\n      : self._origRequire.apply(this, args)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      let matchFound = false\n      if (hasWhitelist) {\n        if (!id.startsWith('.') && modules.includes(id)) {\n          // Not starting with '.' means `id` is identifying a module path,\n          // as opposed to a local file path. (Note: I'm not sure about\n          // absolute paths, but those are handled above.)\n          // If this `id` is in `modules`, then this could be a match to an\n          // package \"exports\" entry point that wouldn't otherwise match below.\n          moduleName = id\n          matchFound = true\n        }\n\n        // abort if module name isn't on whitelist\n        if (!modules.includes(moduleName) && !modules.includes(fullModuleName)) {\n          return exports\n        }\n\n        if (modules.includes(fullModuleName) && fullModuleName !== moduleName) {\n          // if we get to this point, it means that we're requiring a whitelisted sub-module\n          moduleName = fullModuleName\n          matchFound = true\n        }\n      }\n\n      if (!matchFound) {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve(moduleName, basedir)\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('require unhook successful')\n  } else {\n    debug('require unhook unsuccessful')\n  }\n\n  if (process.getBuiltinModule !== undefined) {\n    if (this._getBuiltinModule === process.getBuiltinModule) {\n      process.getBuiltinModule = this._origGetBuiltinModule\n      debug('process.getBuiltinModule unhook successful')\n    } else {\n      debug('process.getBuiltinModule unhook unsuccessful')\n    }\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/require-in-the-middle/package.json":
/*!*********************************************************!*\
  !*** ./node_modules/require-in-the-middle/package.json ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.5.0","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.3.5","module-details-from-path":"^1.0.3","resolve":"^1.22.8"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/elastic/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/elastic/require-in-the-middle/issues"},"homepage":"https://github.com/elastic/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ }),

/***/ "(instrument)/./node_modules/require-in-the-middle/package.json":
/*!*********************************************************!*\
  !*** ./node_modules/require-in-the-middle/package.json ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.5.0","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.3.5","module-details-from-path":"^1.0.3","resolve":"^1.22.8"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/elastic/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/elastic/require-in-the-middle/issues"},"homepage":"https://github.com/elastic/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ }),

/***/ "(rsc)/./node_modules/require-in-the-middle/package.json":
/*!*********************************************************!*\
  !*** ./node_modules/require-in-the-middle/package.json ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.5.0","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.3.5","module-details-from-path":"^1.0.3","resolve":"^1.22.8"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/elastic/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/elastic/require-in-the-middle/issues"},"homepage":"https://github.com/elastic/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ }),

/***/ "(ssr)/./node_modules/require-in-the-middle/package.json":
/*!*********************************************************!*\
  !*** ./node_modules/require-in-the-middle/package.json ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.5.0","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.3.5","module-details-from-path":"^1.0.3","resolve":"^1.22.8"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/elastic/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/elastic/require-in-the-middle/issues"},"homepage":"https://github.com/elastic/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ })

};
;