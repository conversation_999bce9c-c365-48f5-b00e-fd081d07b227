"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@xyflow";
exports.ids = ["vendor-chunks/@xyflow"];
exports.modules = {

/***/ "(ssr)/./node_modules/@xyflow/system/dist/esm/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/@xyflow/system/dist/esm/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionLineType: () => (/* binding */ ConnectionLineType),\n/* harmony export */   ConnectionMode: () => (/* binding */ ConnectionMode),\n/* harmony export */   MarkerType: () => (/* binding */ MarkerType),\n/* harmony export */   PanOnScrollMode: () => (/* binding */ PanOnScrollMode),\n/* harmony export */   Position: () => (/* binding */ Position),\n/* harmony export */   ResizeControlVariant: () => (/* binding */ ResizeControlVariant),\n/* harmony export */   SelectionMode: () => (/* binding */ SelectionMode),\n/* harmony export */   XYDrag: () => (/* binding */ XYDrag),\n/* harmony export */   XYHandle: () => (/* binding */ XYHandle),\n/* harmony export */   XYMinimap: () => (/* binding */ XYMinimap),\n/* harmony export */   XYPanZoom: () => (/* binding */ XYPanZoom),\n/* harmony export */   XYResizer: () => (/* binding */ XYResizer),\n/* harmony export */   XY_RESIZER_HANDLE_POSITIONS: () => (/* binding */ XY_RESIZER_HANDLE_POSITIONS),\n/* harmony export */   XY_RESIZER_LINE_POSITIONS: () => (/* binding */ XY_RESIZER_LINE_POSITIONS),\n/* harmony export */   addEdge: () => (/* binding */ addEdge),\n/* harmony export */   adoptUserNodes: () => (/* binding */ adoptUserNodes),\n/* harmony export */   areConnectionMapsEqual: () => (/* binding */ areConnectionMapsEqual),\n/* harmony export */   areSetsEqual: () => (/* binding */ areSetsEqual),\n/* harmony export */   boxToRect: () => (/* binding */ boxToRect),\n/* harmony export */   calcAutoPan: () => (/* binding */ calcAutoPan),\n/* harmony export */   calculateNodePosition: () => (/* binding */ calculateNodePosition),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   clampPosition: () => (/* binding */ clampPosition),\n/* harmony export */   clampPositionToParent: () => (/* binding */ clampPositionToParent),\n/* harmony export */   createMarkerIds: () => (/* binding */ createMarkerIds),\n/* harmony export */   devWarn: () => (/* binding */ devWarn),\n/* harmony export */   elementSelectionKeys: () => (/* binding */ elementSelectionKeys),\n/* harmony export */   errorMessages: () => (/* binding */ errorMessages),\n/* harmony export */   evaluateAbsolutePosition: () => (/* binding */ evaluateAbsolutePosition),\n/* harmony export */   fitView: () => (/* binding */ fitView),\n/* harmony export */   getBezierEdgeCenter: () => (/* binding */ getBezierEdgeCenter),\n/* harmony export */   getBezierPath: () => (/* binding */ getBezierPath),\n/* harmony export */   getBoundsOfBoxes: () => (/* binding */ getBoundsOfBoxes),\n/* harmony export */   getBoundsOfRects: () => (/* binding */ getBoundsOfRects),\n/* harmony export */   getConnectedEdges: () => (/* binding */ getConnectedEdges),\n/* harmony export */   getConnectionStatus: () => (/* binding */ getConnectionStatus),\n/* harmony export */   getDimensions: () => (/* binding */ getDimensions),\n/* harmony export */   getEdgeCenter: () => (/* binding */ getEdgeCenter),\n/* harmony export */   getEdgePosition: () => (/* binding */ getEdgePosition),\n/* harmony export */   getElementsToRemove: () => (/* binding */ getElementsToRemove),\n/* harmony export */   getElevatedEdgeZIndex: () => (/* binding */ getElevatedEdgeZIndex),\n/* harmony export */   getEventPosition: () => (/* binding */ getEventPosition),\n/* harmony export */   getFitViewNodes: () => (/* binding */ getFitViewNodes),\n/* harmony export */   getHandleBounds: () => (/* binding */ getHandleBounds),\n/* harmony export */   getHandlePosition: () => (/* binding */ getHandlePosition),\n/* harmony export */   getHostForElement: () => (/* binding */ getHostForElement),\n/* harmony export */   getIncomers: () => (/* binding */ getIncomers),\n/* harmony export */   getInternalNodesBounds: () => (/* binding */ getInternalNodesBounds),\n/* harmony export */   getMarkerId: () => (/* binding */ getMarkerId),\n/* harmony export */   getNodeDimensions: () => (/* binding */ getNodeDimensions),\n/* harmony export */   getNodePositionWithOrigin: () => (/* binding */ getNodePositionWithOrigin),\n/* harmony export */   getNodeToolbarTransform: () => (/* binding */ getNodeToolbarTransform),\n/* harmony export */   getNodesBounds: () => (/* binding */ getNodesBounds),\n/* harmony export */   getNodesInside: () => (/* binding */ getNodesInside),\n/* harmony export */   getOutgoers: () => (/* binding */ getOutgoers),\n/* harmony export */   getOverlappingArea: () => (/* binding */ getOverlappingArea),\n/* harmony export */   getPointerPosition: () => (/* binding */ getPointerPosition),\n/* harmony export */   getSmoothStepPath: () => (/* binding */ getSmoothStepPath),\n/* harmony export */   getStraightPath: () => (/* binding */ getStraightPath),\n/* harmony export */   getViewportForBounds: () => (/* binding */ getViewportForBounds),\n/* harmony export */   handleConnectionChange: () => (/* binding */ handleConnectionChange),\n/* harmony export */   handleExpandParent: () => (/* binding */ handleExpandParent),\n/* harmony export */   infiniteExtent: () => (/* binding */ infiniteExtent),\n/* harmony export */   initialConnection: () => (/* binding */ initialConnection),\n/* harmony export */   isCoordinateExtent: () => (/* binding */ isCoordinateExtent),\n/* harmony export */   isEdgeBase: () => (/* binding */ isEdgeBase),\n/* harmony export */   isEdgeVisible: () => (/* binding */ isEdgeVisible),\n/* harmony export */   isInputDOMNode: () => (/* binding */ isInputDOMNode),\n/* harmony export */   isInternalNodeBase: () => (/* binding */ isInternalNodeBase),\n/* harmony export */   isMacOs: () => (/* binding */ isMacOs),\n/* harmony export */   isMouseEvent: () => (/* binding */ isMouseEvent),\n/* harmony export */   isNodeBase: () => (/* binding */ isNodeBase),\n/* harmony export */   isNumeric: () => (/* binding */ isNumeric),\n/* harmony export */   isRectObject: () => (/* binding */ isRectObject),\n/* harmony export */   nodeHasDimensions: () => (/* binding */ nodeHasDimensions),\n/* harmony export */   nodeToBox: () => (/* binding */ nodeToBox),\n/* harmony export */   nodeToRect: () => (/* binding */ nodeToRect),\n/* harmony export */   oppositePosition: () => (/* binding */ oppositePosition),\n/* harmony export */   panBy: () => (/* binding */ panBy),\n/* harmony export */   pointToRendererPoint: () => (/* binding */ pointToRendererPoint),\n/* harmony export */   reconnectEdge: () => (/* binding */ reconnectEdge),\n/* harmony export */   rectToBox: () => (/* binding */ rectToBox),\n/* harmony export */   rendererPointToPoint: () => (/* binding */ rendererPointToPoint),\n/* harmony export */   shallowNodeData: () => (/* binding */ shallowNodeData),\n/* harmony export */   snapPosition: () => (/* binding */ snapPosition),\n/* harmony export */   updateAbsolutePositions: () => (/* binding */ updateAbsolutePositions),\n/* harmony export */   updateConnectionLookup: () => (/* binding */ updateConnectionLookup),\n/* harmony export */   updateNodeInternals: () => (/* binding */ updateNodeInternals)\n/* harmony export */ });\n/* harmony import */ var d3_drag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-drag */ \"(ssr)/./node_modules/d3-drag/src/drag.js\");\n/* harmony import */ var d3_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-selection */ \"(ssr)/./node_modules/d3-selection/src/select.js\");\n/* harmony import */ var d3_selection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-selection */ \"(ssr)/./node_modules/d3-selection/src/pointer.js\");\n/* harmony import */ var d3_zoom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-zoom */ \"(ssr)/./node_modules/d3-zoom/src/index.js\");\n\n\n\n\nconst errorMessages = {\n    error001: () => '[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001',\n    error002: () => \"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.\",\n    error003: (nodeType) => `Node type \"${nodeType}\" not found. Using fallback type \"default\".`,\n    error004: () => 'The React Flow parent container needs a width and a height to render the graph.',\n    error005: () => 'Only child nodes can use a parent extent.',\n    error006: () => \"Can't create edge. An edge needs a source and a target.\",\n    error007: (id) => `The old edge with id=${id} does not exist.`,\n    error009: (type) => `Marker type \"${type}\" doesn't exist.`,\n    error008: (handleType, { id, sourceHandle, targetHandle }) => `Couldn't create edge for ${handleType} handle id: \"${handleType === 'source' ? sourceHandle : targetHandle}\", edge id: ${id}.`,\n    error010: () => 'Handle: No node id found. Make sure to only use a Handle inside a custom Node.',\n    error011: (edgeType) => `Edge type \"${edgeType}\" not found. Using fallback type \"default\".`,\n    error012: (id) => `Node with id \"${id}\" does not exist, it may have been removed. This can happen when a node is deleted before the \"onNodeClick\" handler is called.`,\n    error013: (lib = 'react') => `It seems that you haven't loaded the styles. Please import '@xyflow/${lib}/dist/style.css' or base.css to make sure everything is working properly.`,\n    error014: () => 'useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.',\n};\nconst infiniteExtent = [\n    [Number.NEGATIVE_INFINITY, Number.NEGATIVE_INFINITY],\n    [Number.POSITIVE_INFINITY, Number.POSITIVE_INFINITY],\n];\nconst elementSelectionKeys = ['Enter', ' ', 'Escape'];\n\nvar ConnectionMode;\n(function (ConnectionMode) {\n    ConnectionMode[\"Strict\"] = \"strict\";\n    ConnectionMode[\"Loose\"] = \"loose\";\n})(ConnectionMode || (ConnectionMode = {}));\nvar PanOnScrollMode;\n(function (PanOnScrollMode) {\n    PanOnScrollMode[\"Free\"] = \"free\";\n    PanOnScrollMode[\"Vertical\"] = \"vertical\";\n    PanOnScrollMode[\"Horizontal\"] = \"horizontal\";\n})(PanOnScrollMode || (PanOnScrollMode = {}));\nvar SelectionMode;\n(function (SelectionMode) {\n    SelectionMode[\"Partial\"] = \"partial\";\n    SelectionMode[\"Full\"] = \"full\";\n})(SelectionMode || (SelectionMode = {}));\nconst initialConnection = {\n    inProgress: false,\n    isValid: null,\n    from: null,\n    fromHandle: null,\n    fromPosition: null,\n    fromNode: null,\n    to: null,\n    toHandle: null,\n    toPosition: null,\n    toNode: null,\n};\n\nvar ConnectionLineType;\n(function (ConnectionLineType) {\n    ConnectionLineType[\"Bezier\"] = \"default\";\n    ConnectionLineType[\"Straight\"] = \"straight\";\n    ConnectionLineType[\"Step\"] = \"step\";\n    ConnectionLineType[\"SmoothStep\"] = \"smoothstep\";\n    ConnectionLineType[\"SimpleBezier\"] = \"simplebezier\";\n})(ConnectionLineType || (ConnectionLineType = {}));\nvar MarkerType;\n(function (MarkerType) {\n    MarkerType[\"Arrow\"] = \"arrow\";\n    MarkerType[\"ArrowClosed\"] = \"arrowclosed\";\n})(MarkerType || (MarkerType = {}));\n\nvar Position;\n(function (Position) {\n    Position[\"Left\"] = \"left\";\n    Position[\"Top\"] = \"top\";\n    Position[\"Right\"] = \"right\";\n    Position[\"Bottom\"] = \"bottom\";\n})(Position || (Position = {}));\nconst oppositePosition = {\n    [Position.Left]: Position.Right,\n    [Position.Right]: Position.Left,\n    [Position.Top]: Position.Bottom,\n    [Position.Bottom]: Position.Top,\n};\n\n/**\n * @internal\n */\nfunction areConnectionMapsEqual(a, b) {\n    if (!a && !b) {\n        return true;\n    }\n    if (!a || !b || a.size !== b.size) {\n        return false;\n    }\n    if (!a.size && !b.size) {\n        return true;\n    }\n    for (const key of a.keys()) {\n        if (!b.has(key)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * We call the callback for all connections in a that are not in b\n *\n * @internal\n */\nfunction handleConnectionChange(a, b, cb) {\n    if (!cb) {\n        return;\n    }\n    const diff = [];\n    a.forEach((connection, key) => {\n        if (!b?.has(key)) {\n            diff.push(connection);\n        }\n    });\n    if (diff.length) {\n        cb(diff);\n    }\n}\nfunction getConnectionStatus(isValid) {\n    return isValid === null ? null : isValid ? 'valid' : 'invalid';\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Test whether an object is useable as an Edge\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Edge if it returns true\n * @param element - The element to test\n * @returns A boolean indicating whether the element is an Edge\n */\nconst isEdgeBase = (element) => 'id' in element && 'source' in element && 'target' in element;\n/**\n * Test whether an object is useable as a Node\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Node if it returns true\n * @param element - The element to test\n * @returns A boolean indicating whether the element is an Node\n */\nconst isNodeBase = (element) => 'id' in element && 'position' in element && !('source' in element) && !('target' in element);\nconst isInternalNodeBase = (element) => 'id' in element && 'internals' in element && !('source' in element) && !('target' in element);\n/**\n * Pass in a node, and get connected nodes where edge.source === node.id\n * @public\n * @param node - The node to get the connected nodes from\n * @param nodes - The array of all nodes\n * @param edges - The array of all edges\n * @returns An array of nodes that are connected over eges where the source is the given node\n */\nconst getOutgoers = (node, nodes, edges) => {\n    if (!node.id) {\n        return [];\n    }\n    const outgoerIds = new Set();\n    edges.forEach((edge) => {\n        if (edge.source === node.id) {\n            outgoerIds.add(edge.target);\n        }\n    });\n    return nodes.filter((n) => outgoerIds.has(n.id));\n};\n/**\n * Pass in a node, and get connected nodes where edge.target === node.id\n * @public\n * @param node - The node to get the connected nodes from\n * @param nodes - The array of all nodes\n * @param edges - The array of all edges\n * @returns An array of nodes that are connected over eges where the target is the given node\n */\nconst getIncomers = (node, nodes, edges) => {\n    if (!node.id) {\n        return [];\n    }\n    const incomersIds = new Set();\n    edges.forEach((edge) => {\n        if (edge.target === node.id) {\n            incomersIds.add(edge.source);\n        }\n    });\n    return nodes.filter((n) => incomersIds.has(n.id));\n};\nconst getNodePositionWithOrigin = (node, nodeOrigin = [0, 0]) => {\n    const { width, height } = getNodeDimensions(node);\n    const origin = node.origin ?? nodeOrigin;\n    const offsetX = width * origin[0];\n    const offsetY = height * origin[1];\n    return {\n        x: node.position.x - offsetX,\n        y: node.position.y - offsetY,\n    };\n};\n/**\n * Internal function for determining a bounding box that contains all given nodes in an array.\n * @public\n * @remarks Useful when combined with {@link getViewportForBounds} to calculate the correct transform to fit the given nodes in a viewport.\n * @param nodes - Nodes to calculate the bounds for\n * @param params.nodeOrigin - Origin of the nodes: [0, 0] - top left, [0.5, 0.5] - center\n * @returns Bounding box enclosing all nodes\n */\nconst getNodesBounds = (nodes, params = { nodeOrigin: [0, 0], nodeLookup: undefined }) => {\n    if ( true && !params.nodeLookup) {\n        console.warn('Please use `getNodesBounds` from `useReactFlow`/`useSvelteFlow` hook to ensure correct values for sub flows. If not possible, you have to provide a nodeLookup to support sub flows.');\n    }\n    if (nodes.length === 0) {\n        return { x: 0, y: 0, width: 0, height: 0 };\n    }\n    const box = nodes.reduce((currBox, nodeOrId) => {\n        const isId = typeof nodeOrId === 'string';\n        let currentNode = !params.nodeLookup && !isId ? nodeOrId : undefined;\n        if (params.nodeLookup) {\n            currentNode = isId\n                ? params.nodeLookup.get(nodeOrId)\n                : !isInternalNodeBase(nodeOrId)\n                    ? params.nodeLookup.get(nodeOrId.id)\n                    : nodeOrId;\n        }\n        const nodeBox = currentNode ? nodeToBox(currentNode, params.nodeOrigin) : { x: 0, y: 0, x2: 0, y2: 0 };\n        return getBoundsOfBoxes(currBox, nodeBox);\n    }, { x: Infinity, y: Infinity, x2: -Infinity, y2: -Infinity });\n    return boxToRect(box);\n};\n/**\n * Determines a bounding box that contains all given nodes in an array\n * @internal\n */\nconst getInternalNodesBounds = (nodeLookup, params = {}) => {\n    if (nodeLookup.size === 0) {\n        return { x: 0, y: 0, width: 0, height: 0 };\n    }\n    let box = { x: Infinity, y: Infinity, x2: -Infinity, y2: -Infinity };\n    nodeLookup.forEach((node) => {\n        if (params.filter === undefined || params.filter(node)) {\n            const nodeBox = nodeToBox(node);\n            box = getBoundsOfBoxes(box, nodeBox);\n        }\n    });\n    return boxToRect(box);\n};\nconst getNodesInside = (nodes, rect, [tx, ty, tScale] = [0, 0, 1], partially = false, \n// set excludeNonSelectableNodes if you want to pay attention to the nodes \"selectable\" attribute\nexcludeNonSelectableNodes = false) => {\n    const paneRect = {\n        ...pointToRendererPoint(rect, [tx, ty, tScale]),\n        width: rect.width / tScale,\n        height: rect.height / tScale,\n    };\n    const visibleNodes = [];\n    for (const node of nodes.values()) {\n        const { measured, selectable = true, hidden = false } = node;\n        if ((excludeNonSelectableNodes && !selectable) || hidden) {\n            continue;\n        }\n        const width = measured.width ?? node.width ?? node.initialWidth ?? null;\n        const height = measured.height ?? node.height ?? node.initialHeight ?? null;\n        const overlappingArea = getOverlappingArea(paneRect, nodeToRect(node));\n        const area = (width ?? 0) * (height ?? 0);\n        const partiallyVisible = partially && overlappingArea > 0;\n        const forceInitialRender = !node.internals.handleBounds;\n        const isVisible = forceInitialRender || partiallyVisible || overlappingArea >= area;\n        if (isVisible || node.dragging) {\n            visibleNodes.push(node);\n        }\n    }\n    return visibleNodes;\n};\n/**\n * Get all connecting edges for a given set of nodes\n * @param nodes - Nodes you want to get the connected edges for\n * @param edges - All edges\n * @returns Array of edges that connect any of the given nodes with each other\n */\nconst getConnectedEdges = (nodes, edges) => {\n    const nodeIds = new Set();\n    nodes.forEach((node) => {\n        nodeIds.add(node.id);\n    });\n    return edges.filter((edge) => nodeIds.has(edge.source) || nodeIds.has(edge.target));\n};\nfunction getFitViewNodes(nodeLookup, options) {\n    const fitViewNodes = new Map();\n    const optionNodeIds = options?.nodes ? new Set(options.nodes.map((node) => node.id)) : null;\n    nodeLookup.forEach((n) => {\n        const isVisible = n.measured.width && n.measured.height && (options?.includeHiddenNodes || !n.hidden);\n        if (isVisible && (!optionNodeIds || optionNodeIds.has(n.id))) {\n            fitViewNodes.set(n.id, n);\n        }\n    });\n    return fitViewNodes;\n}\nasync function fitView({ nodes, width, height, panZoom, minZoom, maxZoom }, options) {\n    if (nodes.size === 0) {\n        return Promise.resolve(false);\n    }\n    const bounds = getInternalNodesBounds(nodes);\n    const viewport = getViewportForBounds(bounds, width, height, options?.minZoom ?? minZoom, options?.maxZoom ?? maxZoom, options?.padding ?? 0.1);\n    await panZoom.setViewport(viewport, { duration: options?.duration });\n    return Promise.resolve(true);\n}\n/**\n * This function calculates the next position of a node, taking into account the node's extent, parent node, and origin.\n *\n * @internal\n * @returns position, positionAbsolute\n */\nfunction calculateNodePosition({ nodeId, nextPosition, nodeLookup, nodeOrigin = [0, 0], nodeExtent, onError, }) {\n    const node = nodeLookup.get(nodeId);\n    const parentNode = node.parentId ? nodeLookup.get(node.parentId) : undefined;\n    const { x: parentX, y: parentY } = parentNode ? parentNode.internals.positionAbsolute : { x: 0, y: 0 };\n    const origin = node.origin ?? nodeOrigin;\n    let extent = nodeExtent;\n    if (node.extent === 'parent' && !node.expandParent) {\n        if (!parentNode) {\n            onError?.('005', errorMessages['error005']());\n        }\n        else {\n            const parentWidth = parentNode.measured.width;\n            const parentHeight = parentNode.measured.height;\n            if (parentWidth && parentHeight) {\n                extent = [\n                    [parentX, parentY],\n                    [parentX + parentWidth, parentY + parentHeight],\n                ];\n            }\n        }\n    }\n    else if (parentNode && isCoordinateExtent(node.extent)) {\n        extent = [\n            [node.extent[0][0] + parentX, node.extent[0][1] + parentY],\n            [node.extent[1][0] + parentX, node.extent[1][1] + parentY],\n        ];\n    }\n    const positionAbsolute = isCoordinateExtent(extent)\n        ? clampPosition(nextPosition, extent, node.measured)\n        : nextPosition;\n    return {\n        position: {\n            x: positionAbsolute.x - parentX + node.measured.width * origin[0],\n            y: positionAbsolute.y - parentY + node.measured.height * origin[1],\n        },\n        positionAbsolute,\n    };\n}\n/**\n * Pass in nodes & edges to delete, get arrays of nodes and edges that actually can be deleted\n * @internal\n * @param param.nodesToRemove - The nodes to remove\n * @param param.edgesToRemove - The edges to remove\n * @param param.nodes - All nodes\n * @param param.edges - All edges\n * @param param.onBeforeDelete - Callback to check which nodes and edges can be deleted\n * @returns nodes: nodes that can be deleted, edges: edges that can be deleted\n */\nasync function getElementsToRemove({ nodesToRemove = [], edgesToRemove = [], nodes, edges, onBeforeDelete, }) {\n    const nodeIds = new Set(nodesToRemove.map((node) => node.id));\n    const matchingNodes = [];\n    for (const node of nodes) {\n        if (node.deletable === false) {\n            continue;\n        }\n        const isIncluded = nodeIds.has(node.id);\n        const parentHit = !isIncluded && node.parentId && matchingNodes.find((n) => n.id === node.parentId);\n        if (isIncluded || parentHit) {\n            matchingNodes.push(node);\n        }\n    }\n    const edgeIds = new Set(edgesToRemove.map((edge) => edge.id));\n    const deletableEdges = edges.filter((edge) => edge.deletable !== false);\n    const connectedEdges = getConnectedEdges(matchingNodes, deletableEdges);\n    const matchingEdges = connectedEdges;\n    for (const edge of deletableEdges) {\n        const isIncluded = edgeIds.has(edge.id);\n        if (isIncluded && !matchingEdges.find((e) => e.id === edge.id)) {\n            matchingEdges.push(edge);\n        }\n    }\n    if (!onBeforeDelete) {\n        return {\n            edges: matchingEdges,\n            nodes: matchingNodes,\n        };\n    }\n    const onBeforeDeleteResult = await onBeforeDelete({\n        nodes: matchingNodes,\n        edges: matchingEdges,\n    });\n    if (typeof onBeforeDeleteResult === 'boolean') {\n        return onBeforeDeleteResult ? { edges: matchingEdges, nodes: matchingNodes } : { edges: [], nodes: [] };\n    }\n    return onBeforeDeleteResult;\n}\n\nconst clamp = (val, min = 0, max = 1) => Math.min(Math.max(val, min), max);\nconst clampPosition = (position = { x: 0, y: 0 }, extent, dimensions) => ({\n    x: clamp(position.x, extent[0][0], extent[1][0] - (dimensions?.width ?? 0)),\n    y: clamp(position.y, extent[0][1], extent[1][1] - (dimensions?.height ?? 0)),\n});\nfunction clampPositionToParent(childPosition, childDimensions, parent) {\n    const { width: parentWidth, height: parentHeight } = getNodeDimensions(parent);\n    const { x: parentX, y: parentY } = parent.internals.positionAbsolute;\n    return clampPosition(childPosition, [\n        [parentX, parentY],\n        [parentX + parentWidth, parentY + parentHeight],\n    ], childDimensions);\n}\n/**\n * Calculates the velocity of panning when the mouse is close to the edge of the canvas\n * @internal\n * @param value - One dimensional poition of the mouse (x or y)\n * @param min - Minimal position on canvas before panning starts\n * @param max - Maximal position on canvas before panning starts\n * @returns - A number between 0 and 1 that represents the velocity of panning\n */\nconst calcAutoPanVelocity = (value, min, max) => {\n    if (value < min) {\n        return clamp(Math.abs(value - min), 1, min) / min;\n    }\n    else if (value > max) {\n        return -clamp(Math.abs(value - max), 1, min) / min;\n    }\n    return 0;\n};\nconst calcAutoPan = (pos, bounds, speed = 15, distance = 40) => {\n    const xMovement = calcAutoPanVelocity(pos.x, distance, bounds.width - distance) * speed;\n    const yMovement = calcAutoPanVelocity(pos.y, distance, bounds.height - distance) * speed;\n    return [xMovement, yMovement];\n};\nconst getBoundsOfBoxes = (box1, box2) => ({\n    x: Math.min(box1.x, box2.x),\n    y: Math.min(box1.y, box2.y),\n    x2: Math.max(box1.x2, box2.x2),\n    y2: Math.max(box1.y2, box2.y2),\n});\nconst rectToBox = ({ x, y, width, height }) => ({\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n});\nconst boxToRect = ({ x, y, x2, y2 }) => ({\n    x,\n    y,\n    width: x2 - x,\n    height: y2 - y,\n});\nconst nodeToRect = (node, nodeOrigin = [0, 0]) => {\n    const { x, y } = isInternalNodeBase(node)\n        ? node.internals.positionAbsolute\n        : getNodePositionWithOrigin(node, nodeOrigin);\n    return {\n        x,\n        y,\n        width: node.measured?.width ?? node.width ?? node.initialWidth ?? 0,\n        height: node.measured?.height ?? node.height ?? node.initialHeight ?? 0,\n    };\n};\nconst nodeToBox = (node, nodeOrigin = [0, 0]) => {\n    const { x, y } = isInternalNodeBase(node)\n        ? node.internals.positionAbsolute\n        : getNodePositionWithOrigin(node, nodeOrigin);\n    return {\n        x,\n        y,\n        x2: x + (node.measured?.width ?? node.width ?? node.initialWidth ?? 0),\n        y2: y + (node.measured?.height ?? node.height ?? node.initialHeight ?? 0),\n    };\n};\nconst getBoundsOfRects = (rect1, rect2) => boxToRect(getBoundsOfBoxes(rectToBox(rect1), rectToBox(rect2)));\nconst getOverlappingArea = (rectA, rectB) => {\n    const xOverlap = Math.max(0, Math.min(rectA.x + rectA.width, rectB.x + rectB.width) - Math.max(rectA.x, rectB.x));\n    const yOverlap = Math.max(0, Math.min(rectA.y + rectA.height, rectB.y + rectB.height) - Math.max(rectA.y, rectB.y));\n    return Math.ceil(xOverlap * yOverlap);\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isRectObject = (obj) => isNumeric(obj.width) && isNumeric(obj.height) && isNumeric(obj.x) && isNumeric(obj.y);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nconst isNumeric = (n) => !isNaN(n) && isFinite(n);\n// used for a11y key board controls for nodes and edges\nconst devWarn = (id, message) => {\n    if (true) {\n        console.warn(`[React Flow]: ${message} Help: https://reactflow.dev/error#${id}`);\n    }\n};\nconst snapPosition = (position, snapGrid = [1, 1]) => {\n    return {\n        x: snapGrid[0] * Math.round(position.x / snapGrid[0]),\n        y: snapGrid[1] * Math.round(position.y / snapGrid[1]),\n    };\n};\nconst pointToRendererPoint = ({ x, y }, [tx, ty, tScale], snapToGrid = false, snapGrid = [1, 1]) => {\n    const position = {\n        x: (x - tx) / tScale,\n        y: (y - ty) / tScale,\n    };\n    return snapToGrid ? snapPosition(position, snapGrid) : position;\n};\nconst rendererPointToPoint = ({ x, y }, [tx, ty, tScale]) => {\n    return {\n        x: x * tScale + tx,\n        y: y * tScale + ty,\n    };\n};\n/**\n * Returns a viewport that encloses the given bounds with optional padding.\n * @public\n * @remarks You can determine bounds of nodes with {@link getNodesBounds} and {@link getBoundsOfRects}\n * @param bounds - Bounds to fit inside viewport\n * @param width - Width of the viewport\n * @param height  - Height of the viewport\n * @param minZoom - Minimum zoom level of the resulting viewport\n * @param maxZoom - Maximum zoom level of the resulting viewport\n * @param padding - Optional padding around the bounds\n * @returns A transforned {@link Viewport} that encloses the given bounds which you can pass to e.g. {@link setViewport}\n * @example\n * const { x, y, zoom } = getViewportForBounds(\n  { x: 0, y: 0, width: 100, height: 100},\n  1200, 800, 0.5, 2);\n */\nconst getViewportForBounds = (bounds, width, height, minZoom, maxZoom, padding) => {\n    const xZoom = width / (bounds.width * (1 + padding));\n    const yZoom = height / (bounds.height * (1 + padding));\n    const zoom = Math.min(xZoom, yZoom);\n    const clampedZoom = clamp(zoom, minZoom, maxZoom);\n    const boundsCenterX = bounds.x + bounds.width / 2;\n    const boundsCenterY = bounds.y + bounds.height / 2;\n    const x = width / 2 - boundsCenterX * clampedZoom;\n    const y = height / 2 - boundsCenterY * clampedZoom;\n    return { x, y, zoom: clampedZoom };\n};\nconst isMacOs = () => typeof navigator !== 'undefined' && navigator?.userAgent?.indexOf('Mac') >= 0;\nfunction isCoordinateExtent(extent) {\n    return extent !== undefined && extent !== 'parent';\n}\nfunction getNodeDimensions(node) {\n    return {\n        width: node.measured?.width ?? node.width ?? node.initialWidth ?? 0,\n        height: node.measured?.height ?? node.height ?? node.initialHeight ?? 0,\n    };\n}\nfunction nodeHasDimensions(node) {\n    return ((node.measured?.width ?? node.width ?? node.initialWidth) !== undefined &&\n        (node.measured?.height ?? node.height ?? node.initialHeight) !== undefined);\n}\n/**\n * Convert child position to aboslute position\n *\n * @internal\n * @param position\n * @param parentId\n * @param nodeLookup\n * @param nodeOrigin\n * @returns an internal node with an absolute position\n */\nfunction evaluateAbsolutePosition(position, dimensions = { width: 0, height: 0 }, parentId, nodeLookup, nodeOrigin) {\n    const positionAbsolute = { ...position };\n    const parent = nodeLookup.get(parentId);\n    if (parent) {\n        const origin = parent.origin || nodeOrigin;\n        positionAbsolute.x += parent.internals.positionAbsolute.x - (dimensions.width ?? 0) * origin[0];\n        positionAbsolute.y += parent.internals.positionAbsolute.y - (dimensions.height ?? 0) * origin[1];\n    }\n    return positionAbsolute;\n}\nfunction areSetsEqual(a, b) {\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const item of a) {\n        if (!b.has(item)) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getPointerPosition(event, { snapGrid = [0, 0], snapToGrid = false, transform, containerBounds }) {\n    const { x, y } = getEventPosition(event);\n    const pointerPos = pointToRendererPoint({ x: x - (containerBounds?.left ?? 0), y: y - (containerBounds?.top ?? 0) }, transform);\n    const { x: xSnapped, y: ySnapped } = snapToGrid ? snapPosition(pointerPos, snapGrid) : pointerPos;\n    // we need the snapped position in order to be able to skip unnecessary drag events\n    return {\n        xSnapped,\n        ySnapped,\n        ...pointerPos,\n    };\n}\nconst getDimensions = (node) => ({\n    width: node.offsetWidth,\n    height: node.offsetHeight,\n});\nconst getHostForElement = (element) => element?.getRootNode?.() || window?.document;\nconst inputTags = ['INPUT', 'SELECT', 'TEXTAREA'];\nfunction isInputDOMNode(event) {\n    // using composed path for handling shadow dom\n    const target = (event.composedPath?.()?.[0] || event.target);\n    if (target?.nodeType !== 1 /* Node.ELEMENT_NODE */)\n        return false;\n    const isInput = inputTags.includes(target.nodeName) || target.hasAttribute('contenteditable');\n    // when an input field is focused we don't want to trigger deletion or movement of nodes\n    return isInput || !!target.closest('.nokey');\n}\nconst isMouseEvent = (event) => 'clientX' in event;\nconst getEventPosition = (event, bounds) => {\n    const isMouse = isMouseEvent(event);\n    const evtX = isMouse ? event.clientX : event.touches?.[0].clientX;\n    const evtY = isMouse ? event.clientY : event.touches?.[0].clientY;\n    return {\n        x: evtX - (bounds?.left ?? 0),\n        y: evtY - (bounds?.top ?? 0),\n    };\n};\n// The handle bounds are calculated relative to the node element.\n// We store them in the internals object of the node in order to avoid\n// unnecessary recalculations.\nconst getHandleBounds = (type, nodeElement, nodeBounds, zoom, nodeId) => {\n    const handles = nodeElement.querySelectorAll(`.${type}`);\n    if (!handles || !handles.length) {\n        return null;\n    }\n    return Array.from(handles).map((handle) => {\n        const handleBounds = handle.getBoundingClientRect();\n        return {\n            id: handle.getAttribute('data-handleid'),\n            type,\n            nodeId,\n            position: handle.getAttribute('data-handlepos'),\n            x: (handleBounds.left - nodeBounds.left) / zoom,\n            y: (handleBounds.top - nodeBounds.top) / zoom,\n            ...getDimensions(handle),\n        };\n    });\n};\n\nfunction getBezierEdgeCenter({ sourceX, sourceY, targetX, targetY, sourceControlX, sourceControlY, targetControlX, targetControlY, }) {\n    // cubic bezier t=0.5 mid point, not the actual mid point, but easy to calculate\n    // https://stackoverflow.com/questions/67516101/how-to-find-distance-mid-point-of-bezier-curve\n    const centerX = sourceX * 0.125 + sourceControlX * 0.375 + targetControlX * 0.375 + targetX * 0.125;\n    const centerY = sourceY * 0.125 + sourceControlY * 0.375 + targetControlY * 0.375 + targetY * 0.125;\n    const offsetX = Math.abs(centerX - sourceX);\n    const offsetY = Math.abs(centerY - sourceY);\n    return [centerX, centerY, offsetX, offsetY];\n}\nfunction calculateControlOffset(distance, curvature) {\n    if (distance >= 0) {\n        return 0.5 * distance;\n    }\n    return curvature * 25 * Math.sqrt(-distance);\n}\nfunction getControlWithCurvature({ pos, x1, y1, x2, y2, c }) {\n    switch (pos) {\n        case Position.Left:\n            return [x1 - calculateControlOffset(x1 - x2, c), y1];\n        case Position.Right:\n            return [x1 + calculateControlOffset(x2 - x1, c), y1];\n        case Position.Top:\n            return [x1, y1 - calculateControlOffset(y1 - y2, c)];\n        case Position.Bottom:\n            return [x1, y1 + calculateControlOffset(y2 - y1, c)];\n    }\n}\n/**\n * Get a bezier path from source to target handle\n * @param params.sourceX - The x position of the source handle\n * @param params.sourceY - The y position of the source handle\n * @param params.sourcePosition - The position of the source handle (default: Position.Bottom)\n * @param params.targetX - The x position of the target handle\n * @param params.targetY - The y position of the target handle\n * @param params.targetPosition - The position of the target handle (default: Position.Top)\n * @param params.curvature - The curvature of the bezier edge\n * @returns A path string you can use in an SVG, the labelX and labelY position (center of path) and offsetX, offsetY between source handle and label\n * @example\n *  const source = { x: 0, y: 20 };\n    const target = { x: 150, y: 100 };\n    \n    const [path, labelX, labelY, offsetX, offsetY] = getBezierPath({\n      sourceX: source.x,\n      sourceY: source.y,\n      sourcePosition: Position.Right,\n      targetX: target.x,\n      targetY: target.y,\n      targetPosition: Position.Left,\n});\n */\nfunction getBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, curvature = 0.25, }) {\n    const [sourceControlX, sourceControlY] = getControlWithCurvature({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n        c: curvature,\n    });\n    const [targetControlX, targetControlY] = getControlWithCurvature({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n        c: curvature,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\n\n// this is used for straight edges and simple smoothstep edges (LTR, RTL, BTT, TTB)\nfunction getEdgeCenter({ sourceX, sourceY, targetX, targetY, }) {\n    const xOffset = Math.abs(targetX - sourceX) / 2;\n    const centerX = targetX < sourceX ? targetX + xOffset : targetX - xOffset;\n    const yOffset = Math.abs(targetY - sourceY) / 2;\n    const centerY = targetY < sourceY ? targetY + yOffset : targetY - yOffset;\n    return [centerX, centerY, xOffset, yOffset];\n}\nfunction getElevatedEdgeZIndex({ sourceNode, targetNode, selected = false, zIndex = 0, elevateOnSelect = false, }) {\n    if (!elevateOnSelect) {\n        return zIndex;\n    }\n    const edgeOrConnectedNodeSelected = selected || targetNode.selected || sourceNode.selected;\n    const selectedZIndex = Math.max(sourceNode.internals.z || 0, targetNode.internals.z || 0, 1000);\n    return zIndex + (edgeOrConnectedNodeSelected ? selectedZIndex : 0);\n}\nfunction isEdgeVisible({ sourceNode, targetNode, width, height, transform }) {\n    const edgeBox = getBoundsOfBoxes(nodeToBox(sourceNode), nodeToBox(targetNode));\n    if (edgeBox.x === edgeBox.x2) {\n        edgeBox.x2 += 1;\n    }\n    if (edgeBox.y === edgeBox.y2) {\n        edgeBox.y2 += 1;\n    }\n    const viewRect = {\n        x: -transform[0] / transform[2],\n        y: -transform[1] / transform[2],\n        width: width / transform[2],\n        height: height / transform[2],\n    };\n    return getOverlappingArea(viewRect, boxToRect(edgeBox)) > 0;\n}\nconst getEdgeId = ({ source, sourceHandle, target, targetHandle }) => `xy-edge__${source}${sourceHandle || ''}-${target}${targetHandle || ''}`;\nconst connectionExists = (edge, edges) => {\n    return edges.some((el) => el.source === edge.source &&\n        el.target === edge.target &&\n        (el.sourceHandle === edge.sourceHandle || (!el.sourceHandle && !edge.sourceHandle)) &&\n        (el.targetHandle === edge.targetHandle || (!el.targetHandle && !edge.targetHandle)));\n};\n/**\n * This util is a convenience function to add a new Edge to an array of edges\n * @remarks It also performs some validation to make sure you don't add an invalid edge or duplicate an existing one.\n * @public\n * @param edgeParams - Either an Edge or a Connection you want to add\n * @param edges -  The array of all current edges\n * @returns A new array of edges with the new edge added\n */\nconst addEdge = (edgeParams, edges) => {\n    if (!edgeParams.source || !edgeParams.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    let edge;\n    if (isEdgeBase(edgeParams)) {\n        edge = { ...edgeParams };\n    }\n    else {\n        edge = {\n            ...edgeParams,\n            id: getEdgeId(edgeParams),\n        };\n    }\n    if (connectionExists(edge, edges)) {\n        return edges;\n    }\n    if (edge.sourceHandle === null) {\n        delete edge.sourceHandle;\n    }\n    if (edge.targetHandle === null) {\n        delete edge.targetHandle;\n    }\n    return edges.concat(edge);\n};\n/**\n * A handy utility to reconnect an existing edge with new properties\n * @param oldEdge - The edge you want to update\n * @param newConnection - The new connection you want to update the edge with\n * @param edges - The array of all current edges\n * @param options.shouldReplaceId - should the id of the old edge be replaced with the new connection id\n * @returns the updated edges array\n */\nconst reconnectEdge = (oldEdge, newConnection, edges, options = { shouldReplaceId: true }) => {\n    const { id: oldEdgeId, ...rest } = oldEdge;\n    if (!newConnection.source || !newConnection.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    const foundEdge = edges.find((e) => e.id === oldEdge.id);\n    if (!foundEdge) {\n        devWarn('007', errorMessages['error007'](oldEdgeId));\n        return edges;\n    }\n    // Remove old edge and create the new edge with parameters of old edge.\n    const edge = {\n        ...rest,\n        id: options.shouldReplaceId ? getEdgeId(newConnection) : oldEdgeId,\n        source: newConnection.source,\n        target: newConnection.target,\n        sourceHandle: newConnection.sourceHandle,\n        targetHandle: newConnection.targetHandle,\n    };\n    return edges.filter((e) => e.id !== oldEdgeId).concat(edge);\n};\n\n/**\n * Get a straight path from source to target handle\n * @param params.sourceX - The x position of the source handle\n * @param params.sourceY - The y position of the source handle\n * @param params.targetX - The x position of the target handle\n * @param params.targetY - The y position of the target handle\n * @returns A path string you can use in an SVG, the labelX and labelY position (center of path) and offsetX, offsetY between source handle and label\n * @example\n *  const source = { x: 0, y: 20 };\n    const target = { x: 150, y: 100 };\n    \n    const [path, labelX, labelY, offsetX, offsetY] = getStraightPath({\n      sourceX: source.x,\n      sourceY: source.y,\n      sourcePosition: Position.Right,\n      targetX: target.x,\n      targetY: target.y,\n      targetPosition: Position.Left,\n    });\n */\nfunction getStraightPath({ sourceX, sourceY, targetX, targetY, }) {\n    const [labelX, labelY, offsetX, offsetY] = getEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n    });\n    return [`M ${sourceX},${sourceY}L ${targetX},${targetY}`, labelX, labelY, offsetX, offsetY];\n}\n\nconst handleDirections = {\n    [Position.Left]: { x: -1, y: 0 },\n    [Position.Right]: { x: 1, y: 0 },\n    [Position.Top]: { x: 0, y: -1 },\n    [Position.Bottom]: { x: 0, y: 1 },\n};\nconst getDirection = ({ source, sourcePosition = Position.Bottom, target, }) => {\n    if (sourcePosition === Position.Left || sourcePosition === Position.Right) {\n        return source.x < target.x ? { x: 1, y: 0 } : { x: -1, y: 0 };\n    }\n    return source.y < target.y ? { x: 0, y: 1 } : { x: 0, y: -1 };\n};\nconst distance = (a, b) => Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));\n// ith this function we try to mimic a orthogonal edge routing behaviour\n// It's not as good as a real orthogonal edge routing but it's faster and good enough as a default for step and smooth step edges\nfunction getPoints({ source, sourcePosition = Position.Bottom, target, targetPosition = Position.Top, center, offset, }) {\n    const sourceDir = handleDirections[sourcePosition];\n    const targetDir = handleDirections[targetPosition];\n    const sourceGapped = { x: source.x + sourceDir.x * offset, y: source.y + sourceDir.y * offset };\n    const targetGapped = { x: target.x + targetDir.x * offset, y: target.y + targetDir.y * offset };\n    const dir = getDirection({\n        source: sourceGapped,\n        sourcePosition,\n        target: targetGapped,\n    });\n    const dirAccessor = dir.x !== 0 ? 'x' : 'y';\n    const currDir = dir[dirAccessor];\n    let points = [];\n    let centerX, centerY;\n    const sourceGapOffset = { x: 0, y: 0 };\n    const targetGapOffset = { x: 0, y: 0 };\n    const [defaultCenterX, defaultCenterY, defaultOffsetX, defaultOffsetY] = getEdgeCenter({\n        sourceX: source.x,\n        sourceY: source.y,\n        targetX: target.x,\n        targetY: target.y,\n    });\n    // opposite handle positions, default case\n    if (sourceDir[dirAccessor] * targetDir[dirAccessor] === -1) {\n        centerX = center.x ?? defaultCenterX;\n        centerY = center.y ?? defaultCenterY;\n        //    --->\n        //    |\n        // >---\n        const verticalSplit = [\n            { x: centerX, y: sourceGapped.y },\n            { x: centerX, y: targetGapped.y },\n        ];\n        //    |\n        //  ---\n        //  |\n        const horizontalSplit = [\n            { x: sourceGapped.x, y: centerY },\n            { x: targetGapped.x, y: centerY },\n        ];\n        if (sourceDir[dirAccessor] === currDir) {\n            points = dirAccessor === 'x' ? verticalSplit : horizontalSplit;\n        }\n        else {\n            points = dirAccessor === 'x' ? horizontalSplit : verticalSplit;\n        }\n    }\n    else {\n        // sourceTarget means we take x from source and y from target, targetSource is the opposite\n        const sourceTarget = [{ x: sourceGapped.x, y: targetGapped.y }];\n        const targetSource = [{ x: targetGapped.x, y: sourceGapped.y }];\n        // this handles edges with same handle positions\n        if (dirAccessor === 'x') {\n            points = sourceDir.x === currDir ? targetSource : sourceTarget;\n        }\n        else {\n            points = sourceDir.y === currDir ? sourceTarget : targetSource;\n        }\n        if (sourcePosition === targetPosition) {\n            const diff = Math.abs(source[dirAccessor] - target[dirAccessor]);\n            // if an edge goes from right to right for example (sourcePosition === targetPosition) and the distance between source.x and target.x is less than the offset, the added point and the gapped source/target will overlap. This leads to a weird edge path. To avoid this we add a gapOffset to the source/target\n            if (diff <= offset) {\n                const gapOffset = Math.min(offset - 1, offset - diff);\n                if (sourceDir[dirAccessor] === currDir) {\n                    sourceGapOffset[dirAccessor] = (sourceGapped[dirAccessor] > source[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n                else {\n                    targetGapOffset[dirAccessor] = (targetGapped[dirAccessor] > target[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n            }\n        }\n        // these are conditions for handling mixed handle positions like Right -> Bottom for example\n        if (sourcePosition !== targetPosition) {\n            const dirAccessorOpposite = dirAccessor === 'x' ? 'y' : 'x';\n            const isSameDir = sourceDir[dirAccessor] === targetDir[dirAccessorOpposite];\n            const sourceGtTargetOppo = sourceGapped[dirAccessorOpposite] > targetGapped[dirAccessorOpposite];\n            const sourceLtTargetOppo = sourceGapped[dirAccessorOpposite] < targetGapped[dirAccessorOpposite];\n            const flipSourceTarget = (sourceDir[dirAccessor] === 1 && ((!isSameDir && sourceGtTargetOppo) || (isSameDir && sourceLtTargetOppo))) ||\n                (sourceDir[dirAccessor] !== 1 && ((!isSameDir && sourceLtTargetOppo) || (isSameDir && sourceGtTargetOppo)));\n            if (flipSourceTarget) {\n                points = dirAccessor === 'x' ? sourceTarget : targetSource;\n            }\n        }\n        const sourceGapPoint = { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y };\n        const targetGapPoint = { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y };\n        const maxXDistance = Math.max(Math.abs(sourceGapPoint.x - points[0].x), Math.abs(targetGapPoint.x - points[0].x));\n        const maxYDistance = Math.max(Math.abs(sourceGapPoint.y - points[0].y), Math.abs(targetGapPoint.y - points[0].y));\n        // we want to place the label on the longest segment of the edge\n        if (maxXDistance >= maxYDistance) {\n            centerX = (sourceGapPoint.x + targetGapPoint.x) / 2;\n            centerY = points[0].y;\n        }\n        else {\n            centerX = points[0].x;\n            centerY = (sourceGapPoint.y + targetGapPoint.y) / 2;\n        }\n    }\n    const pathPoints = [\n        source,\n        { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y },\n        ...points,\n        { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y },\n        target,\n    ];\n    return [pathPoints, centerX, centerY, defaultOffsetX, defaultOffsetY];\n}\nfunction getBend(a, b, c, size) {\n    const bendSize = Math.min(distance(a, b) / 2, distance(b, c) / 2, size);\n    const { x, y } = b;\n    // no bend\n    if ((a.x === x && x === c.x) || (a.y === y && y === c.y)) {\n        return `L${x} ${y}`;\n    }\n    // first segment is horizontal\n    if (a.y === y) {\n        const xDir = a.x < c.x ? -1 : 1;\n        const yDir = a.y < c.y ? 1 : -1;\n        return `L ${x + bendSize * xDir},${y}Q ${x},${y} ${x},${y + bendSize * yDir}`;\n    }\n    const xDir = a.x < c.x ? 1 : -1;\n    const yDir = a.y < c.y ? -1 : 1;\n    return `L ${x},${y + bendSize * yDir}Q ${x},${y} ${x + bendSize * xDir},${y}`;\n}\n/**\n * Get a smooth step path from source to target handle\n * @param params.sourceX - The x position of the source handle\n * @param params.sourceY - The y position of the source handle\n * @param params.sourcePosition - The position of the source handle (default: Position.Bottom)\n * @param params.targetX - The x position of the target handle\n * @param params.targetY - The y position of the target handle\n * @param params.targetPosition - The position of the target handle (default: Position.Top)\n * @returns A path string you can use in an SVG, the labelX and labelY position (center of path) and offsetX, offsetY between source handle and label\n * @example\n *  const source = { x: 0, y: 20 };\n    const target = { x: 150, y: 100 };\n    \n    const [path, labelX, labelY, offsetX, offsetY] = getSmoothStepPath({\n      sourceX: source.x,\n      sourceY: source.y,\n      sourcePosition: Position.Right,\n      targetX: target.x,\n      targetY: target.y,\n      targetPosition: Position.Left,\n    });\n */\nfunction getSmoothStepPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, borderRadius = 5, centerX, centerY, offset = 20, }) {\n    const [points, labelX, labelY, offsetX, offsetY] = getPoints({\n        source: { x: sourceX, y: sourceY },\n        sourcePosition,\n        target: { x: targetX, y: targetY },\n        targetPosition,\n        center: { x: centerX, y: centerY },\n        offset,\n    });\n    const path = points.reduce((res, p, i) => {\n        let segment = '';\n        if (i > 0 && i < points.length - 1) {\n            segment = getBend(points[i - 1], p, points[i + 1], borderRadius);\n        }\n        else {\n            segment = `${i === 0 ? 'M' : 'L'}${p.x} ${p.y}`;\n        }\n        res += segment;\n        return res;\n    }, '');\n    return [path, labelX, labelY, offsetX, offsetY];\n}\n\nfunction isNodeInitialized(node) {\n    return (node &&\n        !!(node.internals.handleBounds || node.handles?.length) &&\n        !!(node.measured.width || node.width || node.initialWidth));\n}\nfunction getEdgePosition(params) {\n    const { sourceNode, targetNode } = params;\n    if (!isNodeInitialized(sourceNode) || !isNodeInitialized(targetNode)) {\n        return null;\n    }\n    const sourceHandleBounds = sourceNode.internals.handleBounds || toHandleBounds(sourceNode.handles);\n    const targetHandleBounds = targetNode.internals.handleBounds || toHandleBounds(targetNode.handles);\n    const sourceHandle = getHandle$1(sourceHandleBounds?.source ?? [], params.sourceHandle);\n    const targetHandle = getHandle$1(\n    // when connection type is loose we can define all handles as sources and connect source -> source\n    params.connectionMode === ConnectionMode.Strict\n        ? targetHandleBounds?.target ?? []\n        : (targetHandleBounds?.target ?? []).concat(targetHandleBounds?.source ?? []), params.targetHandle);\n    if (!sourceHandle || !targetHandle) {\n        params.onError?.('008', errorMessages['error008'](!sourceHandle ? 'source' : 'target', {\n            id: params.id,\n            sourceHandle: params.sourceHandle,\n            targetHandle: params.targetHandle,\n        }));\n        return null;\n    }\n    const sourcePosition = sourceHandle?.position || Position.Bottom;\n    const targetPosition = targetHandle?.position || Position.Top;\n    const source = getHandlePosition(sourceNode, sourceHandle, sourcePosition);\n    const target = getHandlePosition(targetNode, targetHandle, targetPosition);\n    return {\n        sourceX: source.x,\n        sourceY: source.y,\n        targetX: target.x,\n        targetY: target.y,\n        sourcePosition,\n        targetPosition,\n    };\n}\nfunction toHandleBounds(handles) {\n    if (!handles) {\n        return null;\n    }\n    const source = [];\n    const target = [];\n    for (const handle of handles) {\n        handle.width = handle.width ?? 1;\n        handle.height = handle.height ?? 1;\n        if (handle.type === 'source') {\n            source.push(handle);\n        }\n        else if (handle.type === 'target') {\n            target.push(handle);\n        }\n    }\n    return {\n        source,\n        target,\n    };\n}\nfunction getHandlePosition(node, handle, fallbackPosition = Position.Left, center = false) {\n    const x = (handle?.x ?? 0) + node.internals.positionAbsolute.x;\n    const y = (handle?.y ?? 0) + node.internals.positionAbsolute.y;\n    const { width, height } = handle ?? getNodeDimensions(node);\n    if (center) {\n        return { x: x + width / 2, y: y + height / 2 };\n    }\n    const position = handle?.position ?? fallbackPosition;\n    switch (position) {\n        case Position.Top:\n            return { x: x + width / 2, y };\n        case Position.Right:\n            return { x: x + width, y: y + height / 2 };\n        case Position.Bottom:\n            return { x: x + width / 2, y: y + height };\n        case Position.Left:\n            return { x, y: y + height / 2 };\n    }\n}\nfunction getHandle$1(bounds, handleId) {\n    if (!bounds) {\n        return null;\n    }\n    // if no handleId is given, we use the first handle, otherwise we check for the id\n    return (!handleId ? bounds[0] : bounds.find((d) => d.id === handleId)) || null;\n}\n\nfunction getMarkerId(marker, id) {\n    if (!marker) {\n        return '';\n    }\n    if (typeof marker === 'string') {\n        return marker;\n    }\n    const idPrefix = id ? `${id}__` : '';\n    return `${idPrefix}${Object.keys(marker)\n        .sort()\n        .map((key) => `${key}=${marker[key]}`)\n        .join('&')}`;\n}\nfunction createMarkerIds(edges, { id, defaultColor, defaultMarkerStart, defaultMarkerEnd, }) {\n    const ids = new Set();\n    return edges\n        .reduce((markers, edge) => {\n        [edge.markerStart || defaultMarkerStart, edge.markerEnd || defaultMarkerEnd].forEach((marker) => {\n            if (marker && typeof marker === 'object') {\n                const markerId = getMarkerId(marker, id);\n                if (!ids.has(markerId)) {\n                    markers.push({ id: markerId, color: marker.color || defaultColor, ...marker });\n                    ids.add(markerId);\n                }\n            }\n        });\n        return markers;\n    }, [])\n        .sort((a, b) => a.id.localeCompare(b.id));\n}\n\nfunction getNodeToolbarTransform(nodeRect, viewport, position, offset, align) {\n    let alignmentOffset = 0.5;\n    if (align === 'start') {\n        alignmentOffset = 0;\n    }\n    else if (align === 'end') {\n        alignmentOffset = 1;\n    }\n    // position === Position.Top\n    // we set the x any y position of the toolbar based on the nodes position\n    let pos = [\n        (nodeRect.x + nodeRect.width * alignmentOffset) * viewport.zoom + viewport.x,\n        nodeRect.y * viewport.zoom + viewport.y - offset,\n    ];\n    // and than shift it based on the alignment. The shift values are in %.\n    let shift = [-100 * alignmentOffset, -100];\n    switch (position) {\n        case Position.Right:\n            pos = [\n                (nodeRect.x + nodeRect.width) * viewport.zoom + viewport.x + offset,\n                (nodeRect.y + nodeRect.height * alignmentOffset) * viewport.zoom + viewport.y,\n            ];\n            shift = [0, -100 * alignmentOffset];\n            break;\n        case Position.Bottom:\n            pos[1] = (nodeRect.y + nodeRect.height) * viewport.zoom + viewport.y + offset;\n            shift[1] = 0;\n            break;\n        case Position.Left:\n            pos = [\n                nodeRect.x * viewport.zoom + viewport.x - offset,\n                (nodeRect.y + nodeRect.height * alignmentOffset) * viewport.zoom + viewport.y,\n            ];\n            shift = [-100, -100 * alignmentOffset];\n            break;\n    }\n    return `translate(${pos[0]}px, ${pos[1]}px) translate(${shift[0]}%, ${shift[1]}%)`;\n}\n\nconst defaultOptions = {\n    nodeOrigin: [0, 0],\n    nodeExtent: infiniteExtent,\n    elevateNodesOnSelect: true,\n    defaults: {},\n};\nconst adoptUserNodesDefaultOptions = {\n    ...defaultOptions,\n    checkEquality: true,\n};\nfunction mergeObjects(base, incoming) {\n    const result = { ...base };\n    for (const key in incoming) {\n        if (incoming[key] !== undefined) {\n            // typecast is safe here, because we check for undefined\n            result[key] = incoming[key];\n        }\n    }\n    return result;\n}\nfunction updateAbsolutePositions(nodeLookup, parentLookup, options) {\n    const _options = mergeObjects(defaultOptions, options);\n    for (const node of nodeLookup.values()) {\n        if (node.parentId) {\n            updateChildNode(node, nodeLookup, parentLookup, _options);\n        }\n        else {\n            const positionWithOrigin = getNodePositionWithOrigin(node, _options.nodeOrigin);\n            const extent = isCoordinateExtent(node.extent) ? node.extent : _options.nodeExtent;\n            const clampedPosition = clampPosition(positionWithOrigin, extent, getNodeDimensions(node));\n            node.internals.positionAbsolute = clampedPosition;\n        }\n    }\n}\nfunction adoptUserNodes(nodes, nodeLookup, parentLookup, options) {\n    const _options = mergeObjects(adoptUserNodesDefaultOptions, options);\n    const tmpLookup = new Map(nodeLookup);\n    const selectedNodeZ = _options?.elevateNodesOnSelect ? 1000 : 0;\n    nodeLookup.clear();\n    parentLookup.clear();\n    for (const userNode of nodes) {\n        let internalNode = tmpLookup.get(userNode.id);\n        if (_options.checkEquality && userNode === internalNode?.internals.userNode) {\n            nodeLookup.set(userNode.id, internalNode);\n        }\n        else {\n            const positionWithOrigin = getNodePositionWithOrigin(userNode, _options.nodeOrigin);\n            const extent = isCoordinateExtent(userNode.extent) ? userNode.extent : _options.nodeExtent;\n            const clampedPosition = clampPosition(positionWithOrigin, extent, getNodeDimensions(userNode));\n            internalNode = {\n                ..._options.defaults,\n                ...userNode,\n                measured: {\n                    width: userNode.measured?.width,\n                    height: userNode.measured?.height,\n                },\n                internals: {\n                    positionAbsolute: clampedPosition,\n                    // if user re-initializes the node or removes `measured` for whatever reason, we reset the handleBounds so that the node gets re-measured\n                    handleBounds: !userNode.measured ? undefined : internalNode?.internals.handleBounds,\n                    z: calculateZ(userNode, selectedNodeZ),\n                    userNode,\n                },\n            };\n            nodeLookup.set(userNode.id, internalNode);\n        }\n        if (userNode.parentId) {\n            updateChildNode(internalNode, nodeLookup, parentLookup, options);\n        }\n    }\n}\nfunction updateParentLookup(node, parentLookup) {\n    if (!node.parentId) {\n        return;\n    }\n    const childNodes = parentLookup.get(node.parentId);\n    if (childNodes) {\n        childNodes.set(node.id, node);\n    }\n    else {\n        parentLookup.set(node.parentId, new Map([[node.id, node]]));\n    }\n}\n/**\n * Updates positionAbsolute and zIndex of a child node and the parentLookup.\n */\nfunction updateChildNode(node, nodeLookup, parentLookup, options) {\n    const { elevateNodesOnSelect, nodeOrigin, nodeExtent } = mergeObjects(defaultOptions, options);\n    const parentId = node.parentId;\n    const parentNode = nodeLookup.get(parentId);\n    if (!parentNode) {\n        console.warn(`Parent node ${parentId} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);\n        return;\n    }\n    updateParentLookup(node, parentLookup);\n    const selectedNodeZ = elevateNodesOnSelect ? 1000 : 0;\n    const { x, y, z } = calculateChildXYZ(node, parentNode, nodeOrigin, nodeExtent, selectedNodeZ);\n    const { positionAbsolute } = node.internals;\n    const positionChanged = x !== positionAbsolute.x || y !== positionAbsolute.y;\n    if (positionChanged || z !== node.internals.z) {\n        // we create a new object to mark the node as updated\n        nodeLookup.set(node.id, {\n            ...node,\n            internals: {\n                ...node.internals,\n                positionAbsolute: positionChanged ? { x, y } : positionAbsolute,\n                z,\n            },\n        });\n    }\n}\nfunction calculateZ(node, selectedNodeZ) {\n    return (isNumeric(node.zIndex) ? node.zIndex : 0) + (node.selected ? selectedNodeZ : 0);\n}\nfunction calculateChildXYZ(childNode, parentNode, nodeOrigin, nodeExtent, selectedNodeZ) {\n    const { x: parentX, y: parentY } = parentNode.internals.positionAbsolute;\n    const childDimensions = getNodeDimensions(childNode);\n    const positionWithOrigin = getNodePositionWithOrigin(childNode, nodeOrigin);\n    const clampedPosition = isCoordinateExtent(childNode.extent)\n        ? clampPosition(positionWithOrigin, childNode.extent, childDimensions)\n        : positionWithOrigin;\n    let absolutePosition = clampPosition({ x: parentX + clampedPosition.x, y: parentY + clampedPosition.y }, nodeExtent, childDimensions);\n    if (childNode.extent === 'parent') {\n        absolutePosition = clampPositionToParent(absolutePosition, childDimensions, parentNode);\n    }\n    const childZ = calculateZ(childNode, selectedNodeZ);\n    const parentZ = parentNode.internals.z ?? 0;\n    return {\n        x: absolutePosition.x,\n        y: absolutePosition.y,\n        z: parentZ > childZ ? parentZ : childZ,\n    };\n}\nfunction handleExpandParent(children, nodeLookup, parentLookup, nodeOrigin = [0, 0]) {\n    const changes = [];\n    const parentExpansions = new Map();\n    // determine the expanded rectangle the child nodes would take for each parent\n    for (const child of children) {\n        const parent = nodeLookup.get(child.parentId);\n        if (!parent) {\n            continue;\n        }\n        const parentRect = parentExpansions.get(child.parentId)?.expandedRect ?? nodeToRect(parent);\n        const expandedRect = getBoundsOfRects(parentRect, child.rect);\n        parentExpansions.set(child.parentId, { expandedRect, parent });\n    }\n    if (parentExpansions.size > 0) {\n        parentExpansions.forEach(({ expandedRect, parent }, parentId) => {\n            // determine the position & dimensions of the parent\n            const positionAbsolute = parent.internals.positionAbsolute;\n            const dimensions = getNodeDimensions(parent);\n            const origin = parent.origin ?? nodeOrigin;\n            // determine how much the parent expands in width and position\n            const xChange = expandedRect.x < positionAbsolute.x ? Math.round(Math.abs(positionAbsolute.x - expandedRect.x)) : 0;\n            const yChange = expandedRect.y < positionAbsolute.y ? Math.round(Math.abs(positionAbsolute.y - expandedRect.y)) : 0;\n            const newWidth = Math.max(dimensions.width, Math.round(expandedRect.width));\n            const newHeight = Math.max(dimensions.height, Math.round(expandedRect.height));\n            const widthChange = (newWidth - dimensions.width) * origin[0];\n            const heightChange = (newHeight - dimensions.height) * origin[1];\n            // We need to correct the position of the parent node if the origin is not [0,0]\n            if (xChange > 0 || yChange > 0 || widthChange || heightChange) {\n                changes.push({\n                    id: parentId,\n                    type: 'position',\n                    position: {\n                        x: parent.position.x - xChange + widthChange,\n                        y: parent.position.y - yChange + heightChange,\n                    },\n                });\n                // We move all child nodes in the oppsite direction\n                // so the x,y changes of the parent do not move the children\n                parentLookup.get(parentId)?.forEach((childNode) => {\n                    if (!children.some((child) => child.id === childNode.id)) {\n                        changes.push({\n                            id: childNode.id,\n                            type: 'position',\n                            position: {\n                                x: childNode.position.x + xChange,\n                                y: childNode.position.y + yChange,\n                            },\n                        });\n                    }\n                });\n            }\n            // We need to correct the dimensions of the parent node if the origin is not [0,0]\n            if (dimensions.width < expandedRect.width || dimensions.height < expandedRect.height || xChange || yChange) {\n                changes.push({\n                    id: parentId,\n                    type: 'dimensions',\n                    setAttributes: true,\n                    dimensions: {\n                        width: newWidth + (xChange ? origin[0] * xChange - widthChange : 0),\n                        height: newHeight + (yChange ? origin[1] * yChange - heightChange : 0),\n                    },\n                });\n            }\n        });\n    }\n    return changes;\n}\nfunction updateNodeInternals(updates, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent) {\n    const viewportNode = domNode?.querySelector('.xyflow__viewport');\n    let updatedInternals = false;\n    if (!viewportNode) {\n        return { changes: [], updatedInternals };\n    }\n    const changes = [];\n    const style = window.getComputedStyle(viewportNode);\n    const { m22: zoom } = new window.DOMMatrixReadOnly(style.transform);\n    // in this array we collect nodes, that might trigger changes (like expanding parent)\n    const parentExpandChildren = [];\n    for (const update of updates.values()) {\n        const node = nodeLookup.get(update.id);\n        if (!node) {\n            continue;\n        }\n        if (node.hidden) {\n            nodeLookup.set(node.id, {\n                ...node,\n                internals: {\n                    ...node.internals,\n                    handleBounds: undefined,\n                },\n            });\n            updatedInternals = true;\n            continue;\n        }\n        const dimensions = getDimensions(update.nodeElement);\n        const dimensionChanged = node.measured.width !== dimensions.width || node.measured.height !== dimensions.height;\n        const doUpdate = !!(dimensions.width &&\n            dimensions.height &&\n            (dimensionChanged || !node.internals.handleBounds || update.force));\n        if (doUpdate) {\n            const nodeBounds = update.nodeElement.getBoundingClientRect();\n            const extent = isCoordinateExtent(node.extent) ? node.extent : nodeExtent;\n            let { positionAbsolute } = node.internals;\n            if (node.parentId && node.extent === 'parent') {\n                positionAbsolute = clampPositionToParent(positionAbsolute, dimensions, nodeLookup.get(node.parentId));\n            }\n            else if (extent) {\n                positionAbsolute = clampPosition(positionAbsolute, extent, dimensions);\n            }\n            const newNode = {\n                ...node,\n                measured: dimensions,\n                internals: {\n                    ...node.internals,\n                    positionAbsolute,\n                    handleBounds: {\n                        source: getHandleBounds('source', update.nodeElement, nodeBounds, zoom, node.id),\n                        target: getHandleBounds('target', update.nodeElement, nodeBounds, zoom, node.id),\n                    },\n                },\n            };\n            nodeLookup.set(node.id, newNode);\n            if (node.parentId) {\n                updateChildNode(newNode, nodeLookup, parentLookup, { nodeOrigin });\n            }\n            updatedInternals = true;\n            if (dimensionChanged) {\n                changes.push({\n                    id: node.id,\n                    type: 'dimensions',\n                    dimensions,\n                });\n                if (node.expandParent && node.parentId) {\n                    parentExpandChildren.push({\n                        id: node.id,\n                        parentId: node.parentId,\n                        rect: nodeToRect(newNode, nodeOrigin),\n                    });\n                }\n            }\n        }\n    }\n    if (parentExpandChildren.length > 0) {\n        const parentExpandChanges = handleExpandParent(parentExpandChildren, nodeLookup, parentLookup, nodeOrigin);\n        changes.push(...parentExpandChanges);\n    }\n    return { changes, updatedInternals };\n}\nasync function panBy({ delta, panZoom, transform, translateExtent, width, height, }) {\n    if (!panZoom || (!delta.x && !delta.y)) {\n        return Promise.resolve(false);\n    }\n    const nextViewport = await panZoom.setViewportConstrained({\n        x: transform[0] + delta.x,\n        y: transform[1] + delta.y,\n        zoom: transform[2],\n    }, [\n        [0, 0],\n        [width, height],\n    ], translateExtent);\n    const transformChanged = !!nextViewport &&\n        (nextViewport.x !== transform[0] || nextViewport.y !== transform[1] || nextViewport.k !== transform[2]);\n    return Promise.resolve(transformChanged);\n}\n/**\n * this function adds the connection to the connectionLookup\n * at the following keys: nodeId-type-handleId, nodeId-type and nodeId\n * @param type type of the connection\n * @param connection connection that should be added to the lookup\n * @param connectionKey at which key the connection should be added\n * @param connectionLookup reference to the connection lookup\n * @param nodeId nodeId of the connection\n * @param handleId handleId of the conneciton\n */\nfunction addConnectionToLookup(type, connection, connectionKey, connectionLookup, nodeId, handleId) {\n    // We add the connection to the connectionLookup at the following keys\n    // 1. nodeId, 2. nodeId-type, 3. nodeId-type-handleId\n    // If the key already exists, we add the connection to the existing map\n    let key = nodeId;\n    const nodeMap = connectionLookup.get(key) || new Map();\n    connectionLookup.set(key, nodeMap.set(connectionKey, connection));\n    key = `${nodeId}-${type}`;\n    const typeMap = connectionLookup.get(key) || new Map();\n    connectionLookup.set(key, typeMap.set(connectionKey, connection));\n    if (handleId) {\n        key = `${nodeId}-${type}-${handleId}`;\n        const handleMap = connectionLookup.get(key) || new Map();\n        connectionLookup.set(key, handleMap.set(connectionKey, connection));\n    }\n}\nfunction updateConnectionLookup(connectionLookup, edgeLookup, edges) {\n    connectionLookup.clear();\n    edgeLookup.clear();\n    for (const edge of edges) {\n        const { source: sourceNode, target: targetNode, sourceHandle = null, targetHandle = null } = edge;\n        const connection = { edgeId: edge.id, source: sourceNode, target: targetNode, sourceHandle, targetHandle };\n        const sourceKey = `${sourceNode}-${sourceHandle}--${targetNode}-${targetHandle}`;\n        const targetKey = `${targetNode}-${targetHandle}--${sourceNode}-${sourceHandle}`;\n        addConnectionToLookup('source', connection, targetKey, connectionLookup, sourceNode, sourceHandle);\n        addConnectionToLookup('target', connection, sourceKey, connectionLookup, targetNode, targetHandle);\n        edgeLookup.set(edge.id, edge);\n    }\n}\n\nfunction shallowNodeData(a, b) {\n    if (a === null || b === null) {\n        return false;\n    }\n    const _a = Array.isArray(a) ? a : [a];\n    const _b = Array.isArray(b) ? b : [b];\n    if (_a.length !== _b.length) {\n        return false;\n    }\n    for (let i = 0; i < _a.length; i++) {\n        if (_a[i].id !== _b[i].id || _a[i].type !== _b[i].type || !Object.is(_a[i].data, _b[i].data)) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction isParentSelected(node, nodeLookup) {\n    if (!node.parentId) {\n        return false;\n    }\n    const parentNode = nodeLookup.get(node.parentId);\n    if (!parentNode) {\n        return false;\n    }\n    if (parentNode.selected) {\n        return true;\n    }\n    return isParentSelected(parentNode, nodeLookup);\n}\nfunction hasSelector(target, selector, domNode) {\n    let current = target;\n    do {\n        if (current?.matches?.(selector))\n            return true;\n        if (current === domNode)\n            return false;\n        current = current?.parentElement;\n    } while (current);\n    return false;\n}\n// looks for all selected nodes and created a NodeDragItem for each of them\nfunction getDragItems(nodeLookup, nodesDraggable, mousePos, nodeId) {\n    const dragItems = new Map();\n    for (const [id, node] of nodeLookup) {\n        if ((node.selected || node.id === nodeId) &&\n            (!node.parentId || !isParentSelected(node, nodeLookup)) &&\n            (node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'))) {\n            const internalNode = nodeLookup.get(id);\n            if (internalNode) {\n                dragItems.set(id, {\n                    id,\n                    position: internalNode.position || { x: 0, y: 0 },\n                    distance: {\n                        x: mousePos.x - internalNode.internals.positionAbsolute.x,\n                        y: mousePos.y - internalNode.internals.positionAbsolute.y,\n                    },\n                    extent: internalNode.extent,\n                    parentId: internalNode.parentId,\n                    origin: internalNode.origin,\n                    expandParent: internalNode.expandParent,\n                    internals: {\n                        positionAbsolute: internalNode.internals.positionAbsolute || { x: 0, y: 0 },\n                    },\n                    measured: {\n                        width: internalNode.measured.width ?? 0,\n                        height: internalNode.measured.height ?? 0,\n                    },\n                });\n            }\n        }\n    }\n    return dragItems;\n}\n// returns two params:\n// 1. the dragged node (or the first of the list, if we are dragging a node selection)\n// 2. array of selected nodes (for multi selections)\nfunction getEventHandlerParams({ nodeId, dragItems, nodeLookup, dragging = true, }) {\n    const nodesFromDragItems = [];\n    for (const [id, dragItem] of dragItems) {\n        const node = nodeLookup.get(id)?.internals.userNode;\n        if (node) {\n            nodesFromDragItems.push({\n                ...node,\n                position: dragItem.position,\n                dragging,\n            });\n        }\n    }\n    if (!nodeId) {\n        return [nodesFromDragItems[0], nodesFromDragItems];\n    }\n    const node = nodeLookup.get(nodeId)?.internals.userNode;\n    return [\n        !node\n            ? nodesFromDragItems[0]\n            : {\n                ...node,\n                position: dragItems.get(nodeId)?.position || node.position,\n                dragging,\n            },\n        nodesFromDragItems,\n    ];\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction XYDrag({ onNodeMouseDown, getStoreItems, onDragStart, onDrag, onDragStop, }) {\n    let lastPos = { x: null, y: null };\n    let autoPanId = 0;\n    let dragItems = new Map();\n    let autoPanStarted = false;\n    let mousePosition = { x: 0, y: 0 };\n    let containerBounds = null;\n    let dragStarted = false;\n    let d3Selection = null;\n    let abortDrag = false; // prevents unintentional dragging on multitouch\n    // public functions\n    function update({ noDragClassName, handleSelector, domNode, isSelectable, nodeId, nodeClickDistance = 0, }) {\n        d3Selection = (0,d3_selection__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domNode);\n        function updateNodes({ x, y }, dragEvent) {\n            const { nodeLookup, nodeExtent, snapGrid, snapToGrid, nodeOrigin, onNodeDrag, onSelectionDrag, onError, updateNodePositions, } = getStoreItems();\n            lastPos = { x, y };\n            let hasChange = false;\n            let nodesBox = { x: 0, y: 0, x2: 0, y2: 0 };\n            if (dragItems.size > 1 && nodeExtent) {\n                const rect = getInternalNodesBounds(dragItems);\n                nodesBox = rectToBox(rect);\n            }\n            for (const [id, dragItem] of dragItems) {\n                if (!nodeLookup.has(id)) {\n                    // if the node is not in the nodeLookup anymore, it was probably deleted while dragging\n                    // and we don't need to update it anymore\n                    continue;\n                }\n                let nextPosition = { x: x - dragItem.distance.x, y: y - dragItem.distance.y };\n                if (snapToGrid) {\n                    nextPosition = snapPosition(nextPosition, snapGrid);\n                }\n                // if there is selection with multiple nodes and a node extent is set, we need to adjust the node extent for each node\n                // based on its position so that the node stays at it's position relative to the selection.\n                let adjustedNodeExtent = [\n                    [nodeExtent[0][0], nodeExtent[0][1]],\n                    [nodeExtent[1][0], nodeExtent[1][1]],\n                ];\n                if (dragItems.size > 1 && nodeExtent && !dragItem.extent) {\n                    const { positionAbsolute } = dragItem.internals;\n                    const x1 = positionAbsolute.x - nodesBox.x + nodeExtent[0][0];\n                    const x2 = positionAbsolute.x + dragItem.measured.width - nodesBox.x2 + nodeExtent[1][0];\n                    const y1 = positionAbsolute.y - nodesBox.y + nodeExtent[0][1];\n                    const y2 = positionAbsolute.y + dragItem.measured.height - nodesBox.y2 + nodeExtent[1][1];\n                    adjustedNodeExtent = [\n                        [x1, y1],\n                        [x2, y2],\n                    ];\n                }\n                const { position, positionAbsolute } = calculateNodePosition({\n                    nodeId: id,\n                    nextPosition,\n                    nodeLookup,\n                    nodeExtent: adjustedNodeExtent,\n                    nodeOrigin,\n                    onError,\n                });\n                // we want to make sure that we only fire a change event when there is a change\n                hasChange = hasChange || dragItem.position.x !== position.x || dragItem.position.y !== position.y;\n                dragItem.position = position;\n                dragItem.internals.positionAbsolute = positionAbsolute;\n            }\n            if (!hasChange) {\n                return;\n            }\n            updateNodePositions(dragItems, true);\n            if (dragEvent && (onDrag || onNodeDrag || (!nodeId && onSelectionDrag))) {\n                const [currentNode, currentNodes] = getEventHandlerParams({\n                    nodeId,\n                    dragItems,\n                    nodeLookup,\n                });\n                onDrag?.(dragEvent, dragItems, currentNode, currentNodes);\n                onNodeDrag?.(dragEvent, currentNode, currentNodes);\n                if (!nodeId) {\n                    onSelectionDrag?.(dragEvent, currentNodes);\n                }\n            }\n        }\n        async function autoPan() {\n            if (!containerBounds) {\n                return;\n            }\n            const { transform, panBy, autoPanSpeed } = getStoreItems();\n            const [xMovement, yMovement] = calcAutoPan(mousePosition, containerBounds, autoPanSpeed);\n            if (xMovement !== 0 || yMovement !== 0) {\n                lastPos.x = (lastPos.x ?? 0) - xMovement / transform[2];\n                lastPos.y = (lastPos.y ?? 0) - yMovement / transform[2];\n                if (await panBy({ x: xMovement, y: yMovement })) {\n                    updateNodes(lastPos, null);\n                }\n            }\n            autoPanId = requestAnimationFrame(autoPan);\n        }\n        function startDrag(event) {\n            const { nodeLookup, multiSelectionActive, nodesDraggable, transform, snapGrid, snapToGrid, selectNodesOnDrag, onNodeDragStart, onSelectionDragStart, unselectNodesAndEdges, } = getStoreItems();\n            dragStarted = true;\n            if ((!selectNodesOnDrag || !isSelectable) && !multiSelectionActive && nodeId) {\n                if (!nodeLookup.get(nodeId)?.selected) {\n                    // we need to reset selected nodes when selectNodesOnDrag=false\n                    unselectNodesAndEdges();\n                }\n            }\n            if (isSelectable && selectNodesOnDrag && nodeId) {\n                onNodeMouseDown?.(nodeId);\n            }\n            const pointerPos = getPointerPosition(event.sourceEvent, { transform, snapGrid, snapToGrid, containerBounds });\n            lastPos = pointerPos;\n            dragItems = getDragItems(nodeLookup, nodesDraggable, pointerPos, nodeId);\n            if (dragItems.size > 0 && (onDragStart || onNodeDragStart || (!nodeId && onSelectionDragStart))) {\n                const [currentNode, currentNodes] = getEventHandlerParams({\n                    nodeId,\n                    dragItems,\n                    nodeLookup,\n                });\n                onDragStart?.(event.sourceEvent, dragItems, currentNode, currentNodes);\n                onNodeDragStart?.(event.sourceEvent, currentNode, currentNodes);\n                if (!nodeId) {\n                    onSelectionDragStart?.(event.sourceEvent, currentNodes);\n                }\n            }\n        }\n        const d3DragInstance = (0,d3_drag__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()\n            .clickDistance(nodeClickDistance)\n            .on('start', (event) => {\n            const { domNode, nodeDragThreshold, transform, snapGrid, snapToGrid } = getStoreItems();\n            containerBounds = domNode?.getBoundingClientRect() || null;\n            abortDrag = false;\n            if (nodeDragThreshold === 0) {\n                startDrag(event);\n            }\n            const pointerPos = getPointerPosition(event.sourceEvent, { transform, snapGrid, snapToGrid, containerBounds });\n            lastPos = pointerPos;\n            mousePosition = getEventPosition(event.sourceEvent, containerBounds);\n        })\n            .on('drag', (event) => {\n            const { autoPanOnNodeDrag, transform, snapGrid, snapToGrid, nodeDragThreshold, nodeLookup } = getStoreItems();\n            const pointerPos = getPointerPosition(event.sourceEvent, { transform, snapGrid, snapToGrid, containerBounds });\n            if ((event.sourceEvent.type === 'touchmove' && event.sourceEvent.touches.length > 1) ||\n                // if user deletes a node while dragging, we need to abort the drag to prevent errors\n                (nodeId && !nodeLookup.has(nodeId))) {\n                abortDrag = true;\n            }\n            if (abortDrag) {\n                return;\n            }\n            if (!autoPanStarted && autoPanOnNodeDrag && dragStarted) {\n                autoPanStarted = true;\n                autoPan();\n            }\n            if (!dragStarted) {\n                const x = pointerPos.xSnapped - (lastPos.x ?? 0);\n                const y = pointerPos.ySnapped - (lastPos.y ?? 0);\n                const distance = Math.sqrt(x * x + y * y);\n                if (distance > nodeDragThreshold) {\n                    startDrag(event);\n                }\n            }\n            // skip events without movement\n            if ((lastPos.x !== pointerPos.xSnapped || lastPos.y !== pointerPos.ySnapped) && dragItems && dragStarted) {\n                // dragEvent = event.sourceEvent as MouseEvent;\n                mousePosition = getEventPosition(event.sourceEvent, containerBounds);\n                updateNodes(pointerPos, event.sourceEvent);\n            }\n        })\n            .on('end', (event) => {\n            if (!dragStarted || abortDrag) {\n                return;\n            }\n            autoPanStarted = false;\n            dragStarted = false;\n            cancelAnimationFrame(autoPanId);\n            if (dragItems.size > 0) {\n                const { nodeLookup, updateNodePositions, onNodeDragStop, onSelectionDragStop } = getStoreItems();\n                updateNodePositions(dragItems, false);\n                if (onDragStop || onNodeDragStop || (!nodeId && onSelectionDragStop)) {\n                    const [currentNode, currentNodes] = getEventHandlerParams({\n                        nodeId,\n                        dragItems,\n                        nodeLookup,\n                        dragging: false,\n                    });\n                    onDragStop?.(event.sourceEvent, dragItems, currentNode, currentNodes);\n                    onNodeDragStop?.(event.sourceEvent, currentNode, currentNodes);\n                    if (!nodeId) {\n                        onSelectionDragStop?.(event.sourceEvent, currentNodes);\n                    }\n                }\n            }\n        })\n            .filter((event) => {\n            const target = event.target;\n            const isDraggable = !event.button &&\n                (!noDragClassName || !hasSelector(target, `.${noDragClassName}`, domNode)) &&\n                (!handleSelector || hasSelector(target, handleSelector, domNode));\n            return isDraggable;\n        });\n        d3Selection.call(d3DragInstance);\n    }\n    function destroy() {\n        d3Selection?.on('.drag', null);\n    }\n    return {\n        update,\n        destroy,\n    };\n}\n\nfunction getNodesWithinDistance(position, nodeLookup, distance) {\n    const nodes = [];\n    const rect = {\n        x: position.x - distance,\n        y: position.y - distance,\n        width: distance * 2,\n        height: distance * 2,\n    };\n    for (const node of nodeLookup.values()) {\n        if (getOverlappingArea(rect, nodeToRect(node)) > 0) {\n            nodes.push(node);\n        }\n    }\n    return nodes;\n}\n// this distance is used for the area around the user pointer\n// while doing a connection for finding the closest nodes\nconst ADDITIONAL_DISTANCE = 250;\nfunction getClosestHandle(position, connectionRadius, nodeLookup, fromHandle) {\n    let closestHandles = [];\n    let minDistance = Infinity;\n    const closeNodes = getNodesWithinDistance(position, nodeLookup, connectionRadius + ADDITIONAL_DISTANCE);\n    for (const node of closeNodes) {\n        const allHandles = [...(node.internals.handleBounds?.source ?? []), ...(node.internals.handleBounds?.target ?? [])];\n        for (const handle of allHandles) {\n            // if the handle is the same as the fromHandle we skip it\n            if (fromHandle.nodeId === handle.nodeId && fromHandle.type === handle.type && fromHandle.id === handle.id) {\n                continue;\n            }\n            // determine absolute position of the handle\n            const { x, y } = getHandlePosition(node, handle, handle.position, true);\n            const distance = Math.sqrt(Math.pow(x - position.x, 2) + Math.pow(y - position.y, 2));\n            if (distance > connectionRadius) {\n                continue;\n            }\n            if (distance < minDistance) {\n                closestHandles = [{ ...handle, x, y }];\n                minDistance = distance;\n            }\n            else if (distance === minDistance) {\n                // when multiple handles are on the same distance we collect all of them\n                closestHandles.push({ ...handle, x, y });\n            }\n        }\n    }\n    if (!closestHandles.length) {\n        return null;\n    }\n    // when multiple handles overlay each other we prefer the opposite handle\n    if (closestHandles.length > 1) {\n        const oppositeHandleType = fromHandle.type === 'source' ? 'target' : 'source';\n        return closestHandles.find((handle) => handle.type === oppositeHandleType) ?? closestHandles[0];\n    }\n    return closestHandles[0];\n}\nfunction getHandle(nodeId, handleType, handleId, nodeLookup, connectionMode, withAbsolutePosition = false) {\n    const node = nodeLookup.get(nodeId);\n    if (!node) {\n        return null;\n    }\n    const handles = connectionMode === 'strict'\n        ? node.internals.handleBounds?.[handleType]\n        : [...(node.internals.handleBounds?.source ?? []), ...(node.internals.handleBounds?.target ?? [])];\n    const handle = (handleId ? handles?.find((h) => h.id === handleId) : handles?.[0]) ?? null;\n    return handle && withAbsolutePosition\n        ? { ...handle, ...getHandlePosition(node, handle, handle.position, true) }\n        : handle;\n}\nfunction getHandleType(edgeUpdaterType, handleDomNode) {\n    if (edgeUpdaterType) {\n        return edgeUpdaterType;\n    }\n    else if (handleDomNode?.classList.contains('target')) {\n        return 'target';\n    }\n    else if (handleDomNode?.classList.contains('source')) {\n        return 'source';\n    }\n    return null;\n}\nfunction isConnectionValid(isInsideConnectionRadius, isHandleValid) {\n    let isValid = null;\n    if (isHandleValid) {\n        isValid = true;\n    }\n    else if (isInsideConnectionRadius && !isHandleValid) {\n        isValid = false;\n    }\n    return isValid;\n}\n\nconst alwaysValid = () => true;\nfunction onPointerDown(event, { connectionMode, connectionRadius, handleId, nodeId, edgeUpdaterType, isTarget, domNode, nodeLookup, lib, autoPanOnConnect, flowId, panBy, cancelConnection, onConnectStart, onConnect, onConnectEnd, isValidConnection = alwaysValid, onReconnectEnd, updateConnection, getTransform, getFromHandle, autoPanSpeed, }) {\n    // when xyflow is used inside a shadow root we can't use document\n    const doc = getHostForElement(event.target);\n    let autoPanId = 0;\n    let closestHandle;\n    const { x, y } = getEventPosition(event);\n    const clickedHandle = doc?.elementFromPoint(x, y);\n    const handleType = getHandleType(edgeUpdaterType, clickedHandle);\n    const containerBounds = domNode?.getBoundingClientRect();\n    if (!containerBounds || !handleType) {\n        return;\n    }\n    const fromHandleInternal = getHandle(nodeId, handleType, handleId, nodeLookup, connectionMode);\n    if (!fromHandleInternal) {\n        return;\n    }\n    let position = getEventPosition(event, containerBounds);\n    let autoPanStarted = false;\n    let connection = null;\n    let isValid = false;\n    let handleDomNode = null;\n    // when the user is moving the mouse close to the edge of the canvas while connecting we move the canvas\n    function autoPan() {\n        if (!autoPanOnConnect || !containerBounds) {\n            return;\n        }\n        const [x, y] = calcAutoPan(position, containerBounds, autoPanSpeed);\n        panBy({ x, y });\n        autoPanId = requestAnimationFrame(autoPan);\n    }\n    // Stays the same for all consecutive pointermove events\n    const fromHandle = {\n        ...fromHandleInternal,\n        nodeId,\n        type: handleType,\n        position: fromHandleInternal.position,\n    };\n    const fromNodeInternal = nodeLookup.get(nodeId);\n    const from = getHandlePosition(fromNodeInternal, fromHandle, Position.Left, true);\n    const newConnection = {\n        inProgress: true,\n        isValid: null,\n        from,\n        fromHandle,\n        fromPosition: fromHandle.position,\n        fromNode: fromNodeInternal,\n        to: position,\n        toHandle: null,\n        toPosition: oppositePosition[fromHandle.position],\n        toNode: null,\n    };\n    updateConnection(newConnection);\n    let previousConnection = newConnection;\n    onConnectStart?.(event, { nodeId, handleId, handleType });\n    function onPointerMove(event) {\n        if (!getFromHandle() || !fromHandle) {\n            onPointerUp(event);\n            return;\n        }\n        const transform = getTransform();\n        position = getEventPosition(event, containerBounds);\n        closestHandle = getClosestHandle(pointToRendererPoint(position, transform, false, [1, 1]), connectionRadius, nodeLookup, fromHandle);\n        if (!autoPanStarted) {\n            autoPan();\n            autoPanStarted = true;\n        }\n        const result = isValidHandle(event, {\n            handle: closestHandle,\n            connectionMode,\n            fromNodeId: nodeId,\n            fromHandleId: handleId,\n            fromType: isTarget ? 'target' : 'source',\n            isValidConnection,\n            doc,\n            lib,\n            flowId,\n            nodeLookup,\n        });\n        handleDomNode = result.handleDomNode;\n        connection = result.connection;\n        isValid = isConnectionValid(!!closestHandle, result.isValid);\n        const newConnection = {\n            // from stays the same\n            ...previousConnection,\n            isValid,\n            to: closestHandle && isValid\n                ? rendererPointToPoint({ x: closestHandle.x, y: closestHandle.y }, transform)\n                : position,\n            toHandle: result.toHandle,\n            toPosition: isValid && result.toHandle ? result.toHandle.position : oppositePosition[fromHandle.position],\n            toNode: result.toHandle ? nodeLookup.get(result.toHandle.nodeId) : null,\n        };\n        // we don't want to trigger an update when the connection\n        // is snapped to the same handle as before\n        if (isValid &&\n            closestHandle &&\n            previousConnection.toHandle &&\n            newConnection.toHandle &&\n            previousConnection.toHandle.type === newConnection.toHandle.type &&\n            previousConnection.toHandle.nodeId === newConnection.toHandle.nodeId &&\n            previousConnection.toHandle.id === newConnection.toHandle.id &&\n            previousConnection.to.x === newConnection.to.x &&\n            previousConnection.to.y === newConnection.to.y) {\n            return;\n        }\n        updateConnection(newConnection);\n        previousConnection = newConnection;\n    }\n    function onPointerUp(event) {\n        if ((closestHandle || handleDomNode) && connection && isValid) {\n            onConnect?.(connection);\n        }\n        // it's important to get a fresh reference from the store here\n        // in order to get the latest state of onConnectEnd\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const { inProgress, ...connectionState } = previousConnection;\n        const finalConnectionState = {\n            ...connectionState,\n            toPosition: previousConnection.toHandle ? previousConnection.toPosition : null,\n        };\n        onConnectEnd?.(event, finalConnectionState);\n        if (edgeUpdaterType) {\n            onReconnectEnd?.(event, finalConnectionState);\n        }\n        cancelConnection();\n        cancelAnimationFrame(autoPanId);\n        autoPanStarted = false;\n        isValid = false;\n        connection = null;\n        handleDomNode = null;\n        doc.removeEventListener('mousemove', onPointerMove);\n        doc.removeEventListener('mouseup', onPointerUp);\n        doc.removeEventListener('touchmove', onPointerMove);\n        doc.removeEventListener('touchend', onPointerUp);\n    }\n    doc.addEventListener('mousemove', onPointerMove);\n    doc.addEventListener('mouseup', onPointerUp);\n    doc.addEventListener('touchmove', onPointerMove);\n    doc.addEventListener('touchend', onPointerUp);\n}\n// checks if  and returns connection in fom of an object { source: 123, target: 312 }\nfunction isValidHandle(event, { handle, connectionMode, fromNodeId, fromHandleId, fromType, doc, lib, flowId, isValidConnection = alwaysValid, nodeLookup, }) {\n    const isTarget = fromType === 'target';\n    const handleDomNode = handle\n        ? doc.querySelector(`.${lib}-flow__handle[data-id=\"${flowId}-${handle?.nodeId}-${handle?.id}-${handle?.type}\"]`)\n        : null;\n    const { x, y } = getEventPosition(event);\n    const handleBelow = doc.elementFromPoint(x, y);\n    // we always want to prioritize the handle below the mouse cursor over the closest distance handle,\n    // because it could be that the center of another handle is closer to the mouse pointer than the handle below the cursor\n    const handleToCheck = handleBelow?.classList.contains(`${lib}-flow__handle`) ? handleBelow : handleDomNode;\n    const result = {\n        handleDomNode: handleToCheck,\n        isValid: false,\n        connection: null,\n        toHandle: null,\n    };\n    if (handleToCheck) {\n        const handleType = getHandleType(undefined, handleToCheck);\n        const handleNodeId = handleToCheck.getAttribute('data-nodeid');\n        const handleId = handleToCheck.getAttribute('data-handleid');\n        const connectable = handleToCheck.classList.contains('connectable');\n        const connectableEnd = handleToCheck.classList.contains('connectableend');\n        if (!handleNodeId || !handleType) {\n            return result;\n        }\n        const connection = {\n            source: isTarget ? handleNodeId : fromNodeId,\n            sourceHandle: isTarget ? handleId : fromHandleId,\n            target: isTarget ? fromNodeId : handleNodeId,\n            targetHandle: isTarget ? fromHandleId : handleId,\n        };\n        result.connection = connection;\n        const isConnectable = connectable && connectableEnd;\n        // in strict mode we don't allow target to target or source to source connections\n        const isValid = isConnectable &&\n            (connectionMode === ConnectionMode.Strict\n                ? (isTarget && handleType === 'source') || (!isTarget && handleType === 'target')\n                : handleNodeId !== fromNodeId || handleId !== fromHandleId);\n        result.isValid = isValid && isValidConnection(connection);\n        result.toHandle = getHandle(handleNodeId, handleType, handleId, nodeLookup, connectionMode, false);\n    }\n    return result;\n}\nconst XYHandle = {\n    onPointerDown,\n    isValid: isValidHandle,\n};\n\nfunction XYMinimap({ domNode, panZoom, getTransform, getViewScale }) {\n    const selection = (0,d3_selection__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domNode);\n    function update({ translateExtent, width, height, zoomStep = 10, pannable = true, zoomable = true, inversePan = false, }) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const zoomHandler = (event) => {\n            const transform = getTransform();\n            if (event.sourceEvent.type !== 'wheel' || !panZoom) {\n                return;\n            }\n            const pinchDelta = -event.sourceEvent.deltaY *\n                (event.sourceEvent.deltaMode === 1 ? 0.05 : event.sourceEvent.deltaMode ? 1 : 0.002) *\n                zoomStep;\n            const nextZoom = transform[2] * Math.pow(2, pinchDelta);\n            panZoom.scaleTo(nextZoom);\n        };\n        let panStart = [0, 0];\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const panStartHandler = (event) => {\n            if (event.sourceEvent.type === 'mousedown' || event.sourceEvent.type === 'touchstart') {\n                panStart = [\n                    event.sourceEvent.clientX ?? event.sourceEvent.touches[0].clientX,\n                    event.sourceEvent.clientY ?? event.sourceEvent.touches[0].clientY,\n                ];\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const panHandler = (event) => {\n            const transform = getTransform();\n            if ((event.sourceEvent.type !== 'mousemove' && event.sourceEvent.type !== 'touchmove') || !panZoom) {\n                return;\n            }\n            const panCurrent = [\n                event.sourceEvent.clientX ?? event.sourceEvent.touches[0].clientX,\n                event.sourceEvent.clientY ?? event.sourceEvent.touches[0].clientY,\n            ];\n            const panDelta = [panCurrent[0] - panStart[0], panCurrent[1] - panStart[1]];\n            panStart = panCurrent;\n            const moveScale = getViewScale() * Math.max(transform[2], Math.log(transform[2])) * (inversePan ? -1 : 1);\n            const position = {\n                x: transform[0] - panDelta[0] * moveScale,\n                y: transform[1] - panDelta[1] * moveScale,\n            };\n            const extent = [\n                [0, 0],\n                [width, height],\n            ];\n            panZoom.setViewportConstrained({\n                x: position.x,\n                y: position.y,\n                zoom: transform[2],\n            }, extent, translateExtent);\n        };\n        const zoomAndPanHandler = (0,d3_zoom__WEBPACK_IMPORTED_MODULE_0__.zoom)()\n            .on('start', panStartHandler)\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            .on('zoom', pannable ? panHandler : null)\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            .on('zoom.wheel', zoomable ? zoomHandler : null);\n        selection.call(zoomAndPanHandler, {});\n    }\n    function destroy() {\n        selection.on('zoom', null);\n    }\n    return {\n        update,\n        destroy,\n        pointer: d3_selection__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    };\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst viewChanged = (prevViewport, eventViewport) => prevViewport.x !== eventViewport.x || prevViewport.y !== eventViewport.y || prevViewport.zoom !== eventViewport.k;\nconst transformToViewport = (transform) => ({\n    x: transform.x,\n    y: transform.y,\n    zoom: transform.k,\n});\nconst viewportToTransform = ({ x, y, zoom }) => d3_zoom__WEBPACK_IMPORTED_MODULE_0__.zoomIdentity.translate(x, y).scale(zoom);\nconst isWrappedWithClass = (event, className) => event.target.closest(`.${className}`);\nconst isRightClickPan = (panOnDrag, usedButton) => usedButton === 2 && Array.isArray(panOnDrag) && panOnDrag.includes(2);\nconst getD3Transition = (selection, duration = 0, onEnd = () => { }) => {\n    const hasDuration = typeof duration === 'number' && duration > 0;\n    if (!hasDuration) {\n        onEnd();\n    }\n    return hasDuration ? selection.transition().duration(duration).on('end', onEnd) : selection;\n};\nconst wheelDelta = (event) => {\n    const factor = event.ctrlKey && isMacOs() ? 10 : 1;\n    return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * factor;\n};\n\nfunction createPanOnScrollHandler({ zoomPanValues, noWheelClassName, d3Selection, d3Zoom, panOnScrollMode, panOnScrollSpeed, zoomOnPinch, onPanZoomStart, onPanZoom, onPanZoomEnd, }) {\n    return (event) => {\n        if (isWrappedWithClass(event, noWheelClassName)) {\n            return false;\n        }\n        event.preventDefault();\n        event.stopImmediatePropagation();\n        const currentZoom = d3Selection.property('__zoom').k || 1;\n        // macos sets ctrlKey=true for pinch gesture on a trackpad\n        if (event.ctrlKey && zoomOnPinch) {\n            const point = (0,d3_selection__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(event);\n            const pinchDelta = wheelDelta(event);\n            const zoom = currentZoom * Math.pow(2, pinchDelta);\n            // @ts-ignore\n            d3Zoom.scaleTo(d3Selection, zoom, point, event);\n            return;\n        }\n        // increase scroll speed in firefox\n        // firefox: deltaMode === 1; chrome: deltaMode === 0\n        const deltaNormalize = event.deltaMode === 1 ? 20 : 1;\n        let deltaX = panOnScrollMode === PanOnScrollMode.Vertical ? 0 : event.deltaX * deltaNormalize;\n        let deltaY = panOnScrollMode === PanOnScrollMode.Horizontal ? 0 : event.deltaY * deltaNormalize;\n        // this enables vertical scrolling with shift + scroll on windows\n        if (!isMacOs() && event.shiftKey && panOnScrollMode !== PanOnScrollMode.Vertical) {\n            deltaX = event.deltaY * deltaNormalize;\n            deltaY = 0;\n        }\n        d3Zoom.translateBy(d3Selection, -(deltaX / currentZoom) * panOnScrollSpeed, -(deltaY / currentZoom) * panOnScrollSpeed, \n        // @ts-ignore\n        { internal: true });\n        const nextViewport = transformToViewport(d3Selection.property('__zoom'));\n        clearTimeout(zoomPanValues.panScrollTimeout);\n        // for pan on scroll we need to handle the event calls on our own\n        // we can't use the start, zoom and end events from d3-zoom\n        // because start and move gets called on every scroll event and not once at the beginning\n        if (!zoomPanValues.isPanScrolling) {\n            zoomPanValues.isPanScrolling = true;\n            onPanZoomStart?.(event, nextViewport);\n        }\n        if (zoomPanValues.isPanScrolling) {\n            onPanZoom?.(event, nextViewport);\n            zoomPanValues.panScrollTimeout = setTimeout(() => {\n                onPanZoomEnd?.(event, nextViewport);\n                zoomPanValues.isPanScrolling = false;\n            }, 150);\n        }\n    };\n}\nfunction createZoomOnScrollHandler({ noWheelClassName, preventScrolling, d3ZoomHandler }) {\n    return function (event, d) {\n        // we still want to enable pinch zooming even if preventScrolling is set to false\n        const preventZoom = !preventScrolling && event.type === 'wheel' && !event.ctrlKey;\n        if (preventZoom || isWrappedWithClass(event, noWheelClassName)) {\n            return null;\n        }\n        event.preventDefault();\n        d3ZoomHandler.call(this, event, d);\n    };\n}\nfunction createPanZoomStartHandler({ zoomPanValues, onDraggingChange, onPanZoomStart }) {\n    return (event) => {\n        if (event.sourceEvent?.internal) {\n            return;\n        }\n        const viewport = transformToViewport(event.transform);\n        // we need to remember it here, because it's always 0 in the \"zoom\" event\n        zoomPanValues.mouseButton = event.sourceEvent?.button || 0;\n        zoomPanValues.isZoomingOrPanning = true;\n        zoomPanValues.prevViewport = viewport;\n        if (event.sourceEvent?.type === 'mousedown') {\n            onDraggingChange(true);\n        }\n        if (onPanZoomStart) {\n            onPanZoomStart?.(event.sourceEvent, viewport);\n        }\n    };\n}\nfunction createPanZoomHandler({ zoomPanValues, panOnDrag, onPaneContextMenu, onTransformChange, onPanZoom, }) {\n    return (event) => {\n        zoomPanValues.usedRightMouseButton = !!(onPaneContextMenu && isRightClickPan(panOnDrag, zoomPanValues.mouseButton ?? 0));\n        if (!event.sourceEvent?.sync) {\n            onTransformChange([event.transform.x, event.transform.y, event.transform.k]);\n        }\n        if (onPanZoom && !event.sourceEvent?.internal) {\n            onPanZoom?.(event.sourceEvent, transformToViewport(event.transform));\n        }\n    };\n}\nfunction createPanZoomEndHandler({ zoomPanValues, panOnDrag, panOnScroll, onDraggingChange, onPanZoomEnd, onPaneContextMenu, }) {\n    return (event) => {\n        if (event.sourceEvent?.internal) {\n            return;\n        }\n        zoomPanValues.isZoomingOrPanning = false;\n        if (onPaneContextMenu &&\n            isRightClickPan(panOnDrag, zoomPanValues.mouseButton ?? 0) &&\n            !zoomPanValues.usedRightMouseButton &&\n            event.sourceEvent) {\n            onPaneContextMenu(event.sourceEvent);\n        }\n        zoomPanValues.usedRightMouseButton = false;\n        onDraggingChange(false);\n        if (onPanZoomEnd && viewChanged(zoomPanValues.prevViewport, event.transform)) {\n            const viewport = transformToViewport(event.transform);\n            zoomPanValues.prevViewport = viewport;\n            clearTimeout(zoomPanValues.timerId);\n            zoomPanValues.timerId = setTimeout(() => {\n                onPanZoomEnd?.(event.sourceEvent, viewport);\n            }, \n            // we need a setTimeout for panOnScroll to supress multiple end events fired during scroll\n            panOnScroll ? 150 : 0);\n        }\n    };\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction createFilter({ zoomActivationKeyPressed, zoomOnScroll, zoomOnPinch, panOnDrag, panOnScroll, zoomOnDoubleClick, userSelectionActive, noWheelClassName, noPanClassName, lib, }) {\n    return (event) => {\n        const zoomScroll = zoomActivationKeyPressed || zoomOnScroll;\n        const pinchZoom = zoomOnPinch && event.ctrlKey;\n        if (event.button === 1 &&\n            event.type === 'mousedown' &&\n            (isWrappedWithClass(event, `${lib}-flow__node`) || isWrappedWithClass(event, `${lib}-flow__edge`))) {\n            return true;\n        }\n        // if all interactions are disabled, we prevent all zoom events\n        if (!panOnDrag && !zoomScroll && !panOnScroll && !zoomOnDoubleClick && !zoomOnPinch) {\n            return false;\n        }\n        // during a selection we prevent all other interactions\n        if (userSelectionActive) {\n            return false;\n        }\n        // if the target element is inside an element with the nowheel class, we prevent zooming\n        if (isWrappedWithClass(event, noWheelClassName) && event.type === 'wheel') {\n            return false;\n        }\n        // if the target element is inside an element with the nopan class, we prevent panning\n        if (isWrappedWithClass(event, noPanClassName) &&\n            (event.type !== 'wheel' || (panOnScroll && event.type === 'wheel' && !zoomActivationKeyPressed))) {\n            return false;\n        }\n        if (!zoomOnPinch && event.ctrlKey && event.type === 'wheel') {\n            return false;\n        }\n        if (!zoomOnPinch && event.type === 'touchstart' && event.touches?.length > 1) {\n            event.preventDefault(); // if you manage to start with 2 touches, we prevent native zoom\n            return false;\n        }\n        // when there is no scroll handling enabled, we prevent all wheel events\n        if (!zoomScroll && !panOnScroll && !pinchZoom && event.type === 'wheel') {\n            return false;\n        }\n        // if the pane is not movable, we prevent dragging it with mousestart or touchstart\n        if (!panOnDrag && (event.type === 'mousedown' || event.type === 'touchstart')) {\n            return false;\n        }\n        // if the pane is only movable using allowed clicks\n        if (Array.isArray(panOnDrag) && !panOnDrag.includes(event.button) && event.type === 'mousedown') {\n            return false;\n        }\n        // We only allow right clicks if pan on drag is set to right click\n        const buttonAllowed = (Array.isArray(panOnDrag) && panOnDrag.includes(event.button)) || !event.button || event.button <= 1;\n        // default filter for d3-zoom\n        return (!event.ctrlKey || event.type === 'wheel') && buttonAllowed;\n    };\n}\n\nfunction XYPanZoom({ domNode, minZoom, maxZoom, paneClickDistance, translateExtent, viewport, onPanZoom, onPanZoomStart, onPanZoomEnd, onDraggingChange, }) {\n    const zoomPanValues = {\n        isZoomingOrPanning: false,\n        usedRightMouseButton: false,\n        prevViewport: { x: 0, y: 0, zoom: 0 },\n        mouseButton: 0,\n        timerId: undefined,\n        panScrollTimeout: undefined,\n        isPanScrolling: false,\n    };\n    const bbox = domNode.getBoundingClientRect();\n    const d3ZoomInstance = (0,d3_zoom__WEBPACK_IMPORTED_MODULE_0__.zoom)()\n        .clickDistance(!isNumeric(paneClickDistance) || paneClickDistance < 0 ? 0 : paneClickDistance)\n        .scaleExtent([minZoom, maxZoom])\n        .translateExtent(translateExtent);\n    const d3Selection = (0,d3_selection__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domNode).call(d3ZoomInstance);\n    setViewportConstrained({\n        x: viewport.x,\n        y: viewport.y,\n        zoom: clamp(viewport.zoom, minZoom, maxZoom),\n    }, [\n        [0, 0],\n        [bbox.width, bbox.height],\n    ], translateExtent);\n    const d3ZoomHandler = d3Selection.on('wheel.zoom');\n    const d3DblClickZoomHandler = d3Selection.on('dblclick.zoom');\n    d3ZoomInstance.wheelDelta(wheelDelta);\n    function setTransform(transform, options) {\n        if (d3Selection) {\n            return new Promise((resolve) => {\n                d3ZoomInstance?.transform(getD3Transition(d3Selection, options?.duration, () => resolve(true)), transform);\n            });\n        }\n        return Promise.resolve(false);\n    }\n    // public functions\n    function update({ noWheelClassName, noPanClassName, onPaneContextMenu, userSelectionActive, panOnScroll, panOnDrag, panOnScrollMode, panOnScrollSpeed, preventScrolling, zoomOnPinch, zoomOnScroll, zoomOnDoubleClick, zoomActivationKeyPressed, lib, onTransformChange, }) {\n        if (userSelectionActive && !zoomPanValues.isZoomingOrPanning) {\n            destroy();\n        }\n        const isPanOnScroll = panOnScroll && !zoomActivationKeyPressed && !userSelectionActive;\n        const wheelHandler = isPanOnScroll\n            ? createPanOnScrollHandler({\n                zoomPanValues,\n                noWheelClassName,\n                d3Selection,\n                d3Zoom: d3ZoomInstance,\n                panOnScrollMode,\n                panOnScrollSpeed,\n                zoomOnPinch,\n                onPanZoomStart,\n                onPanZoom,\n                onPanZoomEnd,\n            })\n            : createZoomOnScrollHandler({\n                noWheelClassName,\n                preventScrolling,\n                d3ZoomHandler,\n            });\n        d3Selection.on('wheel.zoom', wheelHandler, { passive: false });\n        if (!userSelectionActive) {\n            // pan zoom start\n            const startHandler = createPanZoomStartHandler({\n                zoomPanValues,\n                onDraggingChange,\n                onPanZoomStart,\n            });\n            d3ZoomInstance.on('start', startHandler);\n            // pan zoom\n            const panZoomHandler = createPanZoomHandler({\n                zoomPanValues,\n                panOnDrag,\n                onPaneContextMenu: !!onPaneContextMenu,\n                onPanZoom,\n                onTransformChange,\n            });\n            d3ZoomInstance.on('zoom', panZoomHandler);\n            // pan zoom end\n            const panZoomEndHandler = createPanZoomEndHandler({\n                zoomPanValues,\n                panOnDrag,\n                panOnScroll,\n                onPaneContextMenu,\n                onPanZoomEnd,\n                onDraggingChange,\n            });\n            d3ZoomInstance.on('end', panZoomEndHandler);\n        }\n        const filter = createFilter({\n            zoomActivationKeyPressed,\n            panOnDrag,\n            zoomOnScroll,\n            panOnScroll,\n            zoomOnDoubleClick,\n            zoomOnPinch,\n            userSelectionActive,\n            noPanClassName,\n            noWheelClassName,\n            lib,\n        });\n        d3ZoomInstance.filter(filter);\n        // We cannot add zoomOnDoubleClick to the filter above because\n        // double tapping on touch screens circumvents the filter and\n        // dblclick.zoom is fired on the selection directly\n        if (zoomOnDoubleClick) {\n            d3Selection.on('dblclick.zoom', d3DblClickZoomHandler);\n        }\n        else {\n            d3Selection.on('dblclick.zoom', null);\n        }\n    }\n    function destroy() {\n        d3ZoomInstance.on('zoom', null);\n    }\n    async function setViewportConstrained(viewport, extent, translateExtent) {\n        const nextTransform = viewportToTransform(viewport);\n        const contrainedTransform = d3ZoomInstance?.constrain()(nextTransform, extent, translateExtent);\n        if (contrainedTransform) {\n            await setTransform(contrainedTransform);\n        }\n        return new Promise((resolve) => resolve(contrainedTransform));\n    }\n    async function setViewport(viewport, options) {\n        const nextTransform = viewportToTransform(viewport);\n        await setTransform(nextTransform, options);\n        return new Promise((resolve) => resolve(nextTransform));\n    }\n    function syncViewport(viewport) {\n        if (d3Selection) {\n            const nextTransform = viewportToTransform(viewport);\n            const currentTransform = d3Selection.property('__zoom');\n            if (currentTransform.k !== viewport.zoom ||\n                currentTransform.x !== viewport.x ||\n                currentTransform.y !== viewport.y) {\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-ignore\n                d3ZoomInstance?.transform(d3Selection, nextTransform, null, { sync: true });\n            }\n        }\n    }\n    function getViewport() {\n        const transform = d3Selection ? (0,d3_zoom__WEBPACK_IMPORTED_MODULE_0__.zoomTransform)(d3Selection.node()) : { x: 0, y: 0, k: 1 };\n        return { x: transform.x, y: transform.y, zoom: transform.k };\n    }\n    function scaleTo(zoom, options) {\n        if (d3Selection) {\n            return new Promise((resolve) => {\n                d3ZoomInstance?.scaleTo(getD3Transition(d3Selection, options?.duration, () => resolve(true)), zoom);\n            });\n        }\n        return Promise.resolve(false);\n    }\n    function scaleBy(factor, options) {\n        if (d3Selection) {\n            return new Promise((resolve) => {\n                d3ZoomInstance?.scaleBy(getD3Transition(d3Selection, options?.duration, () => resolve(true)), factor);\n            });\n        }\n        return Promise.resolve(false);\n    }\n    function setScaleExtent(scaleExtent) {\n        d3ZoomInstance?.scaleExtent(scaleExtent);\n    }\n    function setTranslateExtent(translateExtent) {\n        d3ZoomInstance?.translateExtent(translateExtent);\n    }\n    function setClickDistance(distance) {\n        const validDistance = !isNumeric(distance) || distance < 0 ? 0 : distance;\n        d3ZoomInstance?.clickDistance(validDistance);\n    }\n    return {\n        update,\n        destroy,\n        setViewport,\n        setViewportConstrained,\n        getViewport,\n        scaleTo,\n        scaleBy,\n        setScaleExtent,\n        setTranslateExtent,\n        syncViewport,\n        setClickDistance,\n    };\n}\n\nvar ResizeControlVariant;\n(function (ResizeControlVariant) {\n    ResizeControlVariant[\"Line\"] = \"line\";\n    ResizeControlVariant[\"Handle\"] = \"handle\";\n})(ResizeControlVariant || (ResizeControlVariant = {}));\nconst XY_RESIZER_HANDLE_POSITIONS = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];\nconst XY_RESIZER_LINE_POSITIONS = ['top', 'right', 'bottom', 'left'];\n\n/**\n * Get all connecting edges for a given set of nodes\n * @param width - new width of the node\n * @param prevWidth - previous width of the node\n * @param height - new height of the node\n * @param prevHeight - previous height of the node\n * @param affectsX - whether to invert the resize direction for the x axis\n * @param affectsY - whether to invert the resize direction for the y axis\n * @returns array of two numbers representing the direction of the resize for each axis, 0 = no change, 1 = increase, -1 = decrease\n */\nfunction getResizeDirection({ width, prevWidth, height, prevHeight, affectsX, affectsY, }) {\n    const deltaWidth = width - prevWidth;\n    const deltaHeight = height - prevHeight;\n    const direction = [deltaWidth > 0 ? 1 : deltaWidth < 0 ? -1 : 0, deltaHeight > 0 ? 1 : deltaHeight < 0 ? -1 : 0];\n    if (deltaWidth && affectsX) {\n        direction[0] = direction[0] * -1;\n    }\n    if (deltaHeight && affectsY) {\n        direction[1] = direction[1] * -1;\n    }\n    return direction;\n}\n/**\n * Parses the control position that is being dragged to dimensions that are being resized\n * @param controlPosition - position of the control that is being dragged\n * @returns isHorizontal, isVertical, affectsX, affectsY,\n */\nfunction getControlDirection(controlPosition) {\n    const isHorizontal = controlPosition.includes('right') || controlPosition.includes('left');\n    const isVertical = controlPosition.includes('bottom') || controlPosition.includes('top');\n    const affectsX = controlPosition.includes('left');\n    const affectsY = controlPosition.includes('top');\n    return {\n        isHorizontal,\n        isVertical,\n        affectsX,\n        affectsY,\n    };\n}\nfunction getLowerExtentClamp(lowerExtent, lowerBound) {\n    return Math.max(0, lowerBound - lowerExtent);\n}\nfunction getUpperExtentClamp(upperExtent, upperBound) {\n    return Math.max(0, upperExtent - upperBound);\n}\nfunction getSizeClamp(size, minSize, maxSize) {\n    return Math.max(0, minSize - size, size - maxSize);\n}\nfunction xor(a, b) {\n    return a ? !b : b;\n}\n/**\n * Calculates new width & height and x & y of node after resize based on pointer position\n * @description - Buckle up, this is a chunky one... If you want to determine the new dimensions of a node after a resize,\n * you have to account for all possible restrictions: min/max width/height of the node, the maximum extent the node is allowed\n * to move in (in this case: resize into) determined by the parent node, the minimal extent determined by child nodes\n * with expandParent or extent: 'parent' set and oh yeah, these things also have to work with keepAspectRatio!\n * The way this is done is by determining how much each of these restricting actually restricts the resize and then applying the\n * strongest restriction. Because the resize affects x, y and width, height and width, height of a opposing side with keepAspectRatio,\n * the resize amount is always kept in distX & distY amount (the distance in mouse movement)\n * Instead of clamping each value, we first calculate the biggest 'clamp' (for the lack of a better name) and then apply it to all values.\n * To complicate things nodeOrigin has to be taken into account as well. This is done by offsetting the nodes as if their origin is [0, 0],\n * then calculating the restrictions as usual\n * @param startValues - starting values of resize\n * @param controlDirection - dimensions affected by the resize\n * @param pointerPosition - the current pointer position corrected for snapping\n * @param boundaries - minimum and maximum dimensions of the node\n * @param keepAspectRatio - prevent changes of asprect ratio\n * @returns x, y, width and height of the node after resize\n */\nfunction getDimensionsAfterResize(startValues, controlDirection, pointerPosition, boundaries, keepAspectRatio, nodeOrigin, extent, childExtent) {\n    let { affectsX, affectsY } = controlDirection;\n    const { isHorizontal, isVertical } = controlDirection;\n    const isDiagonal = isHorizontal && isVertical;\n    const { xSnapped, ySnapped } = pointerPosition;\n    const { minWidth, maxWidth, minHeight, maxHeight } = boundaries;\n    const { x: startX, y: startY, width: startWidth, height: startHeight, aspectRatio } = startValues;\n    let distX = Math.floor(isHorizontal ? xSnapped - startValues.pointerX : 0);\n    let distY = Math.floor(isVertical ? ySnapped - startValues.pointerY : 0);\n    const newWidth = startWidth + (affectsX ? -distX : distX);\n    const newHeight = startHeight + (affectsY ? -distY : distY);\n    const originOffsetX = -nodeOrigin[0] * startWidth;\n    const originOffsetY = -nodeOrigin[1] * startHeight;\n    // Check if maxWidth, minWWidth, maxHeight, minHeight are restricting the resize\n    let clampX = getSizeClamp(newWidth, minWidth, maxWidth);\n    let clampY = getSizeClamp(newHeight, minHeight, maxHeight);\n    // Check if extent is restricting the resize\n    if (extent) {\n        let xExtentClamp = 0;\n        let yExtentClamp = 0;\n        if (affectsX && distX < 0) {\n            xExtentClamp = getLowerExtentClamp(startX + distX + originOffsetX, extent[0][0]);\n        }\n        else if (!affectsX && distX > 0) {\n            xExtentClamp = getUpperExtentClamp(startX + newWidth + originOffsetX, extent[1][0]);\n        }\n        if (affectsY && distY < 0) {\n            yExtentClamp = getLowerExtentClamp(startY + distY + originOffsetY, extent[0][1]);\n        }\n        else if (!affectsY && distY > 0) {\n            yExtentClamp = getUpperExtentClamp(startY + newHeight + originOffsetY, extent[1][1]);\n        }\n        clampX = Math.max(clampX, xExtentClamp);\n        clampY = Math.max(clampY, yExtentClamp);\n    }\n    // Check if the child extent is restricting the resize\n    if (childExtent) {\n        let xExtentClamp = 0;\n        let yExtentClamp = 0;\n        if (affectsX && distX > 0) {\n            xExtentClamp = getUpperExtentClamp(startX + distX, childExtent[0][0]);\n        }\n        else if (!affectsX && distX < 0) {\n            xExtentClamp = getLowerExtentClamp(startX + newWidth, childExtent[1][0]);\n        }\n        if (affectsY && distY > 0) {\n            yExtentClamp = getUpperExtentClamp(startY + distY, childExtent[0][1]);\n        }\n        else if (!affectsY && distY < 0) {\n            yExtentClamp = getLowerExtentClamp(startY + newHeight, childExtent[1][1]);\n        }\n        clampX = Math.max(clampX, xExtentClamp);\n        clampY = Math.max(clampY, yExtentClamp);\n    }\n    // Check if the aspect ratio resizing of the other side is restricting the resize\n    if (keepAspectRatio) {\n        if (isHorizontal) {\n            // Check if the max dimensions might be restricting the resize\n            const aspectHeightClamp = getSizeClamp(newWidth / aspectRatio, minHeight, maxHeight) * aspectRatio;\n            clampX = Math.max(clampX, aspectHeightClamp);\n            // Check if the extent is restricting the resize\n            if (extent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsX && !affectsY && isDiagonal)) {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startY + originOffsetY + newWidth / aspectRatio, extent[1][1]) * aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getLowerExtentClamp(startY + originOffsetY + (affectsX ? distX : -distX) / aspectRatio, extent[0][1]) *\n                            aspectRatio;\n                }\n                clampX = Math.max(clampX, aspectExtentClamp);\n            }\n            // Check if the child extent is restricting the resize\n            if (childExtent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsX && !affectsY && isDiagonal)) {\n                    aspectExtentClamp = getLowerExtentClamp(startY + newWidth / aspectRatio, childExtent[1][1]) * aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startY + (affectsX ? distX : -distX) / aspectRatio, childExtent[0][1]) * aspectRatio;\n                }\n                clampX = Math.max(clampX, aspectExtentClamp);\n            }\n        }\n        // Do the same thing for vertical resizing\n        if (isVertical) {\n            const aspectWidthClamp = getSizeClamp(newHeight * aspectRatio, minWidth, maxWidth) / aspectRatio;\n            clampY = Math.max(clampY, aspectWidthClamp);\n            if (extent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsY && !affectsX && isDiagonal)) {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startX + newHeight * aspectRatio + originOffsetX, extent[1][0]) / aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getLowerExtentClamp(startX + (affectsY ? distY : -distY) * aspectRatio + originOffsetX, extent[0][0]) /\n                            aspectRatio;\n                }\n                clampY = Math.max(clampY, aspectExtentClamp);\n            }\n            if (childExtent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsY && !affectsX && isDiagonal)) {\n                    aspectExtentClamp = getLowerExtentClamp(startX + newHeight * aspectRatio, childExtent[1][0]) / aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startX + (affectsY ? distY : -distY) * aspectRatio, childExtent[0][0]) / aspectRatio;\n                }\n                clampY = Math.max(clampY, aspectExtentClamp);\n            }\n        }\n    }\n    distY = distY + (distY < 0 ? clampY : -clampY);\n    distX = distX + (distX < 0 ? clampX : -clampX);\n    if (keepAspectRatio) {\n        if (isDiagonal) {\n            if (newWidth > newHeight * aspectRatio) {\n                distY = (xor(affectsX, affectsY) ? -distX : distX) / aspectRatio;\n            }\n            else {\n                distX = (xor(affectsX, affectsY) ? -distY : distY) * aspectRatio;\n            }\n        }\n        else {\n            if (isHorizontal) {\n                distY = distX / aspectRatio;\n                affectsY = affectsX;\n            }\n            else {\n                distX = distY * aspectRatio;\n                affectsX = affectsY;\n            }\n        }\n    }\n    const x = affectsX ? startX + distX : startX;\n    const y = affectsY ? startY + distY : startY;\n    return {\n        width: startWidth + (affectsX ? -distX : distX),\n        height: startHeight + (affectsY ? -distY : distY),\n        x: nodeOrigin[0] * distX * (!affectsX ? 1 : -1) + x,\n        y: nodeOrigin[1] * distY * (!affectsY ? 1 : -1) + y,\n    };\n}\n\nconst initPrevValues = { width: 0, height: 0, x: 0, y: 0 };\nconst initStartValues = {\n    ...initPrevValues,\n    pointerX: 0,\n    pointerY: 0,\n    aspectRatio: 1,\n};\nfunction nodeToParentExtent(node) {\n    return [\n        [0, 0],\n        [node.measured.width, node.measured.height],\n    ];\n}\nfunction nodeToChildExtent(child, parent, nodeOrigin) {\n    const x = parent.position.x + child.position.x;\n    const y = parent.position.y + child.position.y;\n    const width = child.measured.width ?? 0;\n    const height = child.measured.height ?? 0;\n    const originOffsetX = nodeOrigin[0] * width;\n    const originOffsetY = nodeOrigin[1] * height;\n    return [\n        [x - originOffsetX, y - originOffsetY],\n        [x + width - originOffsetX, y + height - originOffsetY],\n    ];\n}\nfunction XYResizer({ domNode, nodeId, getStoreItems, onChange, onEnd }) {\n    const selection = (0,d3_selection__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domNode);\n    function update({ controlPosition, boundaries, keepAspectRatio, onResizeStart, onResize, onResizeEnd, shouldResize, }) {\n        let prevValues = { ...initPrevValues };\n        let startValues = { ...initStartValues };\n        const controlDirection = getControlDirection(controlPosition);\n        let node = undefined;\n        let containerBounds = null;\n        let childNodes = [];\n        let parentNode = undefined; // Needed to fix expandParent\n        let parentExtent = undefined;\n        let childExtent = undefined;\n        const dragHandler = (0,d3_drag__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()\n            .on('start', (event) => {\n            const { nodeLookup, transform, snapGrid, snapToGrid, nodeOrigin, paneDomNode } = getStoreItems();\n            node = nodeLookup.get(nodeId);\n            if (!node) {\n                return;\n            }\n            containerBounds = paneDomNode?.getBoundingClientRect() ?? null;\n            const { xSnapped, ySnapped } = getPointerPosition(event.sourceEvent, {\n                transform,\n                snapGrid,\n                snapToGrid,\n                containerBounds,\n            });\n            prevValues = {\n                width: node.measured.width ?? 0,\n                height: node.measured.height ?? 0,\n                x: node.position.x ?? 0,\n                y: node.position.y ?? 0,\n            };\n            startValues = {\n                ...prevValues,\n                pointerX: xSnapped,\n                pointerY: ySnapped,\n                aspectRatio: prevValues.width / prevValues.height,\n            };\n            parentNode = undefined;\n            if (node.parentId && (node.extent === 'parent' || node.expandParent)) {\n                parentNode = nodeLookup.get(node.parentId);\n                parentExtent = parentNode && node.extent === 'parent' ? nodeToParentExtent(parentNode) : undefined;\n            }\n            // Collect all child nodes to correct their relative positions when top/left changes\n            // Determine largest minimal extent the parent node is allowed to resize to\n            childNodes = [];\n            childExtent = undefined;\n            for (const [childId, child] of nodeLookup) {\n                if (child.parentId === nodeId) {\n                    childNodes.push({\n                        id: childId,\n                        position: { ...child.position },\n                        extent: child.extent,\n                    });\n                    if (child.extent === 'parent' || child.expandParent) {\n                        const extent = nodeToChildExtent(child, node, child.origin ?? nodeOrigin);\n                        if (childExtent) {\n                            childExtent = [\n                                [Math.min(extent[0][0], childExtent[0][0]), Math.min(extent[0][1], childExtent[0][1])],\n                                [Math.max(extent[1][0], childExtent[1][0]), Math.max(extent[1][1], childExtent[1][1])],\n                            ];\n                        }\n                        else {\n                            childExtent = extent;\n                        }\n                    }\n                }\n            }\n            onResizeStart?.(event, { ...prevValues });\n        })\n            .on('drag', (event) => {\n            const { transform, snapGrid, snapToGrid, nodeOrigin: storeNodeOrigin } = getStoreItems();\n            const pointerPosition = getPointerPosition(event.sourceEvent, {\n                transform,\n                snapGrid,\n                snapToGrid,\n                containerBounds,\n            });\n            const childChanges = [];\n            if (!node) {\n                return;\n            }\n            const { x: prevX, y: prevY, width: prevWidth, height: prevHeight } = prevValues;\n            const change = {};\n            const nodeOrigin = node.origin ?? storeNodeOrigin;\n            const { width, height, x, y } = getDimensionsAfterResize(startValues, controlDirection, pointerPosition, boundaries, keepAspectRatio, nodeOrigin, parentExtent, childExtent);\n            const isWidthChange = width !== prevWidth;\n            const isHeightChange = height !== prevHeight;\n            const isXPosChange = x !== prevX && isWidthChange;\n            const isYPosChange = y !== prevY && isHeightChange;\n            if (!isXPosChange && !isYPosChange && !isWidthChange && !isHeightChange) {\n                return;\n            }\n            if (isXPosChange || isYPosChange || nodeOrigin[0] === 1 || nodeOrigin[1] === 1) {\n                change.x = isXPosChange ? x : prevValues.x;\n                change.y = isYPosChange ? y : prevValues.y;\n                prevValues.x = change.x;\n                prevValues.y = change.y;\n                // when top/left changes, correct the relative positions of child nodes\n                // so that they stay in the same position\n                if (childNodes.length > 0) {\n                    const xChange = x - prevX;\n                    const yChange = y - prevY;\n                    for (const childNode of childNodes) {\n                        childNode.position = {\n                            x: childNode.position.x - xChange + nodeOrigin[0] * (width - prevWidth),\n                            y: childNode.position.y - yChange + nodeOrigin[1] * (height - prevHeight),\n                        };\n                        childChanges.push(childNode);\n                    }\n                }\n            }\n            if (isWidthChange || isHeightChange) {\n                change.width = isWidthChange ? width : prevValues.width;\n                change.height = isHeightChange ? height : prevValues.height;\n                prevValues.width = change.width;\n                prevValues.height = change.height;\n            }\n            // Fix expandParent when resizing from top/left\n            if (parentNode && node.expandParent) {\n                const xLimit = nodeOrigin[0] * (change.width ?? 0);\n                if (change.x && change.x < xLimit) {\n                    prevValues.x = xLimit;\n                    startValues.x = startValues.x - (change.x - xLimit);\n                }\n                const yLimit = nodeOrigin[1] * (change.height ?? 0);\n                if (change.y && change.y < yLimit) {\n                    prevValues.y = yLimit;\n                    startValues.y = startValues.y - (change.y - yLimit);\n                }\n            }\n            const direction = getResizeDirection({\n                width: prevValues.width,\n                prevWidth,\n                height: prevValues.height,\n                prevHeight,\n                affectsX: controlDirection.affectsX,\n                affectsY: controlDirection.affectsY,\n            });\n            const nextValues = { ...prevValues, direction };\n            const callResize = shouldResize?.(event, nextValues);\n            if (callResize === false) {\n                return;\n            }\n            onResize?.(event, nextValues);\n            onChange(change, childChanges);\n        })\n            .on('end', (event) => {\n            onResizeEnd?.(event, { ...prevValues });\n            onEnd?.();\n        });\n        selection.call(dragHandler);\n    }\n    function destroy() {\n        selection.on('.drag', null);\n    }\n    return {\n        update,\n        destroy,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xyflow/system/dist/esm/index.js\n");

/***/ })

};
;