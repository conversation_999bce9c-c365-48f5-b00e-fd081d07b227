Metadata-Version: 2.3
Name: gravitasml
Version: 0.1.3
Summary: 
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.10,<4.0
Classifier: Framework :: Pydantic :: 2
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Text Processing :: Markup
Classifier: Typing :: Typed
Requires-Dist: black (>=24.10.0,<25.0.0)
Requires-Dist: pydantic (>=2.9.2,<3.0.0)
Requires-Dist: pytest (>=8.2.1,<9.0.0)
Project-URL: Homepage, https://github.com/significant-gravitas/
Description-Content-Type: text/markdown


# GravitasML

GravitasML is a lightweight Python library for parsing custom markup languages. It provides a simple and intuitive API to convert markup into Python objects or dictionaries.

## Features

- Easy-to-use parser for custom markup languages.
- Convert markup directly to Python dictionaries.
- Handles nested and multiple root tags.
- Customizable tag names with automatic whitespace conversion.
- Syntax error detection for unmatched or improperly nested tags.

## Installation

To install GravitasML, use pip as follows:

```bash
pip install gravitasml
```

## Quick Start

Here's a quick example to get you started:

```python
from gravitasml.token import tokenize
from gravitasml.parser import Parser

markup = "<tag1><tag2>content</tag2></tag1>"
tokens = tokenize(markup)
parser = Parser(tokens)
obj = parser.parse()

print(obj)  # Output: {'tag1': {'tag2': 'content'}}
```

## Limitations

GravitasML is designed to be simple and intuitive, but there are some limitations to be aware of:

- It does not support XML namespaces or schema validation.
- It does not handle processing instructions or CDATA sections found in XML.
- Currently, there is no support for attributes within tags; only tag names and content are parsed.
- It does not provide functionality to write or generate markup, only to parse it.
- GravitasML is not optimized for extremely large documents or streaming parsing.

## Documentation

For detailed usage and documentation, please refer to the `docs` directory in this repository.

## Tests

GravitasML comes with a comprehensive test suite. To run the tests, execute the following command:

```bash
python -m unittest discover -v
```

## Contributing

We welcome contributions from the community. If you'd like to contribute, please fork the repository and submit a pull request.

## License

GravitasML is licensed under the MIT License - see the LICENSE file for details.

