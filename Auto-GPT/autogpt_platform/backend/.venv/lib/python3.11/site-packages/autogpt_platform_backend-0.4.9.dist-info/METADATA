Metadata-Version: 2.3
Name: autogpt-platform-backend
Version: 0.4.9
Summary: A platform for building AI-powered agentic workflows
Author: AutoGPT
Author-email: <EMAIL>
Requires-Python: >=3.10,<3.13
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: aio-pika (>=9.5.5,<10.0.0)
Requires-Dist: anthropic (>=0.49.0,<0.50.0)
Requires-Dist: apscheduler (>=3.11.0,<4.0.0)
Requires-Dist: autogpt-libs @ file:///Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/autogpt_libs
Requires-Dist: bleach[css] (>=6.2.0,<7.0.0)
Requires-Dist: click (>=8.1.7,<9.0.0)
Requires-Dist: cryptography (>=43.0,<44.0)
Requires-Dist: discord-py (>=2.5.2,<3.0.0)
Requires-Dist: e2b-code-interpreter (>=1.1.1,<2.0.0)
Requires-Dist: fastapi (>=0.115.12,<0.116.0)
Requires-Dist: feedparser (>=6.0.11,<7.0.0)
Requires-Dist: flake8 (>=7.2.0,<8.0.0)
Requires-Dist: google-api-python-client (>=2.166.0,<3.0.0)
Requires-Dist: google-auth-oauthlib (>=1.2.1,<2.0.0)
Requires-Dist: google-cloud-storage (>=3.1.0,<4.0.0)
Requires-Dist: googlemaps (>=4.10.0,<5.0.0)
Requires-Dist: gravitasml (>=0.1.3,<0.2.0)
Requires-Dist: groq (>=0.20.0,<0.21.0)
Requires-Dist: jinja2 (>=3.1.6,<4.0.0)
Requires-Dist: jsonref (>=1.1.0,<2.0.0)
Requires-Dist: jsonschema (>=4.22.0,<5.0.0)
Requires-Dist: launchdarkly-server-sdk (>=9.10.0,<10.0.0)
Requires-Dist: mem0ai (>=0.1.80,<0.2.0)
Requires-Dist: moviepy (>=2.1.2,<3.0.0)
Requires-Dist: ollama (>=0.4.1,<0.5.0)
Requires-Dist: openai (>=1.70.0,<2.0.0)
Requires-Dist: pika (>=1.3.2,<2.0.0)
Requires-Dist: pinecone (>=5.3.1,<6.0.0)
Requires-Dist: poetry (>=2.1.2,<3.0.0)
Requires-Dist: postmarker (>=1.0,<2.0)
Requires-Dist: praw (>=7.8.1,<7.9.0)
Requires-Dist: prisma (>=0.15.0,<0.16.0)
Requires-Dist: prometheus-client (>=0.21.1,<0.22.0)
Requires-Dist: psutil (>=7.0.0,<8.0.0)
Requires-Dist: psycopg2-binary (>=2.9.10,<3.0.0)
Requires-Dist: pydantic-settings (>=2.8.1,<3.0.0)
Requires-Dist: pydantic[email] (>=2.11.1,<3.0.0)
Requires-Dist: pytest (>=8.3.5,<9.0.0)
Requires-Dist: pytest-asyncio (>=0.26.0,<0.27.0)
Requires-Dist: python-dotenv (>=1.1.0,<2.0.0)
Requires-Dist: python-multipart (>=0.0.20,<0.0.21)
Requires-Dist: redis (>=5.2.0,<6.0.0)
Requires-Dist: replicate (>=1.0.4,<2.0.0)
Requires-Dist: sentry-sdk[anthropic,fastapi,launchdarkly,openai,sqlalchemy] (>=2.25.1,<3.0.0)
Requires-Dist: sqlalchemy (>=2.0.40,<3.0.0)
Requires-Dist: strenum (>=0.4.9,<0.5.0)
Requires-Dist: stripe (>=11.5.0,<12.0.0)
Requires-Dist: supabase (==2.15.0)
Requires-Dist: tenacity (>=9.0.0,<10.0.0)
Requires-Dist: todoist-api-python (>=2.1.7,<3.0.0)
Requires-Dist: tweepy (>=4.14.0,<5.0.0)
Requires-Dist: uvicorn[standard] (>=0.34.0,<0.35.0)
Requires-Dist: websockets (>=14.2,<15.0)
Requires-Dist: youtube-transcript-api (>=0.6.2,<0.7.0)
Requires-Dist: zerobouncesdk (>=1.1.1,<2.0.0)
Description-Content-Type: text/markdown

[Getting Started (Released)](https://docs.agpt.co/platform/getting-started/#autogpt_agent_server)
