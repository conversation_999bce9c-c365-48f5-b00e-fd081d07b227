Metadata-Version: 2.3
Name: autogpt-libs
Version: 0.2.0
Summary: Shared libraries across NextGen AutoGPT
Author: Aarushi
Author-email: <EMAIL>
Requires-Python: >=3.10,<4.0
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: colorama (>=0.4.6,<0.5.0)
Requires-Dist: expiringdict (>=1.2.2,<2.0.0)
Requires-Dist: google-cloud-logging (>=3.11.4,<4.0.0)
Requires-Dist: pydantic (>=2.11.1,<3.0.0)
Requires-Dist: pydantic-settings (>=2.8.1,<3.0.0)
Requires-Dist: pyjwt (>=2.10.1,<3.0.0)
Requires-Dist: pytest-asyncio (>=0.26.0,<0.27.0)
Requires-Dist: pytest-mock (>=3.14.0,<4.0.0)
Requires-Dist: supabase (>=2.15.0,<3.0.0)
Description-Content-Type: text/markdown

# AutoGPT Libs

This is a new project to store shared functionality across different services in NextGen AutoGPT (e.g. authentication)

