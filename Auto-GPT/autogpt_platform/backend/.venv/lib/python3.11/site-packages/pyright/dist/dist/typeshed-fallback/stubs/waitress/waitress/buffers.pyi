from _typeshed import ReadableBuffer
from io import BufferedIOB<PERSON>, BufferedRandom, Bytes<PERSON>
from typing import Final, Literal, NoReturn

COPY_BYTES: Final = 262144
STRBUF_LIMIT: Final = 8192

class FileBasedBuffer:
    remain: int
    file: Bytes<PERSON>
    def __init__(self, file: BytesIO, from_buffer: BytesIO | None = None) -> None: ...
    def __len__(self) -> int: ...
    def __bool__(self) -> Literal[True]: ...
    def append(self, s: ReadableBuffer) -> None: ...
    def get(self, numbytes: int = -1, skip: bool = False) -> bytes: ...
    def skip(self, numbytes: int, allow_prune: int = 0) -> None: ...
    def newfile(self) -> BufferedIOBase: ...
    def prune(self) -> None: ...
    def getfile(self) -> BytesIO: ...
    def close(self) -> None: ...

class TempfileBasedBuffer(FileBasedBuffer):
    def __init__(self, from_buffer: BytesIO | None = None) -> None: ...
    def newfile(self) -> BufferedRandom: ...

class BytesIOBasedBuffer(FileBasedBuffer):
    file: BytesIO
    def __init__(self, from_buffer: BytesIO | None = None) -> None: ...
    def newfile(self) -> BytesIO: ...

class ReadOnlyFileBasedBuffer(FileBasedBuffer):
    file: BytesIO
    block_size: int
    def __init__(self, file: BytesIO, block_size: int = 32768) -> None: ...
    remain: int
    def prepare(self, size: int | None = None) -> int: ...
    def get(self, numbytes: int = -1, skip: bool = False) -> bytes: ...
    def __iter__(self) -> ReadOnlyFileBasedBuffer: ...
    def next(self) -> bytes | None: ...
    __next__ = next
    def append(self, s: ReadableBuffer) -> NoReturn: ...

class OverflowableBuffer:
    overflowed: bool
    buf: BufferedIOBase | None
    strbuf: bytes
    overflow: int
    def __init__(self, overflow: int) -> None: ...
    def __len__(self) -> int: ...
    def __bool__(self) -> bool: ...
    def append(self, s: bytes) -> None: ...
    def get(self, numbytes: int = -1, skip: bool = False) -> bytes: ...
    def skip(self, numbytes: int, allow_prune: bool = False) -> None: ...
    def prune(self) -> None: ...
    def getfile(self) -> BytesIO: ...
    def close(self) -> None: ...
