from _typeshed import Incomplete
from typing import ClassVar

from ..core import WesternCalendar

class SouthAfrica(WesternCalendar):
    include_good_friday: ClassVar[bool]
    include_christmas: ClassVar[bool]
    def holidays(self, year: Incomplete | None = None): ...
    def get_easter_monday_or_family_day(self, year): ...
    def get_fixed_holidays(self, year): ...
    def get_variable_days(self, year): ...
    def get_calendar_holidays(self, year): ...
