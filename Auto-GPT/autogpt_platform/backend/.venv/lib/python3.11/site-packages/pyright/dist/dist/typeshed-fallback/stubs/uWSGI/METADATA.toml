version = "2.0.*"
upstream_repository = "https://github.com/unbit/uwsgi"
extra_description = """\
  Type hints for uWSGI's \
  [Python API](https://uwsgi-docs.readthedocs.io/en/latest/PythonModule.html). \
  Note that this API is available only when running Python code inside a uWSGI process \
  and some parts of the API are only present when corresponding configuration options \
  have been enabled.
"""

[tool.stubtest]
# Run stubtest on MacOS as well, to check that the
# uWSGI-specific parts of stubtest_third_party.py
# also work there
platforms = ["linux", "darwin"]
