from _typeshed import Incomplete

from .core import Calendar

class IsoRegistry:
    STANDARD_MODULES: Incomplete
    region_registry: dict[str, Calendar]
    def __init__(self, load_standard_modules: bool = True) -> None: ...
    def register(self, iso_code, cls) -> None: ...
    def load_module_from_items(self, module_name, items) -> None: ...
    def get(self, iso_code): ...
    def get_subregions(self, iso_code): ...
    def get_calendars(self, region_codes: Incomplete | None = None, include_subregions: bool = False): ...

registry: IsoRegistry
