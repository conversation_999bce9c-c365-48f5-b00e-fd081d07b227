# -*- coding: utf-8 -*-
# code generated by <PERSON>rism<PERSON>. DO NOT EDIT.
# pyright: reportUnusedImport=false
# fmt: off

# global imports for type checking
from builtins import bool as _bool
from builtins import int as _int
from builtins import float as _float
from builtins import str as _str
import sys
import decimal
import datetime
from typing import (
    TYPE_CHECKING,
    Optional,
    Iterable,
    Iterator,
    Sequence,
    Callable,
    ClassVar,
    NoReturn,
    TypeVar,
    Generic,
    Mapping,
    Tuple,
    Union,
    List,
    Dict,
    Type,
    Any,
    Set,
    overload,
    cast,
)
from typing_extensions import TypedDict, Literal


from typing_extensions import LiteralString
# -- template enums.py.jinja --
from ._compat import StrEnum


class OnboardingStep(StrEnum):
    WELCOME = 'WELCOME'
    USAGE_REASON = 'USAGE_REASON'
    INTEGRATIONS = 'INTEGRATIONS'
    AGENT_CHOICE = 'AGENT_CHOICE'
    AGENT_NEW_RUN = 'AGENT_NEW_RUN'
    AGENT_INPUT = 'AGENT_INPUT'
    CONGRATS = 'CONGRATS'
    GET_RESULTS = 'GET_RESULTS'
    MARKETPLACE_VISIT = 'MARKETPLACE_VISIT'
    MARKETPLACE_ADD_AGENT = 'MARKETPLACE_ADD_AGENT'
    MARKETPLACE_RUN_AGENT = 'MARKETPLACE_RUN_AGENT'
    BUILDER_OPEN = 'BUILDER_OPEN'
    BUILDER_SAVE_AGENT = 'BUILDER_SAVE_AGENT'
    BUILDER_RUN_AGENT = 'BUILDER_RUN_AGENT'

class NotificationType(StrEnum):
    AGENT_RUN = 'AGENT_RUN'
    ZERO_BALANCE = 'ZERO_BALANCE'
    LOW_BALANCE = 'LOW_BALANCE'
    BLOCK_EXECUTION_FAILED = 'BLOCK_EXECUTION_FAILED'
    CONTINUOUS_AGENT_ERROR = 'CONTINUOUS_AGENT_ERROR'
    DAILY_SUMMARY = 'DAILY_SUMMARY'
    WEEKLY_SUMMARY = 'WEEKLY_SUMMARY'
    MONTHLY_SUMMARY = 'MONTHLY_SUMMARY'
    REFUND_REQUEST = 'REFUND_REQUEST'
    REFUND_PROCESSED = 'REFUND_PROCESSED'

class AgentExecutionStatus(StrEnum):
    INCOMPLETE = 'INCOMPLETE'
    QUEUED = 'QUEUED'
    RUNNING = 'RUNNING'
    COMPLETED = 'COMPLETED'
    TERMINATED = 'TERMINATED'
    FAILED = 'FAILED'

class CreditTransactionType(StrEnum):
    TOP_UP = 'TOP_UP'
    USAGE = 'USAGE'
    GRANT = 'GRANT'
    REFUND = 'REFUND'
    CARD_CHECK = 'CARD_CHECK'

class CreditRefundRequestStatus(StrEnum):
    PENDING = 'PENDING'
    APPROVED = 'APPROVED'
    REJECTED = 'REJECTED'

class SubmissionStatus(StrEnum):
    DRAFT = 'DRAFT'
    PENDING = 'PENDING'
    APPROVED = 'APPROVED'
    REJECTED = 'REJECTED'

class APIKeyPermission(StrEnum):
    EXECUTE_GRAPH = 'EXECUTE_GRAPH'
    READ_GRAPH = 'READ_GRAPH'
    EXECUTE_BLOCK = 'EXECUTE_BLOCK'
    READ_BLOCK = 'READ_BLOCK'

class APIKeyStatus(StrEnum):
    ACTIVE = 'ACTIVE'
    REVOKED = 'REVOKED'
    SUSPENDED = 'SUSPENDED'

