# -*- coding: utf-8 -*-
# code generated by Prisma. DO NOT EDIT.
# pyright: reportUnusedImport=false
# fmt: off
from __future__ import annotations

# global imports for type checking
from builtins import bool as _bool
from builtins import int as _int
from builtins import float as _float
from builtins import str as _str
import sys
import decimal
import datetime
from typing import (
    TYPE_CHECKING,
    Optional,
    Iterable,
    Iterator,
    Sequence,
    Callable,
    ClassVar,
    NoReturn,
    TypeVar,
    Generic,
    Mapping,
    Tuple,
    Union,
    List,
    Dict,
    Type,
    Any,
    Set,
    overload,
    cast,
)
from typing_extensions import TypedDict, Literal


from typing_extensions import LiteralString
# -- template client.py.jinja --
import warnings
import logging
from datetime import timed<PERSON>ta
from pathlib import Path
from types import TracebackType
from typing_extensions import override

from pydantic import BaseModel

from . import types, models, errors, actions
from ._base_client import BasePrisma, UseClientDefault, USE_CLIENT_DEFAULT
from .types import DatasourceOverride, HttpConfig, MetricsFormat
from ._types import BaseModelT, PrismaMethod, TransactionId, <PERSON>source
from .bases import _PrismaModel
from ._builder import QueryBuilder, dumps
from .generator.models import EngineType, OptionalValueFromEnvVar, BinaryPaths
from ._compat import removeprefix, model_parse
from ._constants import CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED, DEFAULT_CONNECT_TIMEOUT, DEFAULT_TX_MAX_WAIT, DEFAULT_TX_TIMEOUT
from ._raw_query import deserialize_raw_results
from ._metrics import Metrics
from .metadata import PRISMA_MODELS, RELATIONAL_FIELD_MAPPINGS
from ._transactions import AsyncTransactionManager, SyncTransactionManager

# re-exports
from ._base_client import SyncBasePrisma, AsyncBasePrisma, load_env as load_env
from ._registry import (
    register as register,
    get_client as get_client,
    RegisteredClient as RegisteredClient,
)


__all__ = (
    'ENGINE_TYPE',
    'SCHEMA_PATH',
    'BINARY_PATHS',
    'Batch',
    'Prisma',
    'Client',
    'load_env',
    'register',
    'get_client',
)

log: logging.Logger = logging.getLogger(__name__)

SCHEMA_PATH = Path('/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/backend/schema.prisma')
PACKAGED_SCHEMA_PATH = Path(__file__).parent.joinpath('schema.prisma')
ENGINE_TYPE: EngineType = EngineType.binary
BINARY_PATHS = model_parse(BinaryPaths, {'queryEngine': {'darwin': '/Users/<USER>/.cache/prisma-python/binaries/5.17.0/393aa359c9ad4a4bb28630fb5613f9c281cde053/node_modules/prisma/query-engine-darwin'}, 'introspectionEngine': {}, 'migrationEngine': {}, 'libqueryEngine': {}, 'prismaFmt': {}})


class Prisma(AsyncBasePrisma):
    # Note: these property names can be customised using `/// @Python(instance_name: '...')`
    # https://prisma-client-py.readthedocs.io/en/stable/reference/schema-extensions/#instance_name
    user: 'actions.UserActions[models.User]'
    useronboarding: 'actions.UserOnboardingActions[models.UserOnboarding]'
    agentgraph: 'actions.AgentGraphActions[models.AgentGraph]'
    agentpreset: 'actions.AgentPresetActions[models.AgentPreset]'
    notificationevent: 'actions.NotificationEventActions[models.NotificationEvent]'
    usernotificationbatch: 'actions.UserNotificationBatchActions[models.UserNotificationBatch]'
    libraryagent: 'actions.LibraryAgentActions[models.LibraryAgent]'
    agentnode: 'actions.AgentNodeActions[models.AgentNode]'
    agentnodelink: 'actions.AgentNodeLinkActions[models.AgentNodeLink]'
    agentblock: 'actions.AgentBlockActions[models.AgentBlock]'
    agentgraphexecution: 'actions.AgentGraphExecutionActions[models.AgentGraphExecution]'
    agentnodeexecution: 'actions.AgentNodeExecutionActions[models.AgentNodeExecution]'
    agentnodeexecutioninputoutput: 'actions.AgentNodeExecutionInputOutputActions[models.AgentNodeExecutionInputOutput]'
    integrationwebhook: 'actions.IntegrationWebhookActions[models.IntegrationWebhook]'
    analyticsdetails: 'actions.AnalyticsDetailsActions[models.AnalyticsDetails]'
    analyticsmetrics: 'actions.AnalyticsMetricsActions[models.AnalyticsMetrics]'
    credittransaction: 'actions.CreditTransactionActions[models.CreditTransaction]'
    creditrefundrequest: 'actions.CreditRefundRequestActions[models.CreditRefundRequest]'
    profile: 'actions.ProfileActions[models.Profile]'
    storelisting: 'actions.StoreListingActions[models.StoreListing]'
    storelistingversion: 'actions.StoreListingVersionActions[models.StoreListingVersion]'
    storelistingreview: 'actions.StoreListingReviewActions[models.StoreListingReview]'
    apikey: 'actions.APIKeyActions[models.APIKey]'
    creator: 'actions.CreatorActions[models.Creator]'
    storeagent: 'actions.StoreAgentActions[models.StoreAgent]'
    storesubmission: 'actions.StoreSubmissionActions[models.StoreSubmission]'

    __slots__ = (
        'user',
        'useronboarding',
        'agentgraph',
        'agentpreset',
        'notificationevent',
        'usernotificationbatch',
        'libraryagent',
        'agentnode',
        'agentnodelink',
        'agentblock',
        'agentgraphexecution',
        'agentnodeexecution',
        'agentnodeexecutioninputoutput',
        'integrationwebhook',
        'analyticsdetails',
        'analyticsmetrics',
        'credittransaction',
        'creditrefundrequest',
        'profile',
        'storelisting',
        'storelistingversion',
        'storelistingreview',
        'apikey',
        'creator',
        'storeagent',
        'storesubmission',
    )

    def __init__(
        self,
        *,
        use_dotenv: bool = True,
        log_queries: bool = False,
        auto_register: bool = False,
        datasource: DatasourceOverride | None = None,
        connect_timeout: int | timedelta = DEFAULT_CONNECT_TIMEOUT,
        http: HttpConfig | None = None,
    ) -> None:
        super().__init__(
            http=http,
            use_dotenv=use_dotenv,
            log_queries=log_queries,
            datasource=datasource,
            connect_timeout=connect_timeout,
        )
        self._set_generated_properties(
            schema_path=SCHEMA_PATH,
            engine_type=ENGINE_TYPE,
            prisma_models=PRISMA_MODELS,
            packaged_schema_path=PACKAGED_SCHEMA_PATH,
            relational_field_mappings=RELATIONAL_FIELD_MAPPINGS,
            preview_features=set(['views']),
            active_provider='postgresql',
            default_datasource_name='db',
        )

        self.user = actions.UserActions[models.User](self, models.User)
        self.useronboarding = actions.UserOnboardingActions[models.UserOnboarding](self, models.UserOnboarding)
        self.agentgraph = actions.AgentGraphActions[models.AgentGraph](self, models.AgentGraph)
        self.agentpreset = actions.AgentPresetActions[models.AgentPreset](self, models.AgentPreset)
        self.notificationevent = actions.NotificationEventActions[models.NotificationEvent](self, models.NotificationEvent)
        self.usernotificationbatch = actions.UserNotificationBatchActions[models.UserNotificationBatch](self, models.UserNotificationBatch)
        self.libraryagent = actions.LibraryAgentActions[models.LibraryAgent](self, models.LibraryAgent)
        self.agentnode = actions.AgentNodeActions[models.AgentNode](self, models.AgentNode)
        self.agentnodelink = actions.AgentNodeLinkActions[models.AgentNodeLink](self, models.AgentNodeLink)
        self.agentblock = actions.AgentBlockActions[models.AgentBlock](self, models.AgentBlock)
        self.agentgraphexecution = actions.AgentGraphExecutionActions[models.AgentGraphExecution](self, models.AgentGraphExecution)
        self.agentnodeexecution = actions.AgentNodeExecutionActions[models.AgentNodeExecution](self, models.AgentNodeExecution)
        self.agentnodeexecutioninputoutput = actions.AgentNodeExecutionInputOutputActions[models.AgentNodeExecutionInputOutput](self, models.AgentNodeExecutionInputOutput)
        self.integrationwebhook = actions.IntegrationWebhookActions[models.IntegrationWebhook](self, models.IntegrationWebhook)
        self.analyticsdetails = actions.AnalyticsDetailsActions[models.AnalyticsDetails](self, models.AnalyticsDetails)
        self.analyticsmetrics = actions.AnalyticsMetricsActions[models.AnalyticsMetrics](self, models.AnalyticsMetrics)
        self.credittransaction = actions.CreditTransactionActions[models.CreditTransaction](self, models.CreditTransaction)
        self.creditrefundrequest = actions.CreditRefundRequestActions[models.CreditRefundRequest](self, models.CreditRefundRequest)
        self.profile = actions.ProfileActions[models.Profile](self, models.Profile)
        self.storelisting = actions.StoreListingActions[models.StoreListing](self, models.StoreListing)
        self.storelistingversion = actions.StoreListingVersionActions[models.StoreListingVersion](self, models.StoreListingVersion)
        self.storelistingreview = actions.StoreListingReviewActions[models.StoreListingReview](self, models.StoreListingReview)
        self.apikey = actions.APIKeyActions[models.APIKey](self, models.APIKey)
        self.creator = actions.CreatorActions[models.Creator](self, models.Creator)
        self.storeagent = actions.StoreAgentActions[models.StoreAgent](self, models.StoreAgent)
        self.storesubmission = actions.StoreSubmissionActions[models.StoreSubmission](self, models.StoreSubmission)

        if auto_register:
            register(self)

    @property
    @override
    def _default_datasource(self) -> Datasource:
        return {
            'name': 'db',
            'url': OptionalValueFromEnvVar(**{'value': None, 'fromEnvVar': 'DATABASE_URL'}).resolve(),
            'source_file_path': '/Users/<USER>/codes/automation_practice/void_chat_codes/Auto-GPT/autogpt_platform/backend/schema.prisma',
        }

    async def execute_raw(self, query: LiteralString, *args: Any) -> int:
        resp = await self._execute(
            method='execute_raw',
            arguments={
                'query': query,
                'parameters': args,
            },
            model=None,
        )
        return int(resp['data']['result'])

    @overload
    async def query_first(
        self,
        query: LiteralString,
        *args: Any,
    ) -> dict[str, Any]:
        ...

    @overload
    async def query_first(
        self,
        query: LiteralString,
        *args: Any,
        model: Type[BaseModelT],
    ) -> Optional[BaseModelT]:
        ...

    async def query_first(
        self,
        query: LiteralString,
        *args: Any,
        model: Optional[Type[BaseModelT]] = None,
    ) -> Union[Optional[BaseModelT], dict[str, Any]]:
        """This function is the exact same as `query_raw()` but returns the first result.

        If model is given, the returned record is converted to the pydantic model first,
        otherwise a raw dictionary will be returned.
        """
        results: Sequence[Union[BaseModelT, dict[str, Any]]]
        if model is not None:
            results = await self.query_raw(query, *args, model=model)
        else:
            results = await self.query_raw(query, *args)

        if not results:
            return None

        return results[0]

    @overload
    async def query_raw(
        self,
        query: LiteralString,
        *args: Any,
    ) -> List[dict[str, Any]]:
        ...

    @overload
    async def query_raw(
        self,
        query: LiteralString,
        *args: Any,
        model: Type[BaseModelT],
    ) -> List[BaseModelT]:
        ...

    async def query_raw(
        self,
        query: LiteralString,
        *args: Any,
        model: Optional[Type[BaseModelT]] = None,
    ) -> Union[List[BaseModelT], List[dict[str, Any]]]:
        """Execute a raw SQL query against the database.

        If model is given, each returned record is converted to the pydantic model first,
        otherwise results will be raw dictionaries.
        """
        resp = await self._execute(
            method='query_raw',
            arguments={
                'query': query,
                'parameters': args,
            },
            model=model,
        )
        result = resp['data']['result']
        if model is not None:
            return deserialize_raw_results(result, model=model)

        return deserialize_raw_results(result)

    def batch_(self) -> Batch:
        """Returns a context manager for grouping write queries into a single transaction."""
        return Batch(client=self)

    def tx(
        self,
        *,
        max_wait: Union[int, timedelta] = DEFAULT_TX_MAX_WAIT,
        timeout: Union[int, timedelta] = DEFAULT_TX_TIMEOUT,
    ) -> TransactionManager:
        """Returns a context manager for executing queries within a database transaction.

        Entering the context manager returns a new Prisma instance wrapping all
        actions within a transaction, queries will be isolated to the Prisma instance and
        will not be commited to the database until the context manager exits.

        By default, Prisma will wait a maximum of 2 seconds to acquire a transaction from the database. You can modify this
        default with the `max_wait` argument which accepts a value in milliseconds or `datetime.timedelta`.

        By default, Prisma will cancel and rollback ay transactions that last longer than 5 seconds. You can modify this timeout
        with the `timeout` argument which accepts a value in milliseconds or `datetime.timedelta`.

        Example usage:

        ```py
        async with client.tx() as transaction:
            user1 = await client.user.create({'name': 'Robert'})
            user2 = await client.user.create({'name': 'Tegan'})
        ```

        In the above example, if the first database call succeeds but the second does not then neither of the records will be created.
        """
        return TransactionManager(
            client=self,
            max_wait=max_wait,
            timeout=timeout,
        )


TransactionManager = AsyncTransactionManager[Prisma]


# TODO: this should return the results as well
# TODO: don't require copy-pasting arguments between actions and batch actions
class Batch:
    user: 'UserBatchActions'
    useronboarding: 'UserOnboardingBatchActions'
    agentgraph: 'AgentGraphBatchActions'
    agentpreset: 'AgentPresetBatchActions'
    notificationevent: 'NotificationEventBatchActions'
    usernotificationbatch: 'UserNotificationBatchBatchActions'
    libraryagent: 'LibraryAgentBatchActions'
    agentnode: 'AgentNodeBatchActions'
    agentnodelink: 'AgentNodeLinkBatchActions'
    agentblock: 'AgentBlockBatchActions'
    agentgraphexecution: 'AgentGraphExecutionBatchActions'
    agentnodeexecution: 'AgentNodeExecutionBatchActions'
    agentnodeexecutioninputoutput: 'AgentNodeExecutionInputOutputBatchActions'
    integrationwebhook: 'IntegrationWebhookBatchActions'
    analyticsdetails: 'AnalyticsDetailsBatchActions'
    analyticsmetrics: 'AnalyticsMetricsBatchActions'
    credittransaction: 'CreditTransactionBatchActions'
    creditrefundrequest: 'CreditRefundRequestBatchActions'
    profile: 'ProfileBatchActions'
    storelisting: 'StoreListingBatchActions'
    storelistingversion: 'StoreListingVersionBatchActions'
    storelistingreview: 'StoreListingReviewBatchActions'
    apikey: 'APIKeyBatchActions'
    creator: 'CreatorBatchActions'
    storeagent: 'StoreAgentBatchActions'
    storesubmission: 'StoreSubmissionBatchActions'

    def __init__(self, client: Prisma) -> None:
        self.__client = client
        self.__queries: List[str] = []
        self._active_provider = client._active_provider
        self.user = UserBatchActions(self)
        self.useronboarding = UserOnboardingBatchActions(self)
        self.agentgraph = AgentGraphBatchActions(self)
        self.agentpreset = AgentPresetBatchActions(self)
        self.notificationevent = NotificationEventBatchActions(self)
        self.usernotificationbatch = UserNotificationBatchBatchActions(self)
        self.libraryagent = LibraryAgentBatchActions(self)
        self.agentnode = AgentNodeBatchActions(self)
        self.agentnodelink = AgentNodeLinkBatchActions(self)
        self.agentblock = AgentBlockBatchActions(self)
        self.agentgraphexecution = AgentGraphExecutionBatchActions(self)
        self.agentnodeexecution = AgentNodeExecutionBatchActions(self)
        self.agentnodeexecutioninputoutput = AgentNodeExecutionInputOutputBatchActions(self)
        self.integrationwebhook = IntegrationWebhookBatchActions(self)
        self.analyticsdetails = AnalyticsDetailsBatchActions(self)
        self.analyticsmetrics = AnalyticsMetricsBatchActions(self)
        self.credittransaction = CreditTransactionBatchActions(self)
        self.creditrefundrequest = CreditRefundRequestBatchActions(self)
        self.profile = ProfileBatchActions(self)
        self.storelisting = StoreListingBatchActions(self)
        self.storelistingversion = StoreListingVersionBatchActions(self)
        self.storelistingreview = StoreListingReviewBatchActions(self)
        self.apikey = APIKeyBatchActions(self)
        self.creator = CreatorBatchActions(self)
        self.storeagent = StoreAgentBatchActions(self)
        self.storesubmission = StoreSubmissionBatchActions(self)

    def _add(self, **kwargs: Any) -> None:
        builder = QueryBuilder(
            **kwargs,
            prisma_models=PRISMA_MODELS,
            relational_field_mappings=RELATIONAL_FIELD_MAPPINGS,
        )
        self.__queries.append(builder.build_query())

    async def commit(self) -> None:
        """Execute the queries"""
        # TODO: normalise this, we should still call client._execute
        queries = self.__queries
        self.__queries = []

        payload = {
            'batch': [
                {
                    'query': query,
                    'variables': {},
                }
                for query in queries
            ],
            'transaction': True,
        }
        await self.__client._engine.query(
            dumps(payload),
            tx_id=self.__client._tx_id,
        )

    def execute_raw(self, query: LiteralString, *args: Any) -> None:
        self._add(
            method='execute_raw',
            arguments={
                'query': query,
                'parameters': args,
            }
        )

    async def __aenter__(self) -> 'Batch':
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc: Optional[BaseException],
        exc_tb: Optional[TracebackType],
    ) -> None:
        if exc is None:
            await self.commit()


# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class UserBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.UserCreateInput,
        include: Optional[types.UserInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.User,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.UserCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.User,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.UserWhereUniqueInput,
        include: Optional[types.UserInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.User,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.UserUpdateInput,
        where: types.UserWhereUniqueInput,
        include: Optional[types.UserInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.User,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.UserWhereUniqueInput,
        data: types.UserUpsertInput,
        include: Optional[types.UserInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.User,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.UserUpdateManyMutationInput,
        where: types.UserWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.User,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.UserWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.User,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class UserOnboardingBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.UserOnboardingCreateInput,
        include: Optional[types.UserOnboardingInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.UserOnboarding,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.UserOnboardingCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.UserOnboarding,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.UserOnboardingWhereUniqueInput,
        include: Optional[types.UserOnboardingInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.UserOnboarding,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.UserOnboardingUpdateInput,
        where: types.UserOnboardingWhereUniqueInput,
        include: Optional[types.UserOnboardingInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.UserOnboarding,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.UserOnboardingWhereUniqueInput,
        data: types.UserOnboardingUpsertInput,
        include: Optional[types.UserOnboardingInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.UserOnboarding,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.UserOnboardingUpdateManyMutationInput,
        where: types.UserOnboardingWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.UserOnboarding,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.UserOnboardingWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.UserOnboarding,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentGraphBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentGraphCreateInput,
        include: Optional[types.AgentGraphInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentGraph,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentGraphCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentGraph,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentGraphWhereUniqueInput,
        include: Optional[types.AgentGraphInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentGraph,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentGraphUpdateInput,
        where: types.AgentGraphWhereUniqueInput,
        include: Optional[types.AgentGraphInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentGraph,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentGraphWhereUniqueInput,
        data: types.AgentGraphUpsertInput,
        include: Optional[types.AgentGraphInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentGraph,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentGraphUpdateManyMutationInput,
        where: types.AgentGraphWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentGraph,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentGraphWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentGraph,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentPresetBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentPresetCreateInput,
        include: Optional[types.AgentPresetInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentPreset,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentPresetCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentPreset,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentPresetWhereUniqueInput,
        include: Optional[types.AgentPresetInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentPreset,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentPresetUpdateInput,
        where: types.AgentPresetWhereUniqueInput,
        include: Optional[types.AgentPresetInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentPreset,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentPresetWhereUniqueInput,
        data: types.AgentPresetUpsertInput,
        include: Optional[types.AgentPresetInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentPreset,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentPresetUpdateManyMutationInput,
        where: types.AgentPresetWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentPreset,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentPresetWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentPreset,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class NotificationEventBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.NotificationEventCreateInput,
        include: Optional[types.NotificationEventInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.NotificationEvent,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.NotificationEventCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.NotificationEvent,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.NotificationEventWhereUniqueInput,
        include: Optional[types.NotificationEventInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.NotificationEvent,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.NotificationEventUpdateInput,
        where: types.NotificationEventWhereUniqueInput,
        include: Optional[types.NotificationEventInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.NotificationEvent,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.NotificationEventWhereUniqueInput,
        data: types.NotificationEventUpsertInput,
        include: Optional[types.NotificationEventInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.NotificationEvent,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.NotificationEventUpdateManyMutationInput,
        where: types.NotificationEventWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.NotificationEvent,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.NotificationEventWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.NotificationEvent,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class UserNotificationBatchBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.UserNotificationBatchCreateInput,
        include: Optional[types.UserNotificationBatchInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.UserNotificationBatch,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.UserNotificationBatchCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.UserNotificationBatch,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.UserNotificationBatchWhereUniqueInput,
        include: Optional[types.UserNotificationBatchInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.UserNotificationBatch,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.UserNotificationBatchUpdateInput,
        where: types.UserNotificationBatchWhereUniqueInput,
        include: Optional[types.UserNotificationBatchInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.UserNotificationBatch,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.UserNotificationBatchWhereUniqueInput,
        data: types.UserNotificationBatchUpsertInput,
        include: Optional[types.UserNotificationBatchInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.UserNotificationBatch,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.UserNotificationBatchUpdateManyMutationInput,
        where: types.UserNotificationBatchWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.UserNotificationBatch,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.UserNotificationBatchWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.UserNotificationBatch,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class LibraryAgentBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.LibraryAgentCreateInput,
        include: Optional[types.LibraryAgentInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.LibraryAgent,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.LibraryAgentCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.LibraryAgent,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.LibraryAgentWhereUniqueInput,
        include: Optional[types.LibraryAgentInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.LibraryAgent,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.LibraryAgentUpdateInput,
        where: types.LibraryAgentWhereUniqueInput,
        include: Optional[types.LibraryAgentInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.LibraryAgent,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.LibraryAgentWhereUniqueInput,
        data: types.LibraryAgentUpsertInput,
        include: Optional[types.LibraryAgentInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.LibraryAgent,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.LibraryAgentUpdateManyMutationInput,
        where: types.LibraryAgentWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.LibraryAgent,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.LibraryAgentWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.LibraryAgent,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentNodeBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentNodeCreateInput,
        include: Optional[types.AgentNodeInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentNode,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentNodeCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentNode,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentNodeWhereUniqueInput,
        include: Optional[types.AgentNodeInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentNode,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentNodeUpdateInput,
        where: types.AgentNodeWhereUniqueInput,
        include: Optional[types.AgentNodeInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentNode,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentNodeWhereUniqueInput,
        data: types.AgentNodeUpsertInput,
        include: Optional[types.AgentNodeInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentNode,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentNodeUpdateManyMutationInput,
        where: types.AgentNodeWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentNode,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentNodeWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentNode,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentNodeLinkBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentNodeLinkCreateInput,
        include: Optional[types.AgentNodeLinkInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentNodeLink,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentNodeLinkCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentNodeLink,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentNodeLinkWhereUniqueInput,
        include: Optional[types.AgentNodeLinkInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentNodeLink,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentNodeLinkUpdateInput,
        where: types.AgentNodeLinkWhereUniqueInput,
        include: Optional[types.AgentNodeLinkInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentNodeLink,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentNodeLinkWhereUniqueInput,
        data: types.AgentNodeLinkUpsertInput,
        include: Optional[types.AgentNodeLinkInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentNodeLink,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentNodeLinkUpdateManyMutationInput,
        where: types.AgentNodeLinkWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentNodeLink,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentNodeLinkWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentNodeLink,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentBlockBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentBlockCreateInput,
        include: Optional[types.AgentBlockInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentBlock,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentBlockCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentBlock,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentBlockWhereUniqueInput,
        include: Optional[types.AgentBlockInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentBlock,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentBlockUpdateInput,
        where: types.AgentBlockWhereUniqueInput,
        include: Optional[types.AgentBlockInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentBlock,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentBlockWhereUniqueInput,
        data: types.AgentBlockUpsertInput,
        include: Optional[types.AgentBlockInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentBlock,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentBlockUpdateManyMutationInput,
        where: types.AgentBlockWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentBlock,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentBlockWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentBlock,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentGraphExecutionBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentGraphExecutionCreateInput,
        include: Optional[types.AgentGraphExecutionInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentGraphExecution,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentGraphExecutionCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentGraphExecution,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentGraphExecutionWhereUniqueInput,
        include: Optional[types.AgentGraphExecutionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentGraphExecution,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentGraphExecutionUpdateInput,
        where: types.AgentGraphExecutionWhereUniqueInput,
        include: Optional[types.AgentGraphExecutionInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentGraphExecution,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentGraphExecutionWhereUniqueInput,
        data: types.AgentGraphExecutionUpsertInput,
        include: Optional[types.AgentGraphExecutionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentGraphExecution,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentGraphExecutionUpdateManyMutationInput,
        where: types.AgentGraphExecutionWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentGraphExecution,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentGraphExecutionWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentGraphExecution,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentNodeExecutionBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentNodeExecutionCreateInput,
        include: Optional[types.AgentNodeExecutionInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentNodeExecution,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentNodeExecutionCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentNodeExecution,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentNodeExecutionWhereUniqueInput,
        include: Optional[types.AgentNodeExecutionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentNodeExecution,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentNodeExecutionUpdateInput,
        where: types.AgentNodeExecutionWhereUniqueInput,
        include: Optional[types.AgentNodeExecutionInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentNodeExecution,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentNodeExecutionWhereUniqueInput,
        data: types.AgentNodeExecutionUpsertInput,
        include: Optional[types.AgentNodeExecutionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentNodeExecution,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentNodeExecutionUpdateManyMutationInput,
        where: types.AgentNodeExecutionWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentNodeExecution,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentNodeExecutionWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentNodeExecution,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AgentNodeExecutionInputOutputBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AgentNodeExecutionInputOutputCreateInput,
        include: Optional[types.AgentNodeExecutionInputOutputInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AgentNodeExecutionInputOutput,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AgentNodeExecutionInputOutputCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AgentNodeExecutionInputOutput,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AgentNodeExecutionInputOutputWhereUniqueInput,
        include: Optional[types.AgentNodeExecutionInputOutputInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AgentNodeExecutionInputOutput,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AgentNodeExecutionInputOutputUpdateInput,
        where: types.AgentNodeExecutionInputOutputWhereUniqueInput,
        include: Optional[types.AgentNodeExecutionInputOutputInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AgentNodeExecutionInputOutput,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AgentNodeExecutionInputOutputWhereUniqueInput,
        data: types.AgentNodeExecutionInputOutputUpsertInput,
        include: Optional[types.AgentNodeExecutionInputOutputInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AgentNodeExecutionInputOutput,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AgentNodeExecutionInputOutputUpdateManyMutationInput,
        where: types.AgentNodeExecutionInputOutputWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AgentNodeExecutionInputOutput,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AgentNodeExecutionInputOutputWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AgentNodeExecutionInputOutput,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class IntegrationWebhookBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.IntegrationWebhookCreateInput,
        include: Optional[types.IntegrationWebhookInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.IntegrationWebhook,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.IntegrationWebhookCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.IntegrationWebhook,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.IntegrationWebhookWhereUniqueInput,
        include: Optional[types.IntegrationWebhookInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.IntegrationWebhook,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.IntegrationWebhookUpdateInput,
        where: types.IntegrationWebhookWhereUniqueInput,
        include: Optional[types.IntegrationWebhookInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.IntegrationWebhook,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.IntegrationWebhookWhereUniqueInput,
        data: types.IntegrationWebhookUpsertInput,
        include: Optional[types.IntegrationWebhookInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.IntegrationWebhook,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.IntegrationWebhookUpdateManyMutationInput,
        where: types.IntegrationWebhookWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.IntegrationWebhook,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.IntegrationWebhookWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.IntegrationWebhook,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AnalyticsDetailsBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AnalyticsDetailsCreateInput,
        include: Optional[types.AnalyticsDetailsInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AnalyticsDetails,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AnalyticsDetailsCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AnalyticsDetails,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AnalyticsDetailsWhereUniqueInput,
        include: Optional[types.AnalyticsDetailsInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AnalyticsDetails,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AnalyticsDetailsUpdateInput,
        where: types.AnalyticsDetailsWhereUniqueInput,
        include: Optional[types.AnalyticsDetailsInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AnalyticsDetails,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AnalyticsDetailsWhereUniqueInput,
        data: types.AnalyticsDetailsUpsertInput,
        include: Optional[types.AnalyticsDetailsInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AnalyticsDetails,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AnalyticsDetailsUpdateManyMutationInput,
        where: types.AnalyticsDetailsWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AnalyticsDetails,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AnalyticsDetailsWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AnalyticsDetails,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class AnalyticsMetricsBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.AnalyticsMetricsCreateInput,
        include: Optional[types.AnalyticsMetricsInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.AnalyticsMetrics,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.AnalyticsMetricsCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.AnalyticsMetrics,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.AnalyticsMetricsWhereUniqueInput,
        include: Optional[types.AnalyticsMetricsInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.AnalyticsMetrics,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.AnalyticsMetricsUpdateInput,
        where: types.AnalyticsMetricsWhereUniqueInput,
        include: Optional[types.AnalyticsMetricsInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.AnalyticsMetrics,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.AnalyticsMetricsWhereUniqueInput,
        data: types.AnalyticsMetricsUpsertInput,
        include: Optional[types.AnalyticsMetricsInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.AnalyticsMetrics,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.AnalyticsMetricsUpdateManyMutationInput,
        where: types.AnalyticsMetricsWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.AnalyticsMetrics,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.AnalyticsMetricsWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.AnalyticsMetrics,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class CreditTransactionBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.CreditTransactionCreateInput,
        include: Optional[types.CreditTransactionInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.CreditTransaction,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.CreditTransactionCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.CreditTransaction,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.CreditTransactionWhereUniqueInput,
        include: Optional[types.CreditTransactionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.CreditTransaction,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.CreditTransactionUpdateInput,
        where: types.CreditTransactionWhereUniqueInput,
        include: Optional[types.CreditTransactionInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.CreditTransaction,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.CreditTransactionWhereUniqueInput,
        data: types.CreditTransactionUpsertInput,
        include: Optional[types.CreditTransactionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.CreditTransaction,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.CreditTransactionUpdateManyMutationInput,
        where: types.CreditTransactionWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.CreditTransaction,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.CreditTransactionWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.CreditTransaction,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class CreditRefundRequestBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.CreditRefundRequestCreateInput,
        include: Optional[types.CreditRefundRequestInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.CreditRefundRequest,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.CreditRefundRequestCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.CreditRefundRequest,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.CreditRefundRequestWhereUniqueInput,
        include: Optional[types.CreditRefundRequestInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.CreditRefundRequest,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.CreditRefundRequestUpdateInput,
        where: types.CreditRefundRequestWhereUniqueInput,
        include: Optional[types.CreditRefundRequestInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.CreditRefundRequest,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.CreditRefundRequestWhereUniqueInput,
        data: types.CreditRefundRequestUpsertInput,
        include: Optional[types.CreditRefundRequestInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.CreditRefundRequest,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.CreditRefundRequestUpdateManyMutationInput,
        where: types.CreditRefundRequestWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.CreditRefundRequest,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.CreditRefundRequestWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.CreditRefundRequest,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class ProfileBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.ProfileCreateInput,
        include: Optional[types.ProfileInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.Profile,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.ProfileCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.Profile,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.ProfileWhereUniqueInput,
        include: Optional[types.ProfileInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.Profile,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.ProfileUpdateInput,
        where: types.ProfileWhereUniqueInput,
        include: Optional[types.ProfileInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.Profile,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.ProfileWhereUniqueInput,
        data: types.ProfileUpsertInput,
        include: Optional[types.ProfileInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.Profile,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.ProfileUpdateManyMutationInput,
        where: types.ProfileWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.Profile,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.ProfileWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.Profile,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class StoreListingBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.StoreListingCreateInput,
        include: Optional[types.StoreListingInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.StoreListing,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.StoreListingCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.StoreListing,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.StoreListingWhereUniqueInput,
        include: Optional[types.StoreListingInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.StoreListing,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.StoreListingUpdateInput,
        where: types.StoreListingWhereUniqueInput,
        include: Optional[types.StoreListingInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.StoreListing,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.StoreListingWhereUniqueInput,
        data: types.StoreListingUpsertInput,
        include: Optional[types.StoreListingInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.StoreListing,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.StoreListingUpdateManyMutationInput,
        where: types.StoreListingWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.StoreListing,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.StoreListingWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.StoreListing,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class StoreListingVersionBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.StoreListingVersionCreateInput,
        include: Optional[types.StoreListingVersionInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.StoreListingVersion,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.StoreListingVersionCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.StoreListingVersion,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.StoreListingVersionWhereUniqueInput,
        include: Optional[types.StoreListingVersionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.StoreListingVersion,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.StoreListingVersionUpdateInput,
        where: types.StoreListingVersionWhereUniqueInput,
        include: Optional[types.StoreListingVersionInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.StoreListingVersion,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.StoreListingVersionWhereUniqueInput,
        data: types.StoreListingVersionUpsertInput,
        include: Optional[types.StoreListingVersionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.StoreListingVersion,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.StoreListingVersionUpdateManyMutationInput,
        where: types.StoreListingVersionWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.StoreListingVersion,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.StoreListingVersionWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.StoreListingVersion,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class StoreListingReviewBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.StoreListingReviewCreateInput,
        include: Optional[types.StoreListingReviewInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.StoreListingReview,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.StoreListingReviewCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.StoreListingReview,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.StoreListingReviewWhereUniqueInput,
        include: Optional[types.StoreListingReviewInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.StoreListingReview,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.StoreListingReviewUpdateInput,
        where: types.StoreListingReviewWhereUniqueInput,
        include: Optional[types.StoreListingReviewInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.StoreListingReview,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.StoreListingReviewWhereUniqueInput,
        data: types.StoreListingReviewUpsertInput,
        include: Optional[types.StoreListingReviewInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.StoreListingReview,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.StoreListingReviewUpdateManyMutationInput,
        where: types.StoreListingReviewWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.StoreListingReview,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.StoreListingReviewWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.StoreListingReview,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class APIKeyBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.APIKeyCreateInput,
        include: Optional[types.APIKeyInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.APIKey,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.APIKeyCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.APIKey,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.APIKeyWhereUniqueInput,
        include: Optional[types.APIKeyInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.APIKey,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.APIKeyUpdateInput,
        where: types.APIKeyWhereUniqueInput,
        include: Optional[types.APIKeyInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.APIKey,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.APIKeyWhereUniqueInput,
        data: types.APIKeyUpsertInput,
        include: Optional[types.APIKeyInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.APIKey,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.APIKeyUpdateManyMutationInput,
        where: types.APIKeyWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.APIKey,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.APIKeyWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.APIKey,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class CreatorBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.CreatorCreateInput,
        include: Optional[types.CreatorInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.Creator,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.CreatorCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.Creator,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.CreatorWhereUniqueInput,
        include: Optional[types.CreatorInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.Creator,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.CreatorUpdateInput,
        where: types.CreatorWhereUniqueInput,
        include: Optional[types.CreatorInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.Creator,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.CreatorWhereUniqueInput,
        data: types.CreatorUpsertInput,
        include: Optional[types.CreatorInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.Creator,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.CreatorUpdateManyMutationInput,
        where: types.CreatorWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.Creator,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.CreatorWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.Creator,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class StoreAgentBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.StoreAgentCreateInput,
        include: Optional[types.StoreAgentInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.StoreAgent,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.StoreAgentCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.StoreAgent,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.StoreAgentWhereUniqueInput,
        include: Optional[types.StoreAgentInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.StoreAgent,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.StoreAgentUpdateInput,
        where: types.StoreAgentWhereUniqueInput,
        include: Optional[types.StoreAgentInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.StoreAgent,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.StoreAgentWhereUniqueInput,
        data: types.StoreAgentUpsertInput,
        include: Optional[types.StoreAgentInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.StoreAgent,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.StoreAgentUpdateManyMutationInput,
        where: types.StoreAgentWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.StoreAgent,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.StoreAgentWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.StoreAgent,
            arguments={'where': where},
            root_selection=['count'],
        )



# NOTE: some arguments are meaningless in this context but are included
# for completeness sake
class StoreSubmissionBatchActions:
    def __init__(self, batcher: Batch) -> None:
        self._batcher = batcher

    def create(
        self,
        data: types.StoreSubmissionCreateInput,
        include: Optional[types.StoreSubmissionInclude] = None
    ) -> None:
        self._batcher._add(
            method='create',
            model=models.StoreSubmission,
            arguments={
                'data': data,
                'include': include,
            },
        )

    def create_many(
        self,
        data: List[types.StoreSubmissionCreateWithoutRelationsInput],
        *,
        skip_duplicates: Optional[bool] = None,
    ) -> None:
        if skip_duplicates and self._batcher._active_provider in CREATE_MANY_SKIP_DUPLICATES_UNSUPPORTED:
            raise errors.UnsupportedDatabaseError(self._batcher._active_provider, 'create_many_skip_duplicates')

        self._batcher._add(
            method='create_many',
            model=models.StoreSubmission,
            arguments={
                'data': data,
                'skipDuplicates': skip_duplicates,
            },
            root_selection=['count'],
        )

    def delete(
        self,
        where: types.StoreSubmissionWhereUniqueInput,
        include: Optional[types.StoreSubmissionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='delete',
            model=models.StoreSubmission,
            arguments={
                'where': where,
                'include': include,
            },
        )

    def update(
        self,
        data: types.StoreSubmissionUpdateInput,
        where: types.StoreSubmissionWhereUniqueInput,
        include: Optional[types.StoreSubmissionInclude] = None
    ) -> None:
        self._batcher._add(
            method='update',
            model=models.StoreSubmission,
            arguments={
                'data': data,
                'where': where,
                'include': include,
            },
        )

    def upsert(
        self,
        where: types.StoreSubmissionWhereUniqueInput,
        data: types.StoreSubmissionUpsertInput,
        include: Optional[types.StoreSubmissionInclude] = None,
    ) -> None:
        self._batcher._add(
            method='upsert',
            model=models.StoreSubmission,
            arguments={
                'where': where,
                'include': include,
                'create': data.get('create'),
                'update': data.get('update'),
            },
        )

    def update_many(
        self,
        data: types.StoreSubmissionUpdateManyMutationInput,
        where: types.StoreSubmissionWhereInput,
    ) -> None:
        self._batcher._add(
            method='update_many',
            model=models.StoreSubmission,
            arguments={'data': data, 'where': where,},
            root_selection=['count'],
        )

    def delete_many(
        self,
        where: Optional[types.StoreSubmissionWhereInput] = None,
    ) -> None:
        self._batcher._add(
            method='delete_many',
            model=models.StoreSubmission,
            arguments={'where': where},
            root_selection=['count'],
        )



Client = Prisma