# -*- coding: utf-8 -*-
# code generated by Prisma. DO NOT EDIT.
# pyright: reportUnusedImport=false
# fmt: off

# global imports for type checking
from builtins import bool as _bool
from builtins import int as _int
from builtins import float as _float
from builtins import str as _str
import sys
import decimal
import datetime
from typing import (
    TYPE_CHECKING,
    Optional,
    Iterable,
    Iterator,
    Sequence,
    Callable,
    ClassVar,
    NoReturn,
    TypeVar,
    Generic,
    Mapping,
    Tuple,
    Union,
    List,
    Dict,
    Type,
    Any,
    Set,
    overload,
    cast,
)
from typing_extensions import TypedDict, Literal


from typing_extensions import LiteralString
# -- template models.py.jinja --
from pydantic import BaseModel

from . import fields, actions
from ._types import FuncType
from ._builder import serialize_base64
from ._compat import PYDANTIC_V2, ConfigDict

if TYPE_CHECKING:
    from .client import Prisma


_PrismaModelT = TypeVar('_PrismaModelT', bound='_PrismaModel')


class _PrismaModel(BaseModel):
    if PYDANTIC_V2:
        model_config: ClassVar[ConfigDict] = ConfigDict(
            use_enum_values=True,
            arbitrary_types_allowed=True,
            populate_by_name=True,
        )
    elif not TYPE_CHECKING:
        from ._compat import BaseConfig

        class Config(BaseConfig):
            use_enum_values: bool = True
            arbitrary_types_allowed: bool = True
            allow_population_by_field_name: bool = True
            json_encoders: Dict[Any, FuncType] = {
                fields.Base64: serialize_base64,
            }

    # TODO: ensure this is required by subclasses
    __prisma_model__: ClassVar[str]


class BaseUser(_PrismaModel):
    __prisma_model__: ClassVar[Literal['User']] = 'User'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.UserActions[_PrismaModelT]':
        from .client import get_client

        return actions.UserActions[_PrismaModelT](client or get_client(), cls)


class BaseUserOnboarding(_PrismaModel):
    __prisma_model__: ClassVar[Literal['UserOnboarding']] = 'UserOnboarding'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.UserOnboardingActions[_PrismaModelT]':
        from .client import get_client

        return actions.UserOnboardingActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentGraph(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentGraph']] = 'AgentGraph'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentGraphActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentGraphActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentPreset(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentPreset']] = 'AgentPreset'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentPresetActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentPresetActions[_PrismaModelT](client or get_client(), cls)


class BaseNotificationEvent(_PrismaModel):
    __prisma_model__: ClassVar[Literal['NotificationEvent']] = 'NotificationEvent'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.NotificationEventActions[_PrismaModelT]':
        from .client import get_client

        return actions.NotificationEventActions[_PrismaModelT](client or get_client(), cls)


class BaseUserNotificationBatch(_PrismaModel):
    __prisma_model__: ClassVar[Literal['UserNotificationBatch']] = 'UserNotificationBatch'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.UserNotificationBatchActions[_PrismaModelT]':
        from .client import get_client

        return actions.UserNotificationBatchActions[_PrismaModelT](client or get_client(), cls)


class BaseLibraryAgent(_PrismaModel):
    __prisma_model__: ClassVar[Literal['LibraryAgent']] = 'LibraryAgent'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.LibraryAgentActions[_PrismaModelT]':
        from .client import get_client

        return actions.LibraryAgentActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentNode(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentNode']] = 'AgentNode'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentNodeActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentNodeActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentNodeLink(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentNodeLink']] = 'AgentNodeLink'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentNodeLinkActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentNodeLinkActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentBlock(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentBlock']] = 'AgentBlock'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentBlockActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentBlockActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentGraphExecution(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentGraphExecution']] = 'AgentGraphExecution'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentGraphExecutionActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentGraphExecutionActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentNodeExecution(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentNodeExecution']] = 'AgentNodeExecution'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentNodeExecutionActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentNodeExecutionActions[_PrismaModelT](client or get_client(), cls)


class BaseAgentNodeExecutionInputOutput(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AgentNodeExecutionInputOutput']] = 'AgentNodeExecutionInputOutput'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AgentNodeExecutionInputOutputActions[_PrismaModelT]':
        from .client import get_client

        return actions.AgentNodeExecutionInputOutputActions[_PrismaModelT](client or get_client(), cls)


class BaseIntegrationWebhook(_PrismaModel):
    __prisma_model__: ClassVar[Literal['IntegrationWebhook']] = 'IntegrationWebhook'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.IntegrationWebhookActions[_PrismaModelT]':
        from .client import get_client

        return actions.IntegrationWebhookActions[_PrismaModelT](client or get_client(), cls)


class BaseAnalyticsDetails(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AnalyticsDetails']] = 'AnalyticsDetails'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AnalyticsDetailsActions[_PrismaModelT]':
        from .client import get_client

        return actions.AnalyticsDetailsActions[_PrismaModelT](client or get_client(), cls)


class BaseAnalyticsMetrics(_PrismaModel):
    __prisma_model__: ClassVar[Literal['AnalyticsMetrics']] = 'AnalyticsMetrics'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.AnalyticsMetricsActions[_PrismaModelT]':
        from .client import get_client

        return actions.AnalyticsMetricsActions[_PrismaModelT](client or get_client(), cls)


class BaseCreditTransaction(_PrismaModel):
    __prisma_model__: ClassVar[Literal['CreditTransaction']] = 'CreditTransaction'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.CreditTransactionActions[_PrismaModelT]':
        from .client import get_client

        return actions.CreditTransactionActions[_PrismaModelT](client or get_client(), cls)


class BaseCreditRefundRequest(_PrismaModel):
    __prisma_model__: ClassVar[Literal['CreditRefundRequest']] = 'CreditRefundRequest'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.CreditRefundRequestActions[_PrismaModelT]':
        from .client import get_client

        return actions.CreditRefundRequestActions[_PrismaModelT](client or get_client(), cls)


class BaseProfile(_PrismaModel):
    __prisma_model__: ClassVar[Literal['Profile']] = 'Profile'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.ProfileActions[_PrismaModelT]':
        from .client import get_client

        return actions.ProfileActions[_PrismaModelT](client or get_client(), cls)


class BaseStoreListing(_PrismaModel):
    __prisma_model__: ClassVar[Literal['StoreListing']] = 'StoreListing'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.StoreListingActions[_PrismaModelT]':
        from .client import get_client

        return actions.StoreListingActions[_PrismaModelT](client or get_client(), cls)


class BaseStoreListingVersion(_PrismaModel):
    __prisma_model__: ClassVar[Literal['StoreListingVersion']] = 'StoreListingVersion'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.StoreListingVersionActions[_PrismaModelT]':
        from .client import get_client

        return actions.StoreListingVersionActions[_PrismaModelT](client or get_client(), cls)


class BaseStoreListingReview(_PrismaModel):
    __prisma_model__: ClassVar[Literal['StoreListingReview']] = 'StoreListingReview'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.StoreListingReviewActions[_PrismaModelT]':
        from .client import get_client

        return actions.StoreListingReviewActions[_PrismaModelT](client or get_client(), cls)


class BaseAPIKey(_PrismaModel):
    __prisma_model__: ClassVar[Literal['APIKey']] = 'APIKey'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.APIKeyActions[_PrismaModelT]':
        from .client import get_client

        return actions.APIKeyActions[_PrismaModelT](client or get_client(), cls)


class BaseCreator(_PrismaModel):
    __prisma_model__: ClassVar[Literal['Creator']] = 'Creator'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.CreatorActions[_PrismaModelT]':
        from .client import get_client

        return actions.CreatorActions[_PrismaModelT](client or get_client(), cls)


class BaseStoreAgent(_PrismaModel):
    __prisma_model__: ClassVar[Literal['StoreAgent']] = 'StoreAgent'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.StoreAgentActions[_PrismaModelT]':
        from .client import get_client

        return actions.StoreAgentActions[_PrismaModelT](client or get_client(), cls)


class BaseStoreSubmission(_PrismaModel):
    __prisma_model__: ClassVar[Literal['StoreSubmission']] = 'StoreSubmission'  # pyright: ignore[reportIncompatibleVariableOverride]

    @classmethod
    def prisma(cls: Type[_PrismaModelT], client: Optional['Prisma'] = None) -> 'actions.StoreSubmissionActions[_PrismaModelT]':
        from .client import get_client

        return actions.StoreSubmissionActions[_PrismaModelT](client or get_client(), cls)


