# -*- coding: utf-8 -*-
# code generated by Prisma. DO NOT EDIT.
# pyright: reportUnusedImport=false
# fmt: off

# global imports for type checking
from builtins import bool as _bool
from builtins import int as _int
from builtins import float as _float
from builtins import str as _str
import sys
import decimal
import datetime
from typing import (
    TYPE_CHECKING,
    Optional,
    Iterable,
    Iterator,
    Sequence,
    Callable,
    ClassVar,
    NoReturn,
    TypeVar,
    Generic,
    Mapping,
    Tuple,
    Union,
    List,
    Dict,
    Type,
    Any,
    Set,
    overload,
    cast,
)
from typing_extensions import TypedDict, Literal


from typing_extensions import LiteralString
# -- template models.py.jinja --
import os
import logging
import inspect
import warnings
from collections import OrderedDict

from pydantic import BaseModel, Field

from . import types, enums, errors, fields, bases
from ._types import FuncType
from ._compat import model_rebuild, field_validator
from ._builder import serialize_base64
from .generator import partial_models_ctx, PartialModelField


log: logging.Logger = logging.getLogger(__name__)
_created_partial_types: Set[str] = set()

class User(bases.BaseUser):
    """Represents a User record"""

    id: _str
    email: _str
    emailVerified: _bool
    name: Optional[_str] = None
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    metadata: 'fields.Json'
    integrations: _str
    stripeCustomerId: Optional[_str] = None
    topUpConfig: Optional['fields.Json'] = None
    maxEmailsPerDay: _int
    notifyOnAgentRun: _bool
    notifyOnZeroBalance: _bool
    notifyOnLowBalance: _bool
    notifyOnBlockExecutionFailed: _bool
    notifyOnContinuousAgentError: _bool
    notifyOnDailySummary: _bool
    notifyOnWeeklySummary: _bool
    notifyOnMonthlySummary: _bool
    AgentGraphs: Optional[List['models.AgentGraph']] = None
    AgentGraphExecutions: Optional[List['models.AgentGraphExecution']] = None
    AnalyticsDetails: Optional[List['models.AnalyticsDetails']] = None
    AnalyticsMetrics: Optional[List['models.AnalyticsMetrics']] = None
    CreditTransactions: Optional[List['models.CreditTransaction']] = None
    AgentPresets: Optional[List['models.AgentPreset']] = None
    LibraryAgents: Optional[List['models.LibraryAgent']] = None
    Profile: Optional[List['models.Profile']] = None
    UserOnboarding: Optional['models.UserOnboarding'] = None
    StoreListings: Optional[List['models.StoreListing']] = None
    StoreListingReviews: Optional[List['models.StoreListingReview']] = None
    StoreVersionsReviewed: Optional[List['models.StoreListingVersion']] = None
    APIKeys: Optional[List['models.APIKey']] = None
    IntegrationWebhooks: Optional[List['models.IntegrationWebhook']] = None
    NotificationBatches: Optional[List['models.UserNotificationBatch']] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.UserKeys']] = None,
        exclude: Optional[Iterable['types.UserKeys']] = None,
        required: Optional[Iterable['types.UserKeys']] = None,
        optional: Optional[Iterable['types.UserKeys']] = None,
        relations: Optional[Mapping['types.UserRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.UserKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _User_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _User_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _User_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _User_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _User_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _User_relational_fields:
                        raise errors.UnknownRelationalFieldError('User', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid User / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'User',
            }
        )
        _created_partial_types.add(name)


class UserOnboarding(bases.BaseUserOnboarding):
    """Represents a UserOnboarding record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: Optional[datetime.datetime] = None
    completedSteps: List['enums.OnboardingStep']
    notificationDot: _bool
    notified: List['enums.OnboardingStep']
    rewardedFor: List['enums.OnboardingStep']
    usageReason: Optional[_str] = None
    integrations: List[_str]
    otherIntegrations: Optional[_str] = None
    selectedStoreListingVersionId: Optional[_str] = None
    agentInput: Optional['fields.Json'] = None
    onboardingAgentExecutionId: Optional[_str] = None
    userId: _str
    User: Optional['models.User'] = None


    @field_validator('completedSteps', 'notified', 'rewardedFor', 'integrations', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.UserOnboardingKeys']] = None,
        exclude: Optional[Iterable['types.UserOnboardingKeys']] = None,
        required: Optional[Iterable['types.UserOnboardingKeys']] = None,
        optional: Optional[Iterable['types.UserOnboardingKeys']] = None,
        relations: Optional[Mapping['types.UserOnboardingRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.UserOnboardingKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _UserOnboarding_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _UserOnboarding_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _UserOnboarding_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _UserOnboarding_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _UserOnboarding_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _UserOnboarding_relational_fields:
                        raise errors.UnknownRelationalFieldError('UserOnboarding', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid UserOnboarding / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'UserOnboarding',
            }
        )
        _created_partial_types.add(name)


class AgentGraph(bases.BaseAgentGraph):
    """Represents a AgentGraph record"""

    id: _str
    version: _int
    createdAt: datetime.datetime
    updatedAt: Optional[datetime.datetime] = None
    name: Optional[_str] = None
    description: Optional[_str] = None
    isActive: _bool
    userId: _str
    User: Optional['models.User'] = None
    forkedFromId: Optional[_str] = None
    forkedFromVersion: Optional[_int] = None
    forkedFrom: Optional['models.AgentGraph'] = None
    forks: Optional[List['models.AgentGraph']] = None
    Nodes: Optional[List['models.AgentNode']] = None
    Executions: Optional[List['models.AgentGraphExecution']] = None
    Presets: Optional[List['models.AgentPreset']] = None
    LibraryAgents: Optional[List['models.LibraryAgent']] = None
    StoreListings: Optional[List['models.StoreListing']] = None
    StoreListingVersions: Optional[List['models.StoreListingVersion']] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentGraphKeys']] = None,
        exclude: Optional[Iterable['types.AgentGraphKeys']] = None,
        required: Optional[Iterable['types.AgentGraphKeys']] = None,
        optional: Optional[Iterable['types.AgentGraphKeys']] = None,
        relations: Optional[Mapping['types.AgentGraphRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentGraphKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentGraph_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentGraph_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentGraph_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentGraph_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentGraph_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentGraph_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentGraph', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentGraph / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentGraph',
            }
        )
        _created_partial_types.add(name)


class AgentPreset(bases.BaseAgentPreset):
    """Represents a AgentPreset record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    name: _str
    description: _str
    isActive: _bool
    userId: _str
    User: Optional['models.User'] = None
    agentGraphId: _str
    agentGraphVersion: _int
    AgentGraph: Optional['models.AgentGraph'] = None
    InputPresets: Optional[List['models.AgentNodeExecutionInputOutput']] = None
    Executions: Optional[List['models.AgentGraphExecution']] = None
    isDeleted: _bool



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentPresetKeys']] = None,
        exclude: Optional[Iterable['types.AgentPresetKeys']] = None,
        required: Optional[Iterable['types.AgentPresetKeys']] = None,
        optional: Optional[Iterable['types.AgentPresetKeys']] = None,
        relations: Optional[Mapping['types.AgentPresetRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentPresetKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentPreset_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentPreset_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentPreset_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentPreset_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentPreset_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentPreset_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentPreset', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentPreset / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentPreset',
            }
        )
        _created_partial_types.add(name)


class NotificationEvent(bases.BaseNotificationEvent):
    """Represents a NotificationEvent record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    UserNotificationBatch: Optional['models.UserNotificationBatch'] = None
    userNotificationBatchId: Optional[_str] = None
    type: 'enums.NotificationType'
    data: 'fields.Json'



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.NotificationEventKeys']] = None,
        exclude: Optional[Iterable['types.NotificationEventKeys']] = None,
        required: Optional[Iterable['types.NotificationEventKeys']] = None,
        optional: Optional[Iterable['types.NotificationEventKeys']] = None,
        relations: Optional[Mapping['types.NotificationEventRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.NotificationEventKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _NotificationEvent_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _NotificationEvent_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _NotificationEvent_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _NotificationEvent_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _NotificationEvent_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _NotificationEvent_relational_fields:
                        raise errors.UnknownRelationalFieldError('NotificationEvent', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid NotificationEvent / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'NotificationEvent',
            }
        )
        _created_partial_types.add(name)


class UserNotificationBatch(bases.BaseUserNotificationBatch):
    """Represents a UserNotificationBatch record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    userId: _str
    User: Optional['models.User'] = None
    type: 'enums.NotificationType'
    Notifications: Optional[List['models.NotificationEvent']] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.UserNotificationBatchKeys']] = None,
        exclude: Optional[Iterable['types.UserNotificationBatchKeys']] = None,
        required: Optional[Iterable['types.UserNotificationBatchKeys']] = None,
        optional: Optional[Iterable['types.UserNotificationBatchKeys']] = None,
        relations: Optional[Mapping['types.UserNotificationBatchRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.UserNotificationBatchKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _UserNotificationBatch_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _UserNotificationBatch_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _UserNotificationBatch_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _UserNotificationBatch_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _UserNotificationBatch_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _UserNotificationBatch_relational_fields:
                        raise errors.UnknownRelationalFieldError('UserNotificationBatch', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid UserNotificationBatch / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'UserNotificationBatch',
            }
        )
        _created_partial_types.add(name)


class LibraryAgent(bases.BaseLibraryAgent):
    """Represents a LibraryAgent record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    userId: _str
    User: Optional['models.User'] = None
    imageUrl: Optional[_str] = None
    agentGraphId: _str
    agentGraphVersion: _int
    AgentGraph: Optional['models.AgentGraph'] = None
    creatorId: Optional[_str] = None
    Creator: Optional['models.Profile'] = None
    useGraphIsActiveVersion: _bool
    isFavorite: _bool
    isCreatedByUser: _bool
    isArchived: _bool
    isDeleted: _bool



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.LibraryAgentKeys']] = None,
        exclude: Optional[Iterable['types.LibraryAgentKeys']] = None,
        required: Optional[Iterable['types.LibraryAgentKeys']] = None,
        optional: Optional[Iterable['types.LibraryAgentKeys']] = None,
        relations: Optional[Mapping['types.LibraryAgentRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.LibraryAgentKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _LibraryAgent_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _LibraryAgent_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _LibraryAgent_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _LibraryAgent_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _LibraryAgent_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _LibraryAgent_relational_fields:
                        raise errors.UnknownRelationalFieldError('LibraryAgent', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid LibraryAgent / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'LibraryAgent',
            }
        )
        _created_partial_types.add(name)


class AgentNode(bases.BaseAgentNode):
    """Represents a AgentNode record"""

    id: _str
    agentBlockId: _str
    AgentBlock: Optional['models.AgentBlock'] = None
    agentGraphId: _str
    agentGraphVersion: _int
    AgentGraph: Optional['models.AgentGraph'] = None
    Input: Optional[List['models.AgentNodeLink']] = None
    Output: Optional[List['models.AgentNodeLink']] = None
    constantInput: 'fields.Json'
    webhookId: Optional[_str] = None
    Webhook: Optional['models.IntegrationWebhook'] = None
    metadata: 'fields.Json'
    Executions: Optional[List['models.AgentNodeExecution']] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentNodeKeys']] = None,
        exclude: Optional[Iterable['types.AgentNodeKeys']] = None,
        required: Optional[Iterable['types.AgentNodeKeys']] = None,
        optional: Optional[Iterable['types.AgentNodeKeys']] = None,
        relations: Optional[Mapping['types.AgentNodeRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentNodeKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentNode_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentNode_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentNode_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentNode_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentNode_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentNode_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentNode', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentNode / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentNode',
            }
        )
        _created_partial_types.add(name)


class AgentNodeLink(bases.BaseAgentNodeLink):
    """Represents a AgentNodeLink record"""

    id: _str
    agentNodeSourceId: _str
    AgentNodeSource: Optional['models.AgentNode'] = None
    sourceName: _str
    agentNodeSinkId: _str
    AgentNodeSink: Optional['models.AgentNode'] = None
    sinkName: _str
    isStatic: _bool



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentNodeLinkKeys']] = None,
        exclude: Optional[Iterable['types.AgentNodeLinkKeys']] = None,
        required: Optional[Iterable['types.AgentNodeLinkKeys']] = None,
        optional: Optional[Iterable['types.AgentNodeLinkKeys']] = None,
        relations: Optional[Mapping['types.AgentNodeLinkRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentNodeLinkKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentNodeLink_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentNodeLink_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentNodeLink_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentNodeLink_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentNodeLink_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentNodeLink_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentNodeLink', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentNodeLink / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentNodeLink',
            }
        )
        _created_partial_types.add(name)


class AgentBlock(bases.BaseAgentBlock):
    """Represents a AgentBlock record"""

    id: _str
    name: _str
    inputSchema: _str
    outputSchema: _str
    ReferencedByAgentNode: Optional[List['models.AgentNode']] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentBlockKeys']] = None,
        exclude: Optional[Iterable['types.AgentBlockKeys']] = None,
        required: Optional[Iterable['types.AgentBlockKeys']] = None,
        optional: Optional[Iterable['types.AgentBlockKeys']] = None,
        relations: Optional[Mapping['types.AgentBlockRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentBlockKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentBlock_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentBlock_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentBlock_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentBlock_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentBlock_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentBlock_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentBlock', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentBlock / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentBlock',
            }
        )
        _created_partial_types.add(name)


class AgentGraphExecution(bases.BaseAgentGraphExecution):
    """Represents a AgentGraphExecution record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: Optional[datetime.datetime] = None
    startedAt: Optional[datetime.datetime] = None
    isDeleted: _bool
    executionStatus: 'enums.AgentExecutionStatus'
    agentGraphId: _str
    agentGraphVersion: _int
    AgentGraph: Optional['models.AgentGraph'] = None
    NodeExecutions: Optional[List['models.AgentNodeExecution']] = None
    userId: _str
    User: Optional['models.User'] = None
    stats: Optional['fields.Json'] = None
    agentPresetId: Optional[_str] = None
    AgentPreset: Optional['models.AgentPreset'] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentGraphExecutionKeys']] = None,
        exclude: Optional[Iterable['types.AgentGraphExecutionKeys']] = None,
        required: Optional[Iterable['types.AgentGraphExecutionKeys']] = None,
        optional: Optional[Iterable['types.AgentGraphExecutionKeys']] = None,
        relations: Optional[Mapping['types.AgentGraphExecutionRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentGraphExecutionKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentGraphExecution_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentGraphExecution_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentGraphExecution_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentGraphExecution_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentGraphExecution_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentGraphExecution_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentGraphExecution', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentGraphExecution / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentGraphExecution',
            }
        )
        _created_partial_types.add(name)


class AgentNodeExecution(bases.BaseAgentNodeExecution):
    """Represents a AgentNodeExecution record"""

    id: _str
    agentGraphExecutionId: _str
    GraphExecution: Optional['models.AgentGraphExecution'] = None
    agentNodeId: _str
    Node: Optional['models.AgentNode'] = None
    Input: Optional[List['models.AgentNodeExecutionInputOutput']] = None
    Output: Optional[List['models.AgentNodeExecutionInputOutput']] = None
    executionStatus: 'enums.AgentExecutionStatus'
    executionData: Optional['fields.Json'] = None
    addedTime: datetime.datetime
    queuedTime: Optional[datetime.datetime] = None
    startedTime: Optional[datetime.datetime] = None
    endedTime: Optional[datetime.datetime] = None
    stats: Optional['fields.Json'] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentNodeExecutionKeys']] = None,
        exclude: Optional[Iterable['types.AgentNodeExecutionKeys']] = None,
        required: Optional[Iterable['types.AgentNodeExecutionKeys']] = None,
        optional: Optional[Iterable['types.AgentNodeExecutionKeys']] = None,
        relations: Optional[Mapping['types.AgentNodeExecutionRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentNodeExecutionKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentNodeExecution_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentNodeExecution_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentNodeExecution_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentNodeExecution_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentNodeExecution_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentNodeExecution_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentNodeExecution', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentNodeExecution / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentNodeExecution',
            }
        )
        _created_partial_types.add(name)


class AgentNodeExecutionInputOutput(bases.BaseAgentNodeExecutionInputOutput):
    """Represents a AgentNodeExecutionInputOutput record"""

    id: _str
    name: _str
    data: 'fields.Json'
    time: datetime.datetime
    referencedByInputExecId: Optional[_str] = None
    ReferencedByInputExec: Optional['models.AgentNodeExecution'] = None
    referencedByOutputExecId: Optional[_str] = None
    ReferencedByOutputExec: Optional['models.AgentNodeExecution'] = None
    agentPresetId: Optional[_str] = None
    AgentPreset: Optional['models.AgentPreset'] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AgentNodeExecutionInputOutputKeys']] = None,
        exclude: Optional[Iterable['types.AgentNodeExecutionInputOutputKeys']] = None,
        required: Optional[Iterable['types.AgentNodeExecutionInputOutputKeys']] = None,
        optional: Optional[Iterable['types.AgentNodeExecutionInputOutputKeys']] = None,
        relations: Optional[Mapping['types.AgentNodeExecutionInputOutputRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AgentNodeExecutionInputOutputKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AgentNodeExecutionInputOutput_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AgentNodeExecutionInputOutput_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AgentNodeExecutionInputOutput_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AgentNodeExecutionInputOutput_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AgentNodeExecutionInputOutput_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AgentNodeExecutionInputOutput_relational_fields:
                        raise errors.UnknownRelationalFieldError('AgentNodeExecutionInputOutput', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AgentNodeExecutionInputOutput / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AgentNodeExecutionInputOutput',
            }
        )
        _created_partial_types.add(name)


class IntegrationWebhook(bases.BaseIntegrationWebhook):
    """Represents a IntegrationWebhook record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: Optional[datetime.datetime] = None
    userId: _str
    User: Optional['models.User'] = None
    provider: _str
    credentialsId: _str
    webhookType: _str
    resource: _str
    events: List[_str]
    config: 'fields.Json'
    secret: _str
    providerWebhookId: _str
    AgentNodes: Optional[List['models.AgentNode']] = None


    @field_validator('events', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.IntegrationWebhookKeys']] = None,
        exclude: Optional[Iterable['types.IntegrationWebhookKeys']] = None,
        required: Optional[Iterable['types.IntegrationWebhookKeys']] = None,
        optional: Optional[Iterable['types.IntegrationWebhookKeys']] = None,
        relations: Optional[Mapping['types.IntegrationWebhookRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.IntegrationWebhookKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _IntegrationWebhook_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _IntegrationWebhook_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _IntegrationWebhook_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _IntegrationWebhook_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _IntegrationWebhook_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _IntegrationWebhook_relational_fields:
                        raise errors.UnknownRelationalFieldError('IntegrationWebhook', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid IntegrationWebhook / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'IntegrationWebhook',
            }
        )
        _created_partial_types.add(name)


class AnalyticsDetails(bases.BaseAnalyticsDetails):
    """Represents a AnalyticsDetails record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    userId: _str
    User: Optional['models.User'] = None
    type: _str
    data: Optional['fields.Json'] = None
    dataIndex: Optional[_str] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AnalyticsDetailsKeys']] = None,
        exclude: Optional[Iterable['types.AnalyticsDetailsKeys']] = None,
        required: Optional[Iterable['types.AnalyticsDetailsKeys']] = None,
        optional: Optional[Iterable['types.AnalyticsDetailsKeys']] = None,
        relations: Optional[Mapping['types.AnalyticsDetailsRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AnalyticsDetailsKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AnalyticsDetails_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AnalyticsDetails_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AnalyticsDetails_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AnalyticsDetails_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AnalyticsDetails_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AnalyticsDetails_relational_fields:
                        raise errors.UnknownRelationalFieldError('AnalyticsDetails', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AnalyticsDetails / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AnalyticsDetails',
            }
        )
        _created_partial_types.add(name)


class AnalyticsMetrics(bases.BaseAnalyticsMetrics):
    """/////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////
    ///////////   METRICS TRACKING TABLES    ////////////////
    /////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////
    """

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    analyticMetric: _str
    value: _float
    dataString: Optional[_str] = None
    userId: _str
    User: Optional['models.User'] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.AnalyticsMetricsKeys']] = None,
        exclude: Optional[Iterable['types.AnalyticsMetricsKeys']] = None,
        required: Optional[Iterable['types.AnalyticsMetricsKeys']] = None,
        optional: Optional[Iterable['types.AnalyticsMetricsKeys']] = None,
        relations: Optional[Mapping['types.AnalyticsMetricsRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.AnalyticsMetricsKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _AnalyticsMetrics_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _AnalyticsMetrics_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _AnalyticsMetrics_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _AnalyticsMetrics_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _AnalyticsMetrics_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _AnalyticsMetrics_relational_fields:
                        raise errors.UnknownRelationalFieldError('AnalyticsMetrics', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid AnalyticsMetrics / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'AnalyticsMetrics',
            }
        )
        _created_partial_types.add(name)


class CreditTransaction(bases.BaseCreditTransaction):
    """Represents a CreditTransaction record"""

    transactionKey: _str
    createdAt: datetime.datetime
    userId: _str
    User: Optional['models.User'] = None
    amount: _int
    type: 'enums.CreditTransactionType'
    runningBalance: Optional[_int] = None
    isActive: _bool
    metadata: Optional['fields.Json'] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.CreditTransactionKeys']] = None,
        exclude: Optional[Iterable['types.CreditTransactionKeys']] = None,
        required: Optional[Iterable['types.CreditTransactionKeys']] = None,
        optional: Optional[Iterable['types.CreditTransactionKeys']] = None,
        relations: Optional[Mapping['types.CreditTransactionRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.CreditTransactionKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _CreditTransaction_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _CreditTransaction_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _CreditTransaction_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _CreditTransaction_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _CreditTransaction_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _CreditTransaction_relational_fields:
                        raise errors.UnknownRelationalFieldError('CreditTransaction', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid CreditTransaction / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'CreditTransaction',
            }
        )
        _created_partial_types.add(name)


class CreditRefundRequest(bases.BaseCreditRefundRequest):
    """Represents a CreditRefundRequest record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    userId: _str
    transactionKey: _str
    amount: _int
    reason: _str
    result: Optional[_str] = None
    status: 'enums.CreditRefundRequestStatus'



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.CreditRefundRequestKeys']] = None,
        exclude: Optional[Iterable['types.CreditRefundRequestKeys']] = None,
        required: Optional[Iterable['types.CreditRefundRequestKeys']] = None,
        optional: Optional[Iterable['types.CreditRefundRequestKeys']] = None,
        relations: Optional[Mapping['types.CreditRefundRequestRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.CreditRefundRequestKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _CreditRefundRequest_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _CreditRefundRequest_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _CreditRefundRequest_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _CreditRefundRequest_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True


            if relations:
                raise ValueError('Model: "CreditRefundRequest" has no relational fields.')
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid CreditRefundRequest / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'CreditRefundRequest',
            }
        )
        _created_partial_types.add(name)


class Profile(bases.BaseProfile):
    """Represents a Profile record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    userId: Optional[_str] = None
    User: Optional['models.User'] = None
    name: _str
    username: _str
    description: _str
    links: List[_str]
    avatarUrl: Optional[_str] = None
    isFeatured: _bool
    LibraryAgents: Optional[List['models.LibraryAgent']] = None


    @field_validator('links', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.ProfileKeys']] = None,
        exclude: Optional[Iterable['types.ProfileKeys']] = None,
        required: Optional[Iterable['types.ProfileKeys']] = None,
        optional: Optional[Iterable['types.ProfileKeys']] = None,
        relations: Optional[Mapping['types.ProfileRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.ProfileKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _Profile_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _Profile_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _Profile_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _Profile_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _Profile_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _Profile_relational_fields:
                        raise errors.UnknownRelationalFieldError('Profile', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid Profile / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'Profile',
            }
        )
        _created_partial_types.add(name)


class StoreListing(bases.BaseStoreListing):
    """Represents a StoreListing record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    isDeleted: _bool
    hasApprovedVersion: _bool
    slug: _str
    activeVersionId: Optional[_str] = None
    ActiveVersion: Optional['models.StoreListingVersion'] = None
    agentGraphId: _str
    agentGraphVersion: _int
    AgentGraph: Optional['models.AgentGraph'] = None
    owningUserId: _str
    OwningUser: Optional['models.User'] = None
    Versions: Optional[List['models.StoreListingVersion']] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.StoreListingKeys']] = None,
        exclude: Optional[Iterable['types.StoreListingKeys']] = None,
        required: Optional[Iterable['types.StoreListingKeys']] = None,
        optional: Optional[Iterable['types.StoreListingKeys']] = None,
        relations: Optional[Mapping['types.StoreListingRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.StoreListingKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _StoreListing_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _StoreListing_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _StoreListing_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _StoreListing_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _StoreListing_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _StoreListing_relational_fields:
                        raise errors.UnknownRelationalFieldError('StoreListing', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid StoreListing / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'StoreListing',
            }
        )
        _created_partial_types.add(name)


class StoreListingVersion(bases.BaseStoreListingVersion):
    """Represents a StoreListingVersion record"""

    id: _str
    version: _int
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    agentGraphId: _str
    agentGraphVersion: _int
    AgentGraph: Optional['models.AgentGraph'] = None
    name: _str
    subHeading: _str
    videoUrl: Optional[_str] = None
    imageUrls: List[_str]
    description: _str
    categories: List[_str]
    isFeatured: _bool
    isDeleted: _bool
    isAvailable: _bool
    submissionStatus: 'enums.SubmissionStatus'
    submittedAt: Optional[datetime.datetime] = None
    storeListingId: _str
    StoreListing: Optional['models.StoreListing'] = None
    ActiveFor: Optional['models.StoreListing'] = None
    changesSummary: Optional[_str] = None
    reviewerId: Optional[_str] = None
    Reviewer: Optional['models.User'] = None
    internalComments: Optional[_str] = None
    reviewComments: Optional[_str] = None
    reviewedAt: Optional[datetime.datetime] = None
    Reviews: Optional[List['models.StoreListingReview']] = None


    @field_validator('imageUrls', 'categories', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.StoreListingVersionKeys']] = None,
        exclude: Optional[Iterable['types.StoreListingVersionKeys']] = None,
        required: Optional[Iterable['types.StoreListingVersionKeys']] = None,
        optional: Optional[Iterable['types.StoreListingVersionKeys']] = None,
        relations: Optional[Mapping['types.StoreListingVersionRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.StoreListingVersionKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _StoreListingVersion_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _StoreListingVersion_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _StoreListingVersion_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _StoreListingVersion_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _StoreListingVersion_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _StoreListingVersion_relational_fields:
                        raise errors.UnknownRelationalFieldError('StoreListingVersion', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid StoreListingVersion / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'StoreListingVersion',
            }
        )
        _created_partial_types.add(name)


class StoreListingReview(bases.BaseStoreListingReview):
    """Represents a StoreListingReview record"""

    id: _str
    createdAt: datetime.datetime
    updatedAt: datetime.datetime
    storeListingVersionId: _str
    StoreListingVersion: Optional['models.StoreListingVersion'] = None
    reviewByUserId: _str
    ReviewByUser: Optional['models.User'] = None
    score: _int
    comments: Optional[_str] = None



    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.StoreListingReviewKeys']] = None,
        exclude: Optional[Iterable['types.StoreListingReviewKeys']] = None,
        required: Optional[Iterable['types.StoreListingReviewKeys']] = None,
        optional: Optional[Iterable['types.StoreListingReviewKeys']] = None,
        relations: Optional[Mapping['types.StoreListingReviewRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.StoreListingReviewKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _StoreListingReview_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _StoreListingReview_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _StoreListingReview_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _StoreListingReview_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _StoreListingReview_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _StoreListingReview_relational_fields:
                        raise errors.UnknownRelationalFieldError('StoreListingReview', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid StoreListingReview / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'StoreListingReview',
            }
        )
        _created_partial_types.add(name)


class APIKey(bases.BaseAPIKey):
    """Represents a APIKey record"""

    id: _str
    name: _str
    prefix: _str
    postfix: _str
    key: _str
    status: 'enums.APIKeyStatus'
    permissions: List['enums.APIKeyPermission']
    createdAt: datetime.datetime
    lastUsedAt: Optional[datetime.datetime] = None
    revokedAt: Optional[datetime.datetime] = None
    description: Optional[_str] = None
    userId: _str
    User: Optional['models.User'] = None


    @field_validator('permissions', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.APIKeyKeys']] = None,
        exclude: Optional[Iterable['types.APIKeyKeys']] = None,
        required: Optional[Iterable['types.APIKeyKeys']] = None,
        optional: Optional[Iterable['types.APIKeyKeys']] = None,
        relations: Optional[Mapping['types.APIKeyRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.APIKeyKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _APIKey_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _APIKey_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _APIKey_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _APIKey_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True

            if exclude_relational_fields:
                fields = {
                    key: data
                    for key, data in fields.items()
                    if key not in _APIKey_relational_fields
                }

            if relations:
                for field, type_ in relations.items():
                    if field not in _APIKey_relational_fields:
                        raise errors.UnknownRelationalFieldError('APIKey', field)

                    # TODO: this method of validating types is not ideal
                    # as it means we cannot two create partial types that
                    # reference each other
                    if type_ not in _created_partial_types:
                        raise ValueError(
                            f'Unknown partial type: "{type_}". '
                            f'Did you remember to generate the {type_} type before this one?'
                        )

                    # TODO: support non prisma.partials models
                    info = fields[field]
                    if info['is_list']:
                        info['type'] = f'List[\'partials.{type_}\']'
                    else:
                        info['type'] = f'\'partials.{type_}\''
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid APIKey / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'APIKey',
            }
        )
        _created_partial_types.add(name)


class Creator(bases.BaseCreator):
    """Represents a Creator record"""

    username: _str
    name: _str
    avatar_url: _str
    description: _str
    top_categories: List[_str]
    links: List[_str]
    num_agents: _int
    agent_rating: _float
    agent_runs: _int
    is_featured: _bool


    @field_validator('top_categories', 'links', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.CreatorKeys']] = None,
        exclude: Optional[Iterable['types.CreatorKeys']] = None,
        required: Optional[Iterable['types.CreatorKeys']] = None,
        optional: Optional[Iterable['types.CreatorKeys']] = None,
        relations: Optional[Mapping['types.CreatorRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.CreatorKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _Creator_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _Creator_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _Creator_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _Creator_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True


            if relations:
                raise ValueError('Model: "Creator" has no relational fields.')
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid Creator / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'Creator',
            }
        )
        _created_partial_types.add(name)


class StoreAgent(bases.BaseStoreAgent):
    """Represents a StoreAgent record"""

    listing_id: _str
    storeListingVersionId: _str
    updated_at: datetime.datetime
    slug: _str
    agent_name: _str
    agent_video: Optional[_str] = None
    agent_image: List[_str]
    featured: _bool
    creator_username: _str
    creator_avatar: _str
    sub_heading: _str
    description: _str
    categories: List[_str]
    runs: _int
    rating: _float
    versions: List[_str]


    @field_validator('agent_image', 'categories', 'versions', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.StoreAgentKeys']] = None,
        exclude: Optional[Iterable['types.StoreAgentKeys']] = None,
        required: Optional[Iterable['types.StoreAgentKeys']] = None,
        optional: Optional[Iterable['types.StoreAgentKeys']] = None,
        relations: Optional[Mapping['types.StoreAgentRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.StoreAgentKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _StoreAgent_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _StoreAgent_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _StoreAgent_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _StoreAgent_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True


            if relations:
                raise ValueError('Model: "StoreAgent" has no relational fields.')
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid StoreAgent / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'StoreAgent',
            }
        )
        _created_partial_types.add(name)


class StoreSubmission(bases.BaseStoreSubmission):
    """Represents a StoreSubmission record"""

    listing_id: _str
    user_id: _str
    slug: _str
    name: _str
    sub_heading: _str
    description: _str
    image_urls: List[_str]
    date_submitted: datetime.datetime
    status: 'enums.SubmissionStatus'
    runs: _int
    rating: _float
    agent_id: _str
    agent_version: _int
    store_listing_version_id: _str
    reviewer_id: Optional[_str] = None
    review_comments: Optional[_str] = None
    internal_comments: Optional[_str] = None
    reviewed_at: Optional[datetime.datetime] = None
    changes_summary: Optional[_str] = None


    @field_validator('image_urls', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_list_fields(cls, value: object) -> object:
        # When using raw queries, some databases will return `None` for an array field that has not been set yet.
        #
        # In our case we want to use an empty list instead as that is the internal Prisma behaviour and we want
        # to use the same consistent structure between the core ORM and raw queries. For example, if we updated
        # our type definitions to include `None` for `List` fields then it would be misleading as it will only
        # ever be `None` in raw queries.
        if value is None:
            return []

        return value

    @staticmethod
    def create_partial(
        name: str,
        include: Optional[Iterable['types.StoreSubmissionKeys']] = None,
        exclude: Optional[Iterable['types.StoreSubmissionKeys']] = None,
        required: Optional[Iterable['types.StoreSubmissionKeys']] = None,
        optional: Optional[Iterable['types.StoreSubmissionKeys']] = None,
        relations: Optional[Mapping['types.StoreSubmissionRelationalFieldKeys', str]] = None,
        exclude_relational_fields: bool = False,
    ) -> None:
        if not os.environ.get('PRISMA_GENERATOR_INVOCATION'):
            raise RuntimeError(
                'Attempted to create a partial type outside of client generation.'
            )

        if name in _created_partial_types:
            raise ValueError(f'Partial type "{name}" has already been created.')

        if include is not None:
            if exclude is not None:
                raise TypeError('Exclude and include are mutually exclusive.')
            if exclude_relational_fields is True:
                raise TypeError('Include and exclude_relational_fields=True are mutually exclusive.')

        if required and optional:
            shared = set(required) & set(optional)
            if shared:
                raise ValueError(f'Cannot make the same field(s) required and optional {shared}')

        if exclude_relational_fields and relations:
            raise ValueError(
                'exclude_relational_fields and relations are mutually exclusive'
            )

        fields: Dict['types.StoreSubmissionKeys', PartialModelField] = OrderedDict()

        try:
            if include:
                for field in include:
                    fields[field] = _StoreSubmission_fields[field].copy()
            elif exclude:
                for field in exclude:
                    if field not in _StoreSubmission_fields:
                        raise KeyError(field)

                fields = {
                    key: data.copy()
                    for key, data in _StoreSubmission_fields.items()
                    if key not in exclude
                }
            else:
                fields = {
                    key: data.copy()
                    for key, data in _StoreSubmission_fields.items()
                }

            if required:
                for field in required:
                    fields[field]['optional'] = False

            if optional:
                for field in optional:
                    fields[field]['optional'] = True


            if relations:
                raise ValueError('Model: "StoreSubmission" has no relational fields.')
        except KeyError as exc:
            raise ValueError(
                f'{exc.args[0]} is not a valid StoreSubmission / {name} field.'
            ) from None

        models = partial_models_ctx.get()
        models.append(
            {
                'name': name,
                'fields': cast(Mapping[str, PartialModelField], fields),
                'from_model': 'StoreSubmission',
            }
        )
        _created_partial_types.add(name)



_User_relational_fields: Set[str] = {
        'AgentGraphs',
        'AgentGraphExecutions',
        'AnalyticsDetails',
        'AnalyticsMetrics',
        'CreditTransactions',
        'AgentPresets',
        'LibraryAgents',
        'Profile',
        'UserOnboarding',
        'StoreListings',
        'StoreListingReviews',
        'StoreVersionsReviewed',
        'APIKeys',
        'IntegrationWebhooks',
        'NotificationBatches',
    }
_User_fields: Dict['types.UserKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('email', {
            'name': 'email',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('emailVerified', {
            'name': 'emailVerified',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('metadata', {
            'name': 'metadata',
            'is_list': False,
            'optional': False,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('integrations', {
            'name': 'integrations',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('stripeCustomerId', {
            'name': 'stripeCustomerId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('topUpConfig', {
            'name': 'topUpConfig',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('maxEmailsPerDay', {
            'name': 'maxEmailsPerDay',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnAgentRun', {
            'name': 'notifyOnAgentRun',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnZeroBalance', {
            'name': 'notifyOnZeroBalance',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnLowBalance', {
            'name': 'notifyOnLowBalance',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnBlockExecutionFailed', {
            'name': 'notifyOnBlockExecutionFailed',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnContinuousAgentError', {
            'name': 'notifyOnContinuousAgentError',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnDailySummary', {
            'name': 'notifyOnDailySummary',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnWeeklySummary', {
            'name': 'notifyOnWeeklySummary',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notifyOnMonthlySummary', {
            'name': 'notifyOnMonthlySummary',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraphs', {
            'name': 'AgentGraphs',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentGraph\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('AgentGraphExecutions', {
            'name': 'AgentGraphExecutions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentGraphExecution\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('AnalyticsDetails', {
            'name': 'AnalyticsDetails',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AnalyticsDetails\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('AnalyticsMetrics', {
            'name': 'AnalyticsMetrics',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AnalyticsMetrics\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('CreditTransactions', {
            'name': 'CreditTransactions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.CreditTransaction\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('AgentPresets', {
            'name': 'AgentPresets',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentPreset\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('LibraryAgents', {
            'name': 'LibraryAgents',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.LibraryAgent\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Profile', {
            'name': 'Profile',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.Profile\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('UserOnboarding', {
            'name': 'UserOnboarding',
            'is_list': False,
            'optional': True,
            'type': 'models.UserOnboarding',
            'is_relational': True,
            'documentation': None,
        }),
        ('StoreListings', {
            'name': 'StoreListings',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListing\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('StoreListingReviews', {
            'name': 'StoreListingReviews',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListingReview\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('StoreVersionsReviewed', {
            'name': 'StoreVersionsReviewed',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListingVersion\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('APIKeys', {
            'name': 'APIKeys',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.APIKey\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('IntegrationWebhooks', {
            'name': 'IntegrationWebhooks',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.IntegrationWebhook\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('NotificationBatches', {
            'name': 'NotificationBatches',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.UserNotificationBatch\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_UserOnboarding_relational_fields: Set[str] = {
        'User',
    }
_UserOnboarding_fields: Dict['types.UserOnboardingKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('completedSteps', {
            'name': 'completedSteps',
            'is_list': True,
            'optional': False,
            'type': 'List[\'enums.OnboardingStep\']',
            'is_relational': False,
            'documentation': None,
        }),
        ('notificationDot', {
            'name': 'notificationDot',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('notified', {
            'name': 'notified',
            'is_list': True,
            'optional': False,
            'type': 'List[\'enums.OnboardingStep\']',
            'is_relational': False,
            'documentation': None,
        }),
        ('rewardedFor', {
            'name': 'rewardedFor',
            'is_list': True,
            'optional': False,
            'type': 'List[\'enums.OnboardingStep\']',
            'is_relational': False,
            'documentation': None,
        }),
        ('usageReason', {
            'name': 'usageReason',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('integrations', {
            'name': 'integrations',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('otherIntegrations', {
            'name': 'otherIntegrations',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('selectedStoreListingVersionId', {
            'name': 'selectedStoreListingVersionId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentInput', {
            'name': 'agentInput',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('onboardingAgentExecutionId', {
            'name': 'onboardingAgentExecutionId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_AgentGraph_relational_fields: Set[str] = {
        'User',
        'forkedFrom',
        'forks',
        'Nodes',
        'Executions',
        'Presets',
        'LibraryAgents',
        'StoreListings',
        'StoreListingVersions',
    }
_AgentGraph_fields: Dict['types.AgentGraphKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('version', {
            'name': 'version',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('isActive', {
            'name': 'isActive',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('forkedFromId', {
            'name': 'forkedFromId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('forkedFromVersion', {
            'name': 'forkedFromVersion',
            'is_list': False,
            'optional': True,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('forkedFrom', {
            'name': 'forkedFrom',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('forks', {
            'name': 'forks',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentGraph\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Nodes', {
            'name': 'Nodes',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNode\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Executions', {
            'name': 'Executions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentGraphExecution\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Presets', {
            'name': 'Presets',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentPreset\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('LibraryAgents', {
            'name': 'LibraryAgents',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.LibraryAgent\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('StoreListings', {
            'name': 'StoreListings',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListing\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('StoreListingVersions', {
            'name': 'StoreListingVersions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListingVersion\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_AgentPreset_relational_fields: Set[str] = {
        'User',
        'AgentGraph',
        'InputPresets',
        'Executions',
    }
_AgentPreset_fields: Dict['types.AgentPresetKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('isActive', {
            'name': 'isActive',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('agentGraphId', {
            'name': 'agentGraphId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphVersion', {
            'name': 'agentGraphVersion',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraph', {
            'name': 'AgentGraph',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('InputPresets', {
            'name': 'InputPresets',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeExecutionInputOutput\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Executions', {
            'name': 'Executions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentGraphExecution\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('isDeleted', {
            'name': 'isDeleted',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_NotificationEvent_relational_fields: Set[str] = {
        'UserNotificationBatch',
    }
_NotificationEvent_fields: Dict['types.NotificationEventKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('UserNotificationBatch', {
            'name': 'UserNotificationBatch',
            'is_list': False,
            'optional': True,
            'type': 'models.UserNotificationBatch',
            'is_relational': True,
            'documentation': None,
        }),
        ('userNotificationBatchId', {
            'name': 'userNotificationBatchId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('type', {
            'name': 'type',
            'is_list': False,
            'optional': False,
            'type': 'enums.NotificationType',
            'is_relational': False,
            'documentation': None,
        }),
        ('data', {
            'name': 'data',
            'is_list': False,
            'optional': False,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_UserNotificationBatch_relational_fields: Set[str] = {
        'User',
        'Notifications',
    }
_UserNotificationBatch_fields: Dict['types.UserNotificationBatchKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('type', {
            'name': 'type',
            'is_list': False,
            'optional': False,
            'type': 'enums.NotificationType',
            'is_relational': False,
            'documentation': None,
        }),
        ('Notifications', {
            'name': 'Notifications',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.NotificationEvent\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_LibraryAgent_relational_fields: Set[str] = {
        'User',
        'AgentGraph',
        'Creator',
    }
_LibraryAgent_fields: Dict['types.LibraryAgentKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('imageUrl', {
            'name': 'imageUrl',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphId', {
            'name': 'agentGraphId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphVersion', {
            'name': 'agentGraphVersion',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraph', {
            'name': 'AgentGraph',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('creatorId', {
            'name': 'creatorId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('Creator', {
            'name': 'Creator',
            'is_list': False,
            'optional': True,
            'type': 'models.Profile',
            'is_relational': True,
            'documentation': None,
        }),
        ('useGraphIsActiveVersion', {
            'name': 'useGraphIsActiveVersion',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('isFavorite', {
            'name': 'isFavorite',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('isCreatedByUser', {
            'name': 'isCreatedByUser',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('isArchived', {
            'name': 'isArchived',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('isDeleted', {
            'name': 'isDeleted',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_AgentNode_relational_fields: Set[str] = {
        'AgentBlock',
        'AgentGraph',
        'Input',
        'Output',
        'Webhook',
        'Executions',
    }
_AgentNode_fields: Dict['types.AgentNodeKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentBlockId', {
            'name': 'agentBlockId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentBlock', {
            'name': 'AgentBlock',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentBlock',
            'is_relational': True,
            'documentation': None,
        }),
        ('agentGraphId', {
            'name': 'agentGraphId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphVersion', {
            'name': 'agentGraphVersion',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraph', {
            'name': 'AgentGraph',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('Input', {
            'name': 'Input',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeLink\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Output', {
            'name': 'Output',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeLink\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('constantInput', {
            'name': 'constantInput',
            'is_list': False,
            'optional': False,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('webhookId', {
            'name': 'webhookId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('Webhook', {
            'name': 'Webhook',
            'is_list': False,
            'optional': True,
            'type': 'models.IntegrationWebhook',
            'is_relational': True,
            'documentation': None,
        }),
        ('metadata', {
            'name': 'metadata',
            'is_list': False,
            'optional': False,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('Executions', {
            'name': 'Executions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeExecution\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_AgentNodeLink_relational_fields: Set[str] = {
        'AgentNodeSource',
        'AgentNodeSink',
    }
_AgentNodeLink_fields: Dict['types.AgentNodeLinkKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentNodeSourceId', {
            'name': 'agentNodeSourceId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentNodeSource', {
            'name': 'AgentNodeSource',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentNode',
            'is_relational': True,
            'documentation': None,
        }),
        ('sourceName', {
            'name': 'sourceName',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentNodeSinkId', {
            'name': 'agentNodeSinkId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentNodeSink', {
            'name': 'AgentNodeSink',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentNode',
            'is_relational': True,
            'documentation': None,
        }),
        ('sinkName', {
            'name': 'sinkName',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('isStatic', {
            'name': 'isStatic',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_AgentBlock_relational_fields: Set[str] = {
        'ReferencedByAgentNode',
    }
_AgentBlock_fields: Dict['types.AgentBlockKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('inputSchema', {
            'name': 'inputSchema',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('outputSchema', {
            'name': 'outputSchema',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('ReferencedByAgentNode', {
            'name': 'ReferencedByAgentNode',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNode\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_AgentGraphExecution_relational_fields: Set[str] = {
        'AgentGraph',
        'NodeExecutions',
        'User',
        'AgentPreset',
    }
_AgentGraphExecution_fields: Dict['types.AgentGraphExecutionKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('startedAt', {
            'name': 'startedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('isDeleted', {
            'name': 'isDeleted',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('executionStatus', {
            'name': 'executionStatus',
            'is_list': False,
            'optional': False,
            'type': 'enums.AgentExecutionStatus',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphId', {
            'name': 'agentGraphId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphVersion', {
            'name': 'agentGraphVersion',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraph', {
            'name': 'AgentGraph',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('NodeExecutions', {
            'name': 'NodeExecutions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeExecution\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('stats', {
            'name': 'stats',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentPresetId', {
            'name': 'agentPresetId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentPreset', {
            'name': 'AgentPreset',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentPreset',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_AgentNodeExecution_relational_fields: Set[str] = {
        'GraphExecution',
        'Node',
        'Input',
        'Output',
    }
_AgentNodeExecution_fields: Dict['types.AgentNodeExecutionKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphExecutionId', {
            'name': 'agentGraphExecutionId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('GraphExecution', {
            'name': 'GraphExecution',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraphExecution',
            'is_relational': True,
            'documentation': None,
        }),
        ('agentNodeId', {
            'name': 'agentNodeId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('Node', {
            'name': 'Node',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentNode',
            'is_relational': True,
            'documentation': None,
        }),
        ('Input', {
            'name': 'Input',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeExecutionInputOutput\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('Output', {
            'name': 'Output',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNodeExecutionInputOutput\']',
            'is_relational': True,
            'documentation': None,
        }),
        ('executionStatus', {
            'name': 'executionStatus',
            'is_list': False,
            'optional': False,
            'type': 'enums.AgentExecutionStatus',
            'is_relational': False,
            'documentation': None,
        }),
        ('executionData', {
            'name': 'executionData',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('addedTime', {
            'name': 'addedTime',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('queuedTime', {
            'name': 'queuedTime',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('startedTime', {
            'name': 'startedTime',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('endedTime', {
            'name': 'endedTime',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('stats', {
            'name': 'stats',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_AgentNodeExecutionInputOutput_relational_fields: Set[str] = {
        'ReferencedByInputExec',
        'ReferencedByOutputExec',
        'AgentPreset',
    }
_AgentNodeExecutionInputOutput_fields: Dict['types.AgentNodeExecutionInputOutputKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('data', {
            'name': 'data',
            'is_list': False,
            'optional': False,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('time', {
            'name': 'time',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('referencedByInputExecId', {
            'name': 'referencedByInputExecId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('ReferencedByInputExec', {
            'name': 'ReferencedByInputExec',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentNodeExecution',
            'is_relational': True,
            'documentation': None,
        }),
        ('referencedByOutputExecId', {
            'name': 'referencedByOutputExecId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('ReferencedByOutputExec', {
            'name': 'ReferencedByOutputExec',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentNodeExecution',
            'is_relational': True,
            'documentation': None,
        }),
        ('agentPresetId', {
            'name': 'agentPresetId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentPreset', {
            'name': 'AgentPreset',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentPreset',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_IntegrationWebhook_relational_fields: Set[str] = {
        'User',
        'AgentNodes',
    }
_IntegrationWebhook_fields: Dict['types.IntegrationWebhookKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('provider', {
            'name': 'provider',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('credentialsId', {
            'name': 'credentialsId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('webhookType', {
            'name': 'webhookType',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('resource', {
            'name': 'resource',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('events', {
            'name': 'events',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('config', {
            'name': 'config',
            'is_list': False,
            'optional': False,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('secret', {
            'name': 'secret',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('providerWebhookId', {
            'name': 'providerWebhookId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentNodes', {
            'name': 'AgentNodes',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.AgentNode\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_AnalyticsDetails_relational_fields: Set[str] = {
        'User',
    }
_AnalyticsDetails_fields: Dict['types.AnalyticsDetailsKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('type', {
            'name': 'type',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('data', {
            'name': 'data',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
        ('dataIndex', {
            'name': 'dataIndex',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_AnalyticsMetrics_relational_fields: Set[str] = {
        'User',
    }
_AnalyticsMetrics_fields: Dict['types.AnalyticsMetricsKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('analyticMetric', {
            'name': 'analyticMetric',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('value', {
            'name': 'value',
            'is_list': False,
            'optional': False,
            'type': '_float',
            'is_relational': False,
            'documentation': None,
        }),
        ('dataString', {
            'name': 'dataString',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_CreditTransaction_relational_fields: Set[str] = {
        'User',
    }
_CreditTransaction_fields: Dict['types.CreditTransactionKeys', PartialModelField] = OrderedDict(
    [
        ('transactionKey', {
            'name': 'transactionKey',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('amount', {
            'name': 'amount',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('type', {
            'name': 'type',
            'is_list': False,
            'optional': False,
            'type': 'enums.CreditTransactionType',
            'is_relational': False,
            'documentation': None,
        }),
        ('runningBalance', {
            'name': 'runningBalance',
            'is_list': False,
            'optional': True,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('isActive', {
            'name': 'isActive',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('metadata', {
            'name': 'metadata',
            'is_list': False,
            'optional': True,
            'type': 'fields.Json',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_CreditRefundRequest_relational_fields: Set[str] = set()  # pyright: ignore[reportUnusedVariable]
_CreditRefundRequest_fields: Dict['types.CreditRefundRequestKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('transactionKey', {
            'name': 'transactionKey',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('amount', {
            'name': 'amount',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('reason', {
            'name': 'reason',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('result', {
            'name': 'result',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('status', {
            'name': 'status',
            'is_list': False,
            'optional': False,
            'type': 'enums.CreditRefundRequestStatus',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_Profile_relational_fields: Set[str] = {
        'User',
        'LibraryAgents',
    }
_Profile_fields: Dict['types.ProfileKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('username', {
            'name': 'username',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('links', {
            'name': 'links',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('avatarUrl', {
            'name': 'avatarUrl',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('isFeatured', {
            'name': 'isFeatured',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('LibraryAgents', {
            'name': 'LibraryAgents',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.LibraryAgent\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_StoreListing_relational_fields: Set[str] = {
        'ActiveVersion',
        'AgentGraph',
        'OwningUser',
        'Versions',
    }
_StoreListing_fields: Dict['types.StoreListingKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('isDeleted', {
            'name': 'isDeleted',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('hasApprovedVersion', {
            'name': 'hasApprovedVersion',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('slug', {
            'name': 'slug',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('activeVersionId', {
            'name': 'activeVersionId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('ActiveVersion', {
            'name': 'ActiveVersion',
            'is_list': False,
            'optional': True,
            'type': 'models.StoreListingVersion',
            'is_relational': True,
            'documentation': None,
        }),
        ('agentGraphId', {
            'name': 'agentGraphId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphVersion', {
            'name': 'agentGraphVersion',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraph', {
            'name': 'AgentGraph',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('owningUserId', {
            'name': 'owningUserId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('OwningUser', {
            'name': 'OwningUser',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('Versions', {
            'name': 'Versions',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListingVersion\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_StoreListingVersion_relational_fields: Set[str] = {
        'AgentGraph',
        'StoreListing',
        'ActiveFor',
        'Reviewer',
        'Reviews',
    }
_StoreListingVersion_fields: Dict['types.StoreListingVersionKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('version', {
            'name': 'version',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphId', {
            'name': 'agentGraphId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agentGraphVersion', {
            'name': 'agentGraphVersion',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('AgentGraph', {
            'name': 'AgentGraph',
            'is_list': False,
            'optional': True,
            'type': 'models.AgentGraph',
            'is_relational': True,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('subHeading', {
            'name': 'subHeading',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('videoUrl', {
            'name': 'videoUrl',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('imageUrls', {
            'name': 'imageUrls',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('categories', {
            'name': 'categories',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('isFeatured', {
            'name': 'isFeatured',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('isDeleted', {
            'name': 'isDeleted',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('isAvailable', {
            'name': 'isAvailable',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('submissionStatus', {
            'name': 'submissionStatus',
            'is_list': False,
            'optional': False,
            'type': 'enums.SubmissionStatus',
            'is_relational': False,
            'documentation': None,
        }),
        ('submittedAt', {
            'name': 'submittedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('storeListingId', {
            'name': 'storeListingId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('StoreListing', {
            'name': 'StoreListing',
            'is_list': False,
            'optional': True,
            'type': 'models.StoreListing',
            'is_relational': True,
            'documentation': None,
        }),
        ('ActiveFor', {
            'name': 'ActiveFor',
            'is_list': False,
            'optional': True,
            'type': 'models.StoreListing',
            'is_relational': True,
            'documentation': None,
        }),
        ('changesSummary', {
            'name': 'changesSummary',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('reviewerId', {
            'name': 'reviewerId',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('Reviewer', {
            'name': 'Reviewer',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('internalComments', {
            'name': 'internalComments',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('reviewComments', {
            'name': 'reviewComments',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('reviewedAt', {
            'name': 'reviewedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('Reviews', {
            'name': 'Reviews',
            'is_list': True,
            'optional': True,
            'type': 'List[\'models.StoreListingReview\']',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_StoreListingReview_relational_fields: Set[str] = {
        'StoreListingVersion',
        'ReviewByUser',
    }
_StoreListingReview_fields: Dict['types.StoreListingReviewKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('updatedAt', {
            'name': 'updatedAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('storeListingVersionId', {
            'name': 'storeListingVersionId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('StoreListingVersion', {
            'name': 'StoreListingVersion',
            'is_list': False,
            'optional': True,
            'type': 'models.StoreListingVersion',
            'is_relational': True,
            'documentation': None,
        }),
        ('reviewByUserId', {
            'name': 'reviewByUserId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('ReviewByUser', {
            'name': 'ReviewByUser',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
        ('score', {
            'name': 'score',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('comments', {
            'name': 'comments',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_APIKey_relational_fields: Set[str] = {
        'User',
    }
_APIKey_fields: Dict['types.APIKeyKeys', PartialModelField] = OrderedDict(
    [
        ('id', {
            'name': 'id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('prefix', {
            'name': 'prefix',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('postfix', {
            'name': 'postfix',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('key', {
            'name': 'key',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('status', {
            'name': 'status',
            'is_list': False,
            'optional': False,
            'type': 'enums.APIKeyStatus',
            'is_relational': False,
            'documentation': None,
        }),
        ('permissions', {
            'name': 'permissions',
            'is_list': True,
            'optional': False,
            'type': 'List[\'enums.APIKeyPermission\']',
            'is_relational': False,
            'documentation': None,
        }),
        ('createdAt', {
            'name': 'createdAt',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('lastUsedAt', {
            'name': 'lastUsedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('revokedAt', {
            'name': 'revokedAt',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('userId', {
            'name': 'userId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('User', {
            'name': 'User',
            'is_list': False,
            'optional': True,
            'type': 'models.User',
            'is_relational': True,
            'documentation': None,
        }),
    ],
)

_Creator_relational_fields: Set[str] = set()  # pyright: ignore[reportUnusedVariable]
_Creator_fields: Dict['types.CreatorKeys', PartialModelField] = OrderedDict(
    [
        ('username', {
            'name': 'username',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('avatar_url', {
            'name': 'avatar_url',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('top_categories', {
            'name': 'top_categories',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('links', {
            'name': 'links',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('num_agents', {
            'name': 'num_agents',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_rating', {
            'name': 'agent_rating',
            'is_list': False,
            'optional': False,
            'type': '_float',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_runs', {
            'name': 'agent_runs',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('is_featured', {
            'name': 'is_featured',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_StoreAgent_relational_fields: Set[str] = set()  # pyright: ignore[reportUnusedVariable]
_StoreAgent_fields: Dict['types.StoreAgentKeys', PartialModelField] = OrderedDict(
    [
        ('listing_id', {
            'name': 'listing_id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('storeListingVersionId', {
            'name': 'storeListingVersionId',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('updated_at', {
            'name': 'updated_at',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('slug', {
            'name': 'slug',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_name', {
            'name': 'agent_name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_video', {
            'name': 'agent_video',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_image', {
            'name': 'agent_image',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('featured', {
            'name': 'featured',
            'is_list': False,
            'optional': False,
            'type': '_bool',
            'is_relational': False,
            'documentation': None,
        }),
        ('creator_username', {
            'name': 'creator_username',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('creator_avatar', {
            'name': 'creator_avatar',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('sub_heading', {
            'name': 'sub_heading',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('categories', {
            'name': 'categories',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('runs', {
            'name': 'runs',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('rating', {
            'name': 'rating',
            'is_list': False,
            'optional': False,
            'type': '_float',
            'is_relational': False,
            'documentation': None,
        }),
        ('versions', {
            'name': 'versions',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)

_StoreSubmission_relational_fields: Set[str] = set()  # pyright: ignore[reportUnusedVariable]
_StoreSubmission_fields: Dict['types.StoreSubmissionKeys', PartialModelField] = OrderedDict(
    [
        ('listing_id', {
            'name': 'listing_id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('user_id', {
            'name': 'user_id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('slug', {
            'name': 'slug',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('name', {
            'name': 'name',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('sub_heading', {
            'name': 'sub_heading',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('description', {
            'name': 'description',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('image_urls', {
            'name': 'image_urls',
            'is_list': True,
            'optional': False,
            'type': 'List[_str]',
            'is_relational': False,
            'documentation': None,
        }),
        ('date_submitted', {
            'name': 'date_submitted',
            'is_list': False,
            'optional': False,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('status', {
            'name': 'status',
            'is_list': False,
            'optional': False,
            'type': 'enums.SubmissionStatus',
            'is_relational': False,
            'documentation': None,
        }),
        ('runs', {
            'name': 'runs',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('rating', {
            'name': 'rating',
            'is_list': False,
            'optional': False,
            'type': '_float',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_id', {
            'name': 'agent_id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('agent_version', {
            'name': 'agent_version',
            'is_list': False,
            'optional': False,
            'type': '_int',
            'is_relational': False,
            'documentation': None,
        }),
        ('store_listing_version_id', {
            'name': 'store_listing_version_id',
            'is_list': False,
            'optional': False,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('reviewer_id', {
            'name': 'reviewer_id',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('review_comments', {
            'name': 'review_comments',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('internal_comments', {
            'name': 'internal_comments',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
        ('reviewed_at', {
            'name': 'reviewed_at',
            'is_list': False,
            'optional': True,
            'type': 'datetime.datetime',
            'is_relational': False,
            'documentation': None,
        }),
        ('changes_summary', {
            'name': 'changes_summary',
            'is_list': False,
            'optional': True,
            'type': '_str',
            'is_relational': False,
            'documentation': None,
        }),
    ],
)



# we have to import ourselves as relation types are namespaced to models
# e.g. models.Post
from . import models, actions

# required to support relationships between models
model_rebuild(User)
model_rebuild(UserOnboarding)
model_rebuild(AgentGraph)
model_rebuild(AgentPreset)
model_rebuild(NotificationEvent)
model_rebuild(UserNotificationBatch)
model_rebuild(LibraryAgent)
model_rebuild(AgentNode)
model_rebuild(AgentNodeLink)
model_rebuild(AgentBlock)
model_rebuild(AgentGraphExecution)
model_rebuild(AgentNodeExecution)
model_rebuild(AgentNodeExecutionInputOutput)
model_rebuild(IntegrationWebhook)
model_rebuild(AnalyticsDetails)
model_rebuild(AnalyticsMetrics)
model_rebuild(CreditTransaction)
model_rebuild(CreditRefundRequest)
model_rebuild(Profile)
model_rebuild(StoreListing)
model_rebuild(StoreListingVersion)
model_rebuild(StoreListingReview)
model_rebuild(APIKey)
model_rebuild(Creator)
model_rebuild(StoreAgent)
model_rebuild(StoreSubmission)
