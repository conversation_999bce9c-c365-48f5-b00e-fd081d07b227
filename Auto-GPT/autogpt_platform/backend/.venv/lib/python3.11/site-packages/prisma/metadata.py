from __future__ import annotations
# -*- coding: utf-8 -*-
# code generated by Prisma. DO NOT EDIT.
# fmt: off
# -- template metadata.py.jinja --


PRISMA_MODELS: set[str] = {
    'User',
    'UserOnboarding',
    'AgentGraph',
    'AgentPreset',
    'NotificationEvent',
    'UserNotificationBatch',
    'LibraryAgent',
    'AgentNode',
    'AgentNodeLink',
    'AgentBlock',
    'AgentGraphExecution',
    'AgentNodeExecution',
    'AgentNodeExecutionInputOutput',
    'IntegrationWebhook',
    'AnalyticsDetails',
    'AnalyticsMetrics',
    'CreditTransaction',
    'CreditRefundRequest',
    'Profile',
    'StoreListing',
    'StoreListingVersion',
    'StoreListingReview',
    'APIKey',
    'Creator',
    'StoreAgent',
    'StoreSubmission',
}

RELATIONAL_FIELD_MAPPINGS: dict[str, dict[str, str]] = {
    'User': {
        'AgentGraphs': 'AgentGraph',
        'AgentGraphExecutions': 'AgentGraphExecution',
        'AnalyticsDetails': 'AnalyticsDetails',
        'AnalyticsMetrics': 'AnalyticsMetrics',
        'CreditTransactions': 'CreditTransaction',
        'AgentPresets': 'AgentPreset',
        'LibraryAgents': 'LibraryAgent',
        'Profile': 'Profile',
        'UserOnboarding': 'UserOnboarding',
        'StoreListings': 'StoreListing',
        'StoreListingReviews': 'StoreListingReview',
        'StoreVersionsReviewed': 'StoreListingVersion',
        'APIKeys': 'APIKey',
        'IntegrationWebhooks': 'IntegrationWebhook',
        'NotificationBatches': 'UserNotificationBatch',
    },
    'UserOnboarding': {
        'User': 'User',
    },
    'AgentGraph': {
        'User': 'User',
        'forkedFrom': 'AgentGraph',
        'forks': 'AgentGraph',
        'Nodes': 'AgentNode',
        'Executions': 'AgentGraphExecution',
        'Presets': 'AgentPreset',
        'LibraryAgents': 'LibraryAgent',
        'StoreListings': 'StoreListing',
        'StoreListingVersions': 'StoreListingVersion',
    },
    'AgentPreset': {
        'User': 'User',
        'AgentGraph': 'AgentGraph',
        'InputPresets': 'AgentNodeExecutionInputOutput',
        'Executions': 'AgentGraphExecution',
    },
    'NotificationEvent': {
        'UserNotificationBatch': 'UserNotificationBatch',
    },
    'UserNotificationBatch': {
        'User': 'User',
        'Notifications': 'NotificationEvent',
    },
    'LibraryAgent': {
        'User': 'User',
        'AgentGraph': 'AgentGraph',
        'Creator': 'Profile',
    },
    'AgentNode': {
        'AgentBlock': 'AgentBlock',
        'AgentGraph': 'AgentGraph',
        'Input': 'AgentNodeLink',
        'Output': 'AgentNodeLink',
        'Webhook': 'IntegrationWebhook',
        'Executions': 'AgentNodeExecution',
    },
    'AgentNodeLink': {
        'AgentNodeSource': 'AgentNode',
        'AgentNodeSink': 'AgentNode',
    },
    'AgentBlock': {
        'ReferencedByAgentNode': 'AgentNode',
    },
    'AgentGraphExecution': {
        'AgentGraph': 'AgentGraph',
        'NodeExecutions': 'AgentNodeExecution',
        'User': 'User',
        'AgentPreset': 'AgentPreset',
    },
    'AgentNodeExecution': {
        'GraphExecution': 'AgentGraphExecution',
        'Node': 'AgentNode',
        'Input': 'AgentNodeExecutionInputOutput',
        'Output': 'AgentNodeExecutionInputOutput',
    },
    'AgentNodeExecutionInputOutput': {
        'ReferencedByInputExec': 'AgentNodeExecution',
        'ReferencedByOutputExec': 'AgentNodeExecution',
        'AgentPreset': 'AgentPreset',
    },
    'IntegrationWebhook': {
        'User': 'User',
        'AgentNodes': 'AgentNode',
    },
    'AnalyticsDetails': {
        'User': 'User',
    },
    'AnalyticsMetrics': {
        'User': 'User',
    },
    'CreditTransaction': {
        'User': 'User',
    },
    'CreditRefundRequest': {
    },
    'Profile': {
        'User': 'User',
        'LibraryAgents': 'LibraryAgent',
    },
    'StoreListing': {
        'ActiveVersion': 'StoreListingVersion',
        'AgentGraph': 'AgentGraph',
        'OwningUser': 'User',
        'Versions': 'StoreListingVersion',
    },
    'StoreListingVersion': {
        'AgentGraph': 'AgentGraph',
        'StoreListing': 'StoreListing',
        'ActiveFor': 'StoreListing',
        'Reviewer': 'User',
        'Reviews': 'StoreListingReview',
    },
    'StoreListingReview': {
        'StoreListingVersion': 'StoreListingVersion',
        'ReviewByUser': 'User',
    },
    'APIKey': {
        'User': 'User',
    },
    'Creator': {
    },
    'StoreAgent': {
    },
    'StoreSubmission': {
    },
}

# fmt: on